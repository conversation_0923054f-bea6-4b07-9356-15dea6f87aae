package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.base.swagger.api.rpc.annotation.RpcModel;

import java.io.Serializable;
import java.util.List;

@RpcModel("订单详情关联-验收附件信息")
public class AcceptAttachmentDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("附件信息")
    private AttachmentDTO attachment;

    @RpcModelProperty("关联的订单详情ID集合")
    private List<Integer> orderDetailIdList;

    public AttachmentDTO getAttachment() {
        return attachment;
    }

    public void setAttachment(AttachmentDTO attachment) {
        this.attachment = attachment;
    }

    public List<Integer> getOrderDetailIdList() {
        return orderDetailIdList;
    }

    public void setOrderDetailIdList(List<Integer> orderDetailIdList) {
        this.orderDetailIdList = orderDetailIdList;
    }
}