package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 机构配置参数dto
 */
public class OrgConfigRequestDTO implements Serializable {
    private static final long serialVersionUID = 6899938423219648721L;

    /**
     * 机构code
     */
    private String orgCode;

    /**
     * 配置编码, configCodes上线后删掉此变量
     */
    private String configCode;

    /**
     * 配置编码数组
     * @return
     */
    private List<String> configCodesList;

    public List<String> getConfigCodesList() {
        return configCodesList;
    }

    public void setConfigCodesList(List<String> configCodesList) {
        this.configCodesList = configCodesList;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getConfigCode() {
        return configCode;
    }

    public void setConfigCode(String configCode) {
        this.configCode = configCode;
    }


    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrgConfigRequestDTO{");
        sb.append("orgCode='").append(orgCode).append('\'');
        sb.append(", configCode='").append(configCode).append('\'');
        sb.append(", configCodesList=").append(configCodesList);
        sb.append('}');
        return sb.toString();
    }
}
