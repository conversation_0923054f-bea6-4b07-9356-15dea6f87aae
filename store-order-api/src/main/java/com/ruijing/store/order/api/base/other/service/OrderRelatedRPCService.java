package com.ruijing.store.order.api.base.other.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.orderapprovallog.dto.OrderApprovalRequestDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterCommonReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.TimeOutOrderParamsDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.other.dto.*;

import java.util.List;

/**
 * @description: 订单相关的其他服务
 * @author: zhuk
 * @create: 2019-10-12 16:43
 **/
@RpcApi(value = "订单相关的其他业务线专用RPC服务")
public interface OrderRelatedRPCService {

    /**
     * 查询订单 关联发票信息
     * @param orderIdList
     * @return
     */
    RemoteResponse<List<RefInvoiceOrderDTO>> findRefInvoiceOrderByRefId(List<String> orderIdList);

    /**
     * 根据发票id集合查询 发票信息
     * @param invoiceIdList
     * @return
     */
    RemoteResponse<List<RefInvoiceOrderDTO>> findRefInvoiceOrderByInvoiceIds(List<Integer> invoiceIdList);

    /**
     * 根据订单id 获取 对应的 测试分析费 和实验耗材费
     * @param orderMasterCommonReqDTO  orderMasterIds 不能为空 且 大小不能超过 300
     * @return
     */
    RemoteResponse<List<OrderCategoryAmountDTO> > getFeeCategoryAmount(OrderMasterCommonReqDTO orderMasterCommonReqDTO);

    /**
     * 根据订单id 获取 对应的  服务，耗材，试剂，动物 金额
     * @param orderMasterCommonReqDTO orderMasterIds 不能为空 且 大小不能超过 300
     * @return
     */
    RemoteResponse<List<OrderCategoryAmountDTO> > getCategoryTypeAmount(OrderMasterCommonReqDTO orderMasterCommonReqDTO);

    /**
     * 新增订单 审批日志
     * @param orderApprovalLogDTO
     * @return
     */
    RemoteResponse insertOrderApproval(OrderApprovalLogDTO orderApprovalLogDTO);

    /**
     * 根据订单id列表查询 订单经费卡关联信息
     * @param orderIdList size不能超过100
     * @return
     */
    RemoteResponse<List<RefFundcardOrderDTO>> findRefFundCardOrderByOrderIdList(List<Integer> orderIdList);

    /**
     * 根据采购单id列表查询 订单经费卡关联信息
     * @param applicationIdList size不能超过100
     * @return
     */
    RemoteResponse<List<RefFundcardOrderDTO>> findRefFundCardOrderByApplicationIdList(List<Integer> applicationIdList);

    /**
     * 添加一条经费卡关联信息记录
     * @param refFundcardOrderDTO
     * @return
     */
    RemoteResponse insertRefFundcardOrder(RefFundcardOrderDTO refFundcardOrderDTO);

    /**
     * 更新经费卡信息
     * @param refFundcardOrderDTO
     * @return
     */
    RemoteResponse updateRefFundcardOrderByOrderId(RefFundcardOrderDTO refFundcardOrderDTO);

    /**
     * 通过订单id物理删除经费卡关联信息
     * @param orderId
     * @return
     */
    RemoteResponse deleteRefFundcardOrderByOrderId(String orderId);

    /**
     * 通过订单id物理删除经费卡关联信息, orderIdList必填, length <= 200
     * @param request   入参
     * @return          删除数量
     */
    RemoteResponse<Integer> deleteRefFundCardByOrderIds(OrderBasicParamDTO request);

    /**
     * 批量删除订单绑卡记录
     * @param request   orderIdList必填
     * @return          删除数量，单次最大支持删除100条
     */
    RemoteResponse<Integer> deleteRefFundCardByIdList(List<RefFundcardOrderDTO> request);

    /**
     * 插入 第三方回调信息记录
     * @param infoDTO
     * @return
     */
    RemoteResponse insertCallbackInfo(ThirdPartyCallbackInfoDTO infoDTO);

    /**
     * 更新 第三方回调信息记录
     * @param infoDTO
     * @return
     */
    RemoteResponse updateCallbackInfoById(ThirdPartyCallbackInfoDTO infoDTO);

    /**
     * 通过采购单id 和 供应商id 批量查询记录
     * @param orderRemarkDTOList
     * @return
     */
    RemoteResponse<List<OrderRemarkDTO>> findOrderRemarkByPrimaryKeys(List<OrderRemarkDTO> orderRemarkDTOList);

    /**
     * 保存订单备注记录
     * @param   request 入参
     * @return          是否成功
     */
    RemoteResponse<Boolean> saveOrderRemark(OrderRemarkDTO request);

    /**
     * 恢复订单待结算状态, 入参是orgCode 和 被驳回的订单id数组
     * @param request 结算中订单复原待结算的入参
     * @return
     */
    RemoteResponse revertOrderStatement(RevertStatementRequestDTO request);

    /**
     * 发起结算成功，回调修改订单信息，并同步待结算中间表
     * @param orderStatementRequestDTOList 订单结算模型对象
     * @return
     */
    RemoteResponse statementUpdateOrder(List<OrderStatementRequestDTO> orderStatementRequestDTOList);

    /**
     * 订单入库成功，回调修改订单信息，并同步待结算中间表
     * @param param 订单出入库入参
     * @return
     */
    RemoteResponse warehouseUpdateOrder(InventoryOrderRequestDTO param);

    /**
     * 用于同步订单结算状态，状态不一致才修改，防止刷新表更新时间
     * 批量条数小于等于100条
     *
     * @param syncStatementStatusDTOS 批量更新结算状态DTO（订单id、结算状态）
     * @return 返回更新成功条数； 如果返回为0，则说明当前状态已是入参所要更新的状态
     * @throws IllegalArgumentException 非法入参异常
     */
    RemoteResponse<Integer> batchUpdateStatementStatus(List<SyncStatementStatusDTO> syncStatementStatusDTOS);

    /**
     * 通用换卡接口
     * @param request
     * @return
     */
    RemoteResponse saveFundCardCommon(OrderChangeCommonDTO request);

    /**
     * 查询机构配置信息
     * @param param
     * @return
     */
    RemoteResponse<OrgConfigResponseDTO> findSysConfigByOrgCode(OrgConfigRequestDTO param);

    /**
     * 查询机构配置信息,批量查询
     * @param param
     * @return
     */
    RemoteResponse<List<OrgConfigResponseDTO>> findSysConfigListByOrgCode(OrgConfigRequestDTO param);

    /**
     * 保存修改经费卡缓存的信息
     * @param fundCardList
     * @return
     */
    RemoteResponse saveFundCardCaches(List<OrderFundCardCacheRequestDTO> fundCardList, String orgCode);


    RemoteResponse saveFundCardCache(List<OrderFundCardCacheRequestDTO> fundCardList);

    /**
     * 通过pi工号查询经费卡, 目前只有暨大在用
     * @param request
     * @return
     */
    RemoteResponse<List<OrderFundCardResponseDTO>> findFundCardByDepartmentId(OrderFundCardRequestDTO request);

    /**
     * 查询订单换卡缓存rpc服务，目前暨大用
     * @param param
     * @return
     */
    RemoteResponse<OrderFundCardResponseDTO> findFundCardCacheByOrderId(OrderBasicParamDTO param);

    /**
     * 查询detail tag
     * @return
     */
    RemoteResponse<List<RefOrderDetailTagResponseDTO>> findRefOrderDetailTag(RefOrderDetailTagRequestDTO param);

    /**
     * 根据经费卡id集合查订单id集合
     * @param cardIds
     * @return
     */
    RemoteResponse<List<String>> findOrderByCardIds(List<String> cardIds);

    /**
     * 获取采购或者订单 于经费卡条数
     * @return
     */
    RemoteResponse<Integer> findRefFundCardOrderCount(FundCardAndOrderQueryDTO orderQueryDTO);

    /**
     * 根据 applicationId更新绑卡记录
     * @param refFundcardOrderDTO
     * @return
     */
    RemoteResponse<Integer> updateRefFundcardOrderByApplicationId(RefFundcardOrderDTO refFundcardOrderDTO);

    /**
     * 根据 bid 更新绑卡记录
     * @param refFundcardOrderDTO
     * @return
     */
    RemoteResponse<Integer> updateRefFundcardOrderByBid(RefFundcardOrderDTO refFundcardOrderDTO);

    /**
     * 查询超时未收货/未结算的课题组 rpc 接口
     * 目前只有新竞价在使用
     * @param params
     * @return
     */
    RemoteResponse<List<TimeoutStatisticsDTO>> findTimeOutDepartmentByOrgIdAndDepartmentId(TimeOutOrderParamsDTO params);

    /**
     * 条件查询审批日志
     * @param request
     * @return
     */
    RemoteResponse<List<OrderApprovalLogDTO>> findByOrderIdListAndStatus(OrderApprovalRequestDTO request);

    /**
     * 供应商专用接口
     * 更新第三方对接平台的订单状态
     * 目前有更新 华农的基里平台的订单状态
     * @param request
     * @return
     */
    @RpcMethod(value = "供应商专用接口, 更新第三方对接平台的订单状态(华农)")
    RemoteResponse<Boolean> updateThirdPlatformOrderForSupplier(UpdateOrderParamDTO request);

    /**
     * 根据订单号查询华农经费信息
     * @return
     */
    @RpcMethod(value = "根据订单号查询华农经费信息")
    RemoteResponse<List<HuaNongFundInfoDTO>> findByFundInfoByOrderNoList(OrderMasterCommonReqDTO request);

    /**
     * 根据订单id数组查询订单备案信息
     * @return
     */
    RemoteResponse<List<OrderConfirmForTheRecordDTO>> findOrderConfirmByOrderIdList(OrderMasterCommonReqDTO request);

    /**
     * @description: 检查在部门列表任一部门中是否存在订单
     * @date: 2021/4/6 14:05
     * @author: zengyanru
     * @param deptIdList
     * @return com.ruijing.fundamental.api.remote.RemoteResponse<java.lang.Boolean>
     */
    RemoteResponse<Boolean> checkDeptHasOrder(List<Integer> deptIdList);
}
