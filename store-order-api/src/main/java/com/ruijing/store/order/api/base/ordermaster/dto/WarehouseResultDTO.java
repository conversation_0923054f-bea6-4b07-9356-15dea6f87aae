package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.store.order.api.base.enums.InventoryStatusEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/10/20 14:19
 * @description 入库结果
 */
public class WarehouseResultDTO implements Serializable {

    private static final long serialVersionUID = 1261897493325671365L;

    /**
     * 入库状态
     */
    private InventoryStatusEnum inventoryStatus;

    /**
     * 入库调用过程是否成功
     */
    private Boolean warehouseSuccess;

    /**
     * 入库的错误信息
     */
    private String warehouseErrorMsg;

    public InventoryStatusEnum getInventoryStatus() {
        return inventoryStatus;
    }

    public void setInventoryStatus(InventoryStatusEnum inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
    }

    public Boolean getWarehouseSuccess() {
        return warehouseSuccess;
    }

    public void setWarehouseSuccess(Boolean warehouseSuccess) {
        this.warehouseSuccess = warehouseSuccess;
    }

    public String getWarehouseErrorMsg() {
        return warehouseErrorMsg;
    }

    public void setWarehouseErrorMsg(String warehouseErrorMsg) {
        this.warehouseErrorMsg = warehouseErrorMsg;
    }

    @Override
    public String toString() {
        return "WarehouseResultDTO{" +
                "inventoryStatus=" + inventoryStatus +
                ", warehouseSuccess=" + warehouseSuccess +
                ", warehouseErrorMsg='" + warehouseErrorMsg + '\'' +
                '}';
    }
}
