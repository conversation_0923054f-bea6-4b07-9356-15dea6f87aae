package com.ruijing.store.order.api.base.ordermaster.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2023-11-10 17:11
 * @description:
 **/
public class OrderDeliveryDTO implements Serializable {

    private static final long serialVersionUID = 6512224742986785610L;

    private Integer orderId;

    private Date deliveryDate;

    private String deliveryMan;

    private String deliveryManId;

    private String deliveryInfo;

    private String deliveryNo;

    public Integer getOrderId() {
        return orderId;
    }

    public OrderDeliveryDTO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public OrderDeliveryDTO setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
        return this;
    }

    public String getDeliveryMan() {
        return deliveryMan;
    }

    public OrderDeliveryDTO setDeliveryMan(String deliveryMan) {
        this.deliveryMan = deliveryMan;
        return this;
    }

    public String getDeliveryManId() {
        return deliveryManId;
    }

    public OrderDeliveryDTO setDeliveryManId(String deliveryManId) {
        this.deliveryManId = deliveryManId;
        return this;
    }

    public String getDeliveryInfo() {
        return deliveryInfo;
    }

    public OrderDeliveryDTO setDeliveryInfo(String deliveryInfo) {
        this.deliveryInfo = deliveryInfo;
        return this;
    }

    public String getDeliveryNo() {
        return deliveryNo;
    }

    public OrderDeliveryDTO setDeliveryNo(String deliveryNo) {
        this.deliveryNo = deliveryNo;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderDeliveryDTO.class.getSimpleName() + "[", "]")
                .add("orderId=" + orderId)
                .add("deliveryDate=" + deliveryDate)
                .add("deliveryMan='" + deliveryMan + "'")
                .add("deliveryManId='" + deliveryManId + "'")
                .add("deliveryInfo='" + deliveryInfo + "'")
                .add("deliveryNo='" + deliveryNo + "'")
                .toString();
    }
}
