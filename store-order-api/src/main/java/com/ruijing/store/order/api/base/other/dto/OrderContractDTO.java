package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/7/15 11:42
 * @description
 */
@RpcModel("订单合同传输体")
public class OrderContractDTO implements Serializable {

    private static final long serialVersionUID = -7894261583392009576L;
    
    /**
     * 订单id
     */
    @RpcModelProperty("订单id")
    private Integer orderId;

    /**
     * 合同文件地址
     */
    @RpcModelProperty("合同文件地址")
    private String contractLocation;

    /**
     * 合同文件名
     */
    @RpcModelProperty("合同文件名")
    private String contractName;

    /**
     * 合同编号
     */
    @RpcModelProperty("合同编号")
    private String contractNo;

    /**
     * 创建时间
     */
    @RpcModelProperty("创建时间")
    private Date createTime;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getContractLocation() {
        return contractLocation;
    }

    public void setContractLocation(String contractLocation) {
        this.contractLocation = contractLocation;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderContractDTO{");
        sb.append("orderId=").append(orderId);
        sb.append(", contractLocation='").append(contractLocation).append('\'');
        sb.append(", contractName='").append(contractName).append('\'');
        sb.append(", contractNo='").append(contractNo).append('\'');
        sb.append(", createTime=").append(createTime);
        sb.append('}');
        return sb.toString();
    }
}
