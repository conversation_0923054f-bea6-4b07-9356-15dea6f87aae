package com.ruijing.store.order.api.sysu.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.StringJoiner;

/**
 * @Author:zhengzhendong
 * @Date:2020/06/08 11:22
 * @Desc:订单请求实体
 */
@RpcModel("订单请求实体")
public class OrderRequestDTO implements Serializable {

    private static final long serialVersionUID = -6567242153389554462L;

    @RpcModelProperty("页码")
    private Integer pageNo = 1;

    @RpcModelProperty("页数")
    private Integer pageSize = 20;

    @RpcModelProperty("机构id")
    private Integer orgId;

    @RpcModelProperty("机构id")
    private List<Integer> orgIds;

    @RpcModelProperty("订单号/采购单号")
    private String no;

    @RpcModelProperty("订单号List")
    private List<String> orderNoList;

    @RpcModelProperty("经费卡号")
    private String fundCardNo;

    @RpcModelProperty("学院id")
    private Integer collegeId;

    @RpcModelProperty("学院id集合")
    private List<Integer> collegeIds;

    @RpcModelProperty("学院名称")
    private String collegeName;

    @RpcModelProperty("课题组id")
    private Integer departmentId;

    @RpcModelProperty("课题组id集合")
    private List<Integer> departmentIds;

    @RpcModelProperty("课题组负责人名称")
    private String departmentManagerName;

    @RpcModelProperty("发货开始时间")
    private String deliveryStartDate;

    @RpcModelProperty("发货结束时间")
    private String deliveryEndDate;

    @RpcModelProperty("商品名称/货号 搜索")
    private String productSearch;

    @RpcModelProperty("采购经办人/经费负责人/供应商 搜索")
    private String userSearch;

    @RpcModelProperty("采购人id")
    private Integer buyerId;

    @RpcModelProperty("采购人名称")
    private String buyerName;

    @RpcModelProperty("订单状态")
    private List<Integer> statusList;

    @RpcModelProperty("关联关系")
    private Boolean relateInfo;

    @RpcModelProperty("经费状态")
    private Integer fundStatus;

    @RpcModelProperty("线上单0  线下单1")
    private Integer processSpecies;

    @RpcModelProperty("过滤的订单号")
    private List<String> excludeOrderList;

    @RpcModelProperty("部门与发货时间查询")
    private List<OrderDeptDeliveryDateDTO> orderDeptDeliveryDateDtoList;

    /**
     * 找了前端让他搜了下没用到，中大那边也只是把前端的传值搞过来，感觉是没用的
     */
    @RpcModelProperty("经费负责人或订单审批授权权限")
    @Deprecated
    private Boolean cardAuthAccess;

    @RpcModelProperty("财务结算单号")
    private String financeNumber;

    @RpcModelProperty("订单状态")
    private Integer status;

    @RpcModelProperty("订单列表类型")
    private Integer listType;

    @RpcModelProperty("是否已备案,true是，false否")
    private Boolean confirm;

    /**
     * {@link com.ruijing.shop.category.api.enums.RegulatoryTypeEnum}
     */
    @RpcModelProperty("危化品标识")
    private Integer dangerousType;

    @RpcModelProperty("危化品标识集合")
    private List<Integer> dangerousTypeList;

    @RpcModelProperty("0全部，1待备案")
    private Integer confirmForTheRecord;

    @RpcModelProperty("订单开始时间")
    private String orderStartTime;

    @RpcModelProperty("订单结束时间")
    private String orderEndTime;

    @RpcModelProperty("经费卡id集合")
    private List<String> fundCardIds;

    @RpcModelProperty("用户id")
    private Integer userId;

    /**
     * {@link com.ruijing.store.apply.enums.DangerousTypeEnum}
     */
    @RpcModelProperty("商品类型")
    private Integer productType;

    @RpcModelProperty("经费卡号前缀")
    private String fundCardNoPrefix;

    @RpcModelProperty("经费卡号后缀")
    private String fundCardNoSuffix;

    @RpcModelProperty("起始金额")
    private BigDecimal priceStart;

    @RpcModelProperty("结束金额")
    private BigDecimal priceEnd;

    @RpcModelProperty("通配符类型经费卡号")
    private String fundCardNoWildCard;

    @RpcModelProperty("我的已审批用户id参数")
    private Integer myApprovalUserId;

    @RpcModelProperty("供应商id")
    private Integer suppId;

    @RpcModelProperty("排除供应商id")
    private List<Integer> excludeSuppIdList;

    @RpcModelProperty("配送城市集合")
    private List<String> cityNames;

    @RpcModelProperty("订单验收查询条件业务DTO 订单状态 + 对应订单状态的部门集合")
    private List<OrderReceiptApproveQueryDTO> orderReceiptApproveQueryDTOS;

    @RpcModelProperty("riskVerifiedStatus字段的exclude查询条件")
    private List<Integer> riskVerifiedStatusExcludeList;

    /**
     * {@link com.reagent.research.statement.api.enums.StatementStatusEnum}
     */
    @RpcModelProperty("结算状态数组")
    private List<Integer> statementStatusList;

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public List<Integer> getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(List<Integer> orgIds) {
        this.orgIds = orgIds;
    }

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public List<String> getOrderNoList() {
        return orderNoList;
    }

    public void setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
    }

    public String getFundCardNo() {
        return fundCardNo;
    }

    public void setFundCardNo(String fundCardNo) {
        this.fundCardNo = fundCardNo;
    }

    public Integer getCollegeId() {
        return collegeId;
    }

    public void setCollegeId(Integer collegeId) {
        this.collegeId = collegeId;
    }

    public List<Integer> getCollegeIds() {
        return collegeIds;
    }

    public void setCollegeIds(List<Integer> collegeIds) {
        this.collegeIds = collegeIds;
    }

    public String getCollegeName() {
        return collegeName;
    }

    public void setCollegeName(String collegeName) {
        this.collegeName = collegeName;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public List<Integer> getDepartmentIds() {
        return departmentIds;
    }

    public void setDepartmentIds(List<Integer> departmentIds) {
        this.departmentIds = departmentIds;
    }

    public String getDepartmentManagerName() {
        return departmentManagerName;
    }

    public void setDepartmentManagerName(String departmentManagerName) {
        this.departmentManagerName = departmentManagerName;
    }

    public String getDeliveryStartDate() {
        return deliveryStartDate;
    }

    public void setDeliveryStartDate(String deliveryStartDate) {
        this.deliveryStartDate = deliveryStartDate;
    }

    public String getDeliveryEndDate() {
        return deliveryEndDate;
    }

    public void setDeliveryEndDate(String deliveryEndDate) {
        this.deliveryEndDate = deliveryEndDate;
    }

    public String getProductSearch() {
        return productSearch;
    }

    public void setProductSearch(String productSearch) {
        this.productSearch = productSearch;
    }

    public String getUserSearch() {
        return userSearch;
    }

    public void setUserSearch(String userSearch) {
        this.userSearch = userSearch;
    }

    public Integer getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Integer buyerId) {
        this.buyerId = buyerId;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public Boolean getRelateInfo() {
        return relateInfo;
    }

    public void setRelateInfo(Boolean relateInfo) {
        this.relateInfo = relateInfo;
    }

    public Integer getFundStatus() {
        return fundStatus;
    }

    public void setFundStatus(Integer fundStatus) {
        this.fundStatus = fundStatus;
    }

    public Integer getProcessSpecies() {
        return processSpecies;
    }

    public void setProcessSpecies(Integer processSpecies) {
        this.processSpecies = processSpecies;
    }

    public List<String> getExcludeOrderList() {
        return excludeOrderList;
    }

    public void setExcludeOrderList(List<String> excludeOrderList) {
        this.excludeOrderList = excludeOrderList;
    }

    public List<OrderDeptDeliveryDateDTO> getOrderDeptDeliveryDateDtoList() {
        return orderDeptDeliveryDateDtoList;
    }

    public void setOrderDeptDeliveryDateDtoList(List<OrderDeptDeliveryDateDTO> orderDeptDeliveryDateDtoList) {
        this.orderDeptDeliveryDateDtoList = orderDeptDeliveryDateDtoList;
    }

    public Boolean getCardAuthAccess() {
        return cardAuthAccess;
    }

    public void setCardAuthAccess(Boolean cardAuthAccess) {
        this.cardAuthAccess = cardAuthAccess;
    }

    public String getFinanceNumber() {
        return financeNumber;
    }

    public void setFinanceNumber(String financeNumber) {
        this.financeNumber = financeNumber;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getListType() {
        return listType;
    }

    public void setListType(Integer listType) {
        this.listType = listType;
    }

    public Boolean getConfirm() {
        return confirm;
    }

    public void setConfirm(Boolean confirm) {
        this.confirm = confirm;
    }

    public Integer getDangerousType() {
        return dangerousType;
    }

    public void setDangerousType(Integer dangerousType) {
        this.dangerousType = dangerousType;
    }

    public List<Integer> getDangerousTypeList() {
        return dangerousTypeList;
    }

    public void setDangerousTypeList(List<Integer> dangerousTypeList) {
        this.dangerousTypeList = dangerousTypeList;
    }

    public Integer getConfirmForTheRecord() {
        return confirmForTheRecord;
    }

    public void setConfirmForTheRecord(Integer confirmForTheRecord) {
        this.confirmForTheRecord = confirmForTheRecord;
    }

    public String getOrderStartTime() {
        return orderStartTime;
    }

    public void setOrderStartTime(String orderStartTime) {
        this.orderStartTime = orderStartTime;
    }

    public String getOrderEndTime() {
        return orderEndTime;
    }

    public void setOrderEndTime(String orderEndTime) {
        this.orderEndTime = orderEndTime;
    }

    public List<String> getFundCardIds() {
        return fundCardIds;
    }

    public void setFundCardIds(List<String> fundCardIds) {
        this.fundCardIds = fundCardIds;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public String getFundCardNoPrefix() {
        return fundCardNoPrefix;
    }

    public void setFundCardNoPrefix(String fundCardNoPrefix) {
        this.fundCardNoPrefix = fundCardNoPrefix;
    }

    public String getFundCardNoSuffix() {
        return fundCardNoSuffix;
    }

    public void setFundCardNoSuffix(String fundCardNoSuffix) {
        this.fundCardNoSuffix = fundCardNoSuffix;
    }

    public BigDecimal getPriceStart() {
        return priceStart;
    }

    public void setPriceStart(BigDecimal priceStart) {
        this.priceStart = priceStart;
    }

    public BigDecimal getPriceEnd() {
        return priceEnd;
    }

    public void setPriceEnd(BigDecimal priceEnd) {
        this.priceEnd = priceEnd;
    }

    public String getFundCardNoWildCard() {
        return fundCardNoWildCard;
    }

    public void setFundCardNoWildCard(String fundCardNoWildCard) {
        this.fundCardNoWildCard = fundCardNoWildCard;
    }

    public Integer getMyApprovalUserId() {
        return myApprovalUserId;
    }

    public void setMyApprovalUserId(Integer myApprovalUserId) {
        this.myApprovalUserId = myApprovalUserId;
    }

    public Integer getSuppId() {
        return suppId;
    }

    public void setSuppId(Integer suppId) {
        this.suppId = suppId;
    }

    public List<String> getCityNames() {
        return cityNames;
    }

    public void setCityNames(List<String> cityNames) {
        this.cityNames = cityNames;
    }

    public List<OrderReceiptApproveQueryDTO> getOrderReceiptApproveQueryDTOS() {
        return orderReceiptApproveQueryDTOS;
    }

    public void setOrderReceiptApproveQueryDTOS(List<OrderReceiptApproveQueryDTO> orderReceiptApproveQueryDTOS) {
        this.orderReceiptApproveQueryDTOS = orderReceiptApproveQueryDTOS;
    }

    public List<Integer> getRiskVerifiedStatusExcludeList() {
        return riskVerifiedStatusExcludeList;
    }

    public void setRiskVerifiedStatusExcludeList(List<Integer> riskVerifiedStatusExcludeList) {
        this.riskVerifiedStatusExcludeList = riskVerifiedStatusExcludeList;
    }

    public List<Integer> getExcludeSuppIdList() {
        return excludeSuppIdList;
    }

    public void setExcludeSuppIdList(List<Integer> excludeSuppIdList) {
        this.excludeSuppIdList = excludeSuppIdList;
    }

    public List<Integer> getStatementStatusList() {
        return statementStatusList;
    }

    public OrderRequestDTO setStatementStatusList(List<Integer> statementStatusList) {
        this.statementStatusList = statementStatusList;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderRequestDTO.class.getSimpleName() + "[", "]")
                .add("pageNo=" + pageNo)
                .add("pageSize=" + pageSize)
                .add("orgId=" + orgId)
                .add("orgIds=" + orgIds)
                .add("no='" + no + "'")
                .add("orderNoList=" + orderNoList)
                .add("fundCardNo='" + fundCardNo + "'")
                .add("collegeId=" + collegeId)
                .add("collegeIds=" + collegeIds)
                .add("collegeName='" + collegeName + "'")
                .add("departmentId=" + departmentId)
                .add("departmentIds=" + departmentIds)
                .add("departmentManagerName='" + departmentManagerName + "'")
                .add("deliveryStartDate='" + deliveryStartDate + "'")
                .add("deliveryEndDate='" + deliveryEndDate + "'")
                .add("productSearch='" + productSearch + "'")
                .add("userSearch='" + userSearch + "'")
                .add("buyerId=" + buyerId)
                .add("buyerName='" + buyerName + "'")
                .add("statusList=" + statusList)
                .add("relateInfo=" + relateInfo)
                .add("fundStatus=" + fundStatus)
                .add("processSpecies=" + processSpecies)
                .add("excludeOrderList=" + excludeOrderList)
                .add("orderDeptDeliveryDateDtoList=" + orderDeptDeliveryDateDtoList)
                .add("cardAuthAccess=" + cardAuthAccess)
                .add("financeNumber='" + financeNumber + "'")
                .add("status=" + status)
                .add("listType=" + listType)
                .add("confirm=" + confirm)
                .add("dangerousType=" + dangerousType)
                .add("dangerousTypeList=" + dangerousTypeList)
                .add("confirmForTheRecord=" + confirmForTheRecord)
                .add("orderStartTime='" + orderStartTime + "'")
                .add("orderEndTime='" + orderEndTime + "'")
                .add("fundCardIds=" + fundCardIds)
                .add("userId=" + userId)
                .add("productType=" + productType)
                .add("fundCardNoPrefix='" + fundCardNoPrefix + "'")
                .add("fundCardNoSuffix='" + fundCardNoSuffix + "'")
                .add("priceStart=" + priceStart)
                .add("priceEnd=" + priceEnd)
                .add("fundCardNoWildCard='" + fundCardNoWildCard + "'")
                .add("myApprovalUserId=" + myApprovalUserId)
                .add("suppId=" + suppId)
                .add("excludeSuppIdList=" + excludeSuppIdList)
                .add("cityNames=" + cityNames)
                .add("orderReceiptApproveQueryDTOS=" + orderReceiptApproveQueryDTOS)
                .add("riskVerifiedStatusExcludeList=" + riskVerifiedStatusExcludeList)
                .add("statementStatusList=" + statementStatusList)
                .toString();
    }
}
