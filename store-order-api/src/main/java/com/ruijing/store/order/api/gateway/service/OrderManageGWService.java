package com.ruijing.store.order.api.gateway.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.common.OrderCommonPrintParamDTO;
import com.ruijing.store.order.api.base.other.dto.OrderCommonPrintDataDTO;
import com.ruijing.store.order.api.gateway.dto.DeliveryNoteDTO;
import com.ruijing.store.order.api.gateway.dto.OrderOMSConfigRequestDTO;
import com.ruijing.store.order.api.gateway.dto.OrderOMSConfigResponseDTO;

@RpcApi(value = "订单管理网关服务" ,description = "订单管理相关服务")
public interface OrderManageGWService {

    @RpcMethod("获取送货单数据 /store/order/orderManage/getDeliveryNote 后续会废弃,请使用/getCommonPrintData")
    @Deprecated
    RemoteResponse<DeliveryNoteDTO> getDeliveryNote(OrderBasicParamDTO orderBasicParamDTO);

    @RpcMethod("获取订单单据打印通用数据 /store/order/orderManage/getCommonPrintData")
    RemoteResponse<OrderCommonPrintDataDTO> getCommonPrintData(OrderCommonPrintParamDTO request);

    @RpcMethod("重新推送订单数据到广工等对接平台 /store/order/orderManage/retryPushOrderInfo")
    RemoteResponse<Boolean> retryPushOrderInfo(OrderBasicParamDTO orderBasicParamDTO);

    @RpcMethod("获取订单OMS配置 /store/order/orderManage/getOrderOMSConfig")
    RemoteResponse<OrderOMSConfigResponseDTO> getOrderOMSConfig(OrderOMSConfigRequestDTO requset);

    @RpcMethod("通过采购单重新推送订单数据到管理平台 /store/order/orderManage/retryPushOrderByAppId")
    RemoteResponse<Integer> retryPushOrderByAppId(OrderBasicParamDTO request);
}
