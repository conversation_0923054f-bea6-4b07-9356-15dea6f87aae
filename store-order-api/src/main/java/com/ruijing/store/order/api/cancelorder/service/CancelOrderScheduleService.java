package com.ruijing.store.order.api.cancelorder.service;

import com.ruijing.store.order.api.cancelorder.dto.CancelOrderScheduleReqDTO;

/**
 * @description: 定时 取消订单  接口
 * @author: zhuk
 * @create: 2019-07-10 16:07
 **/

public interface CancelOrderScheduleService {

    /**
     * 自动取消 超时未确认订单
     * @param cancelOrderScheduleReqDTO
     * @return
     */
    void autoCancelOrderForWaitingConfirm(CancelOrderScheduleReqDTO cancelOrderScheduleReqDTO);

    /**
     * 自动取消 超时 未确认取消订单
     * @param cancelOrderScheduleReqDTO
     */
    void autoAgreeCancelOrder(CancelOrderScheduleReqDTO cancelOrderScheduleReqDTO);

}
