package com.ruijing.store.order.api.base.orderdetail.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;

import java.io.Serializable;
import java.util.List;

@RpcModel("订单全部批次条形码信息")
public class OrderBatchesDTO implements Serializable {

    private static final long serialVersionUID = 1568073493644436120L;

    private List<OrderDetailBathesDTO> orderDetailBathes;

    public List<OrderDetailBathesDTO> getOrderDetailBathes() {
        return orderDetailBathes;
    }

    public OrderBatchesDTO setOrderDetailBathes(List<OrderDetailBathesDTO> orderDetailBathes) {
        this.orderDetailBathes = orderDetailBathes;
        return this;
    }
}
