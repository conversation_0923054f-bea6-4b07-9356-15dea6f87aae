package com.ruijing.store.order.api.base.enums;

/**
 * <AUTHOR>
 * @Description 订单按钮类型枚举
 * @Date: 2024/10/14 11:22
 **/
public enum OrderButtonTypeEnum {

    REGENERATE_WAREHOUSE_APPLICATION("重新生成出入库单据按钮"),

    AUDIT_SAMPLING("抽检"),

    /**
     * 暨大使用
     */
    COMPLETE_REPORT_EXPENSE("确认报账"),
    ;

    /**
     * 描述
     */
    private final String desc;

    OrderButtonTypeEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

}
