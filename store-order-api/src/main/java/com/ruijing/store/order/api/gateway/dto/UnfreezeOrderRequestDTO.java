package com.ruijing.store.order.api.gateway.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020/11/6 14:14
 * @Description
 **/
public class UnfreezeOrderRequestDTO implements Serializable {

    private static final long serialVersionUID = 4895662670236190667L;

    private Integer orderId;

    private String unfreezeVerify;

    public Integer getOrderId() {
        return orderId;
    }

    public UnfreezeOrderRequestDTO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public String getUnfreezeVerify() {
        return unfreezeVerify;
    }

    public UnfreezeOrderRequestDTO setUnfreezeVerify(String unfreezeVerify) {
        this.unfreezeVerify = unfreezeVerify;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("UnfreezeOrderRequestDTO{");
        sb.append("orderId=").append(orderId);
        sb.append(", unfreezeVerify='").append(unfreezeVerify).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
