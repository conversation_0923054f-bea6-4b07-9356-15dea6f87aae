package com.ruijing.store.order.api.cancelorder.dto;

import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 自动取消订单 入参对象
 * @author: zhuk
 * @create: 2019-07-10 16:12
 **/
public class CancelOrderScheduleReqDTO implements Serializable {

    private static final long serialVersionUID = 2015719328394658406L;
    /**
     * 需要取消的 订单列表
     */
    private List<OrderMasterDTO> orderMasterList;

    public List<OrderMasterDTO> getOrderMasterList() {
        return orderMasterList;
    }

    public void setOrderMasterList(List<OrderMasterDTO> orderMasterList) {
        this.orderMasterList = orderMasterList;
    }

    @Override
    public String toString() {
        return "CancelOrderScheduleReqDTO{" +
                "orderMasterList.size=" + orderMasterList.size() +
                '}';
    }
}
