package com.ruijing.store.order.api.base.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 退货日志操作类型
 */
public enum GoodsReturnOperationTypeEnum {
    PURCHASE_APPLY_GOODS_RETURN(0, "申请退货"),
    PURCHASE_CANCEL_GOODS_RETURN(1, "撤销退货申请"),
    SUPPLIER_AGREE_GOODS_RETURN(2, "商家同意退货"),
    PURCHASE_MANUAL_GOODS_RETURN(3, "采购人归还货物"),
    SUPPLIER_REJECT_GOODS_RETURN(4, "拒绝退货"),
    SUPPLIER_RECEIVE_GOODS_RETURN(5, "商家确认收货，退货成功"),
    SYSTEM_RECEIVE_GOODS_RETURN(6, "退货单超时未收货，系统自动收货"),
    SUPPLIER_REJECT_THEN_AUTO_CANCEL_GOODS_RETURN(7, "供应商拒绝退货自动取消退货申请"),
    FORCE_CANCEL_GOODS_RETURN(8, "强制取消退货"),
    SUPPLIER_DELAY_ACCEPT(12,"供应商延迟验收"),

    /**
     * 推送退货单给外部供应商中
     */
    PUSHING_RETURN_TO_SUPP(13, "退货单推送中"),

    /**
     * 推送退货单给外部供应商成功
     */
    PUSH_RETURN_TO_SUPP_SUCCESS(14, "退货单推送成功"),

    /**
     * 推送退货单给外部供应商失败
     */
    PUSH_RETURN_TO_SUPP_FAILURE(15, "退货单推送失败"),
    ;

    /**
     * 需要在展示在订单模块中的退货日志的操作类型集合
     */
    public static List<GoodsReturnOperationTypeEnum> needOrderLogList = new ArrayList<>();

    private static Map<Integer, GoodsReturnOperationTypeEnum> enumMap;

    static {
        needOrderLogList.add(GoodsReturnOperationTypeEnum.PURCHASE_APPLY_GOODS_RETURN);
        needOrderLogList.add(GoodsReturnOperationTypeEnum.PURCHASE_CANCEL_GOODS_RETURN);
        needOrderLogList.add(GoodsReturnOperationTypeEnum.SUPPLIER_RECEIVE_GOODS_RETURN);
        needOrderLogList.add(GoodsReturnOperationTypeEnum.SYSTEM_RECEIVE_GOODS_RETURN);
        needOrderLogList.add(GoodsReturnOperationTypeEnum.FORCE_CANCEL_GOODS_RETURN);
        needOrderLogList.add(GoodsReturnOperationTypeEnum.PUSHING_RETURN_TO_SUPP);
        needOrderLogList.add(GoodsReturnOperationTypeEnum.PUSH_RETURN_TO_SUPP_SUCCESS);
        needOrderLogList.add(GoodsReturnOperationTypeEnum.PUSH_RETURN_TO_SUPP_FAILURE);

        GoodsReturnOperationTypeEnum[] values = GoodsReturnOperationTypeEnum.values();
        if (values.length < 1) {
            throw new IllegalArgumentException("the GoodsReturnOperationTypeEnum instance is 0");
        }
        enumMap = new HashMap<>(values.length);
        for (GoodsReturnOperationTypeEnum it : values) {
            enumMap.put(it.getCode(), it);
        }
    }

    /**
     * 编码
     */
    private Integer code;

    /**
     * 描述
     */
    private String description;

    GoodsReturnOperationTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 通过code获取映射枚举值
     * @param code
     * @return  enum instance
     */
    public static GoodsReturnOperationTypeEnum getByCode(Integer code) {
        GoodsReturnOperationTypeEnum instance = enumMap.get(code);
        if (instance == null) {
            throw new IllegalStateException("unknown DockingPushStatus, current code:" + code);
        }
        return instance;
    }
}
