package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class OrderGoodsReturnPrintDTO implements Serializable {

    private static final long serialVersionUID = 5268036086798289321L;

    @RpcModelProperty("退货单号")
    private String returnNo;

    @RpcModelProperty("退货日期")
    private Date applyGoodsReturnTime;

    @RpcModelProperty("退货单状态")
    private Integer returnStatus;

    @RpcModelProperty("退货总金额")
    private BigDecimal totalPrice;

    @RpcModelProperty("退货单商品明细")
    private List<OrderDetailReturnPrintDTO> returnDetailPrintList;

    public String getReturnNo() {
        return returnNo;
    }

    public void setReturnNo(String returnNo) {
        this.returnNo = returnNo;
    }

    public Date getApplyGoodsReturnTime() {
        return applyGoodsReturnTime;
    }

    public void setApplyGoodsReturnTime(Date applyGoodsReturnTime) {
        this.applyGoodsReturnTime = applyGoodsReturnTime;
    }

    public Integer getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(Integer returnStatus) {
        this.returnStatus = returnStatus;
    }

    public List<OrderDetailReturnPrintDTO> getReturnDetailPrintList() {
        return returnDetailPrintList;
    }

    public void setReturnDetailPrintList(List<OrderDetailReturnPrintDTO> returnDetailPrintList) {
        this.returnDetailPrintList = returnDetailPrintList;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    @Override
    public String toString() {
        return "OrderGoodsReturnPrintDTO{" +
                "returnNo='" + returnNo + '\'' +
                ", applyGoodsReturnTime=" + applyGoodsReturnTime +
                ", returnStatus=" + returnStatus +
                ", returnDetailList=" + returnDetailPrintList +
                '}';
    }
}
