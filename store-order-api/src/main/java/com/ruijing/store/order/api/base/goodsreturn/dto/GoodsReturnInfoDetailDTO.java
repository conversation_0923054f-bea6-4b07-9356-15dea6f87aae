package com.ruijing.store.order.api.base.goodsreturn.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

public class GoodsReturnInfoDetailDTO implements Serializable {

    private static final long serialVersionUID = 7096915335520092278L;

    @RpcModelProperty("订单商品id")
    private String detailId;

    @RpcModelProperty("商品名称")
    private String goodsName;

    @RpcModelProperty("商品货号")
    private String goodsCode;

    @RpcModelProperty("规格")
    private String specification;

    @RpcModelProperty("商品图片路径")
    private String goodsPicturePath;

    @RpcModelProperty("危化品标签")
    private String dangerousTag;

    @RpcModelProperty("退货商品数量")
    private BigDecimal quantity;

    @RpcModelProperty("退货原因")
    private String returnReason;

    @RpcModelProperty("退货说明")
    private String remark;

    @RpcModelProperty("品牌")
    private String brand;

    @RpcModelProperty("金额")
    private BigDecimal amount;

    @RpcModelProperty("商品id")
    private String productId;

    @RpcModelProperty("商品单位")
    private String unit;

    @RpcModelProperty("商品单价")
    private BigDecimal price;

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getDangerousTag() {
        return dangerousTag;
    }

    public void setDangerousTag(String dangerousTag) {
        this.dangerousTag = dangerousTag;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public void setReturnReason(String returnReason) {
        this.returnReason = returnReason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getGoodsPicturePath() {
        return goodsPicturePath;
    }

    public void setGoodsPicturePath(String goodsPicturePath) {
        this.goodsPicturePath = goodsPicturePath;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnInfoDetailVO{");
        sb.append("detailId='").append(detailId).append('\'');
        sb.append("goodsName='").append(goodsName).append('\'');
        sb.append(", goodsCode='").append(goodsCode).append('\'');
        sb.append(", specification='").append(specification).append('\'');
        sb.append(", picturePath='").append(goodsPicturePath).append('\'');
        sb.append(", dangerousTag='").append(dangerousTag).append('\'');
        sb.append(", quantity=").append(quantity);
        sb.append(", returnReason='").append(returnReason).append('\'');
        sb.append(", remark=").append(remark);
        sb.append('}');
        return sb.toString();
    }
}
