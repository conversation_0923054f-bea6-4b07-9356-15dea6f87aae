package com.ruijing.store.order.api.base.docking.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 第三方对接单位更新请求入参
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/3/18 18:09
 **/
public class ThirdPartUpdateRequestDTO implements Serializable {

    private static final long serialVersionUID = -6349295062414506745L;

    /**
     * 机构/医院编码
     */
    private String orgCode;

    /**
     * 对接单位调用商城更新订单入参
     */
    private List<ThirdPartUpdateOrderRequestDTO> thirdPartUpdateOrderRequestDTOList;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public List<ThirdPartUpdateOrderRequestDTO> getThirdPartUpdateOrderRequestDTOList() {
        return thirdPartUpdateOrderRequestDTOList;
    }

    public void setThirdPartUpdateOrderRequestDTOList(List<ThirdPartUpdateOrderRequestDTO> thirdPartUpdateOrderRequestDTOList) {
        this.thirdPartUpdateOrderRequestDTOList = thirdPartUpdateOrderRequestDTOList;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ThirdPartUpdateRequestDTO{");
        sb.append("orgCode='").append(orgCode).append('\'');
        sb.append("thirdPartUpdateOrderRequestDTOList='").append(thirdPartUpdateOrderRequestDTOList).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
