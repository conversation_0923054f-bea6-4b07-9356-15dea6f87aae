package com.ruijing.store.order.api.base.other.dto;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/9/20 15:21
 * @description
 */
public class OrderOutWarehouseProductInfoDTO implements Serializable {
    
    private static final long serialVersionUID = 5059319158152662293L;
    
    @RpcModelProperty(value = "商品Id")
    private Long productId;

    @RpcModelProperty(value = "商品名称")
    private String productName;

    @RpcModelProperty(value = "商品规格")
    private String specifications;

    @RpcModelProperty(value = "商品品牌")
    private String brand;

    @RpcModelProperty(value = "商品货号")
    private String goodCode;

    @RpcModelProperty(value = "CAS号")
    private String casNo;

    @RpcModelProperty(value = "危化品标识(类型)")
    private Integer dangerousType;

    @RpcModelProperty(value = "危化品标识名称")
    private String dangerousTypeName;

    @RpcModelProperty(value = "是否是危化品, 0不是， 1是")
    private Integer dangerousFlag;

    @RpcModelProperty(value = "管制类型, 1,管制类;2,非管制,其他商品类型为null")
    private Integer regulatoryFlag;

    @RpcModelProperty(value = "单位")
    private String unit;

    @RpcModelProperty(value = "数量")
    private Integer quantity;

    @RpcModelProperty(value = "计量总量")
    private Double totalQuantity;

    @RpcModelProperty(value = "计量单位")
    private String quantityUnit;

    @RpcModelProperty(value = "供应商Id")
    private Integer supplierId;

    @RpcModelProperty(value = "供应商名称")
    private String supplierName;

    @RpcModelProperty(value = "商品图片")
    private String productPhoto;

    @RpcModelProperty(value = "是否是要提交入库的商品，如果这个商品的分类不在该单位指定的分类中则无需提交入库;0不需要入库，1需要入库")
    private Integer needSubmitWarehouseTag;

    @RpcModelProperty(value = "商品单价")
    private String singlePrice;

    @RpcModelProperty(value = "商品总额")
    private String totalPrice;

    @RpcModelProperty(value = "商品分类id")
    private Integer categoryId;

    @RpcModelProperty(value = "一级商品分类名称")
    private String firstLevelCategoryName;

    @RpcModelProperty(value = "是否需要用户输入危化品属性, 0否，1是")
    private Integer needShowDangerousInputFlag;

    /**
     * {@link com.ruijing.store.wms.api.enums.FormEnum}
     */
    @RpcModelProperty(value = "商品状态, 1 固体, 2 液体, 3 气体")
    private Integer form;

    @RpcModelProperty(value = "默认库房Id")
    private Integer defaultWarehouseId;

    @RpcModelProperty(value = "个性化商品品类名称，只用作存取显示")
    private String personalizedCategoryName;

    @RpcModelProperty(value = "订单明细id")
    private Integer orderDetailId;

    @RpcModelProperty(value = "注册编码", description = "医疗器械注册证书编号")
    private String medicalDeviceRegisCertNumber;

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public Integer getDangerousType() {
        return dangerousType;
    }

    public void setDangerousType(Integer dangerousType) {
        this.dangerousType = dangerousType;
    }

    public String getDangerousTypeName() {
        return dangerousTypeName;
    }

    public void setDangerousTypeName(String dangerousTypeName) {
        this.dangerousTypeName = dangerousTypeName;
    }

    public Integer getDangerousFlag() {
        return dangerousFlag;
    }

    public void setDangerousFlag(Integer dangerousFlag) {
        this.dangerousFlag = dangerousFlag;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getProductPhoto() {
        return productPhoto;
    }

    public void setProductPhoto(String productPhoto) {
        this.productPhoto = productPhoto;
    }

    public String getGoodCode() {
        return goodCode;
    }

    public void setGoodCode(String goodCode) {
        this.goodCode = goodCode;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public String getQuantityUnit() {
        return quantityUnit;
    }

    public void setQuantityUnit(String quantityUnit) {
        this.quantityUnit = quantityUnit;
    }

    public Double getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Double totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public Integer getNeedSubmitWarehouseTag() {
        return needSubmitWarehouseTag;
    }

    public void setNeedSubmitWarehouseTag(Integer needSubmitWarehouseTag) {
        this.needSubmitWarehouseTag = needSubmitWarehouseTag;
    }

    public String getSinglePrice() {
        return singlePrice;
    }

    public void setSinglePrice(String singlePrice) {
        this.singlePrice = singlePrice;
    }

    public String getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getNeedShowDangerousInputFlag() {
        return needShowDangerousInputFlag;
    }

    public void setNeedShowDangerousInputFlag(Integer needShowDangerousInputFlag) {
        this.needShowDangerousInputFlag = needShowDangerousInputFlag;
    }

    public Integer getRegulatoryFlag() {
        return regulatoryFlag;
    }

    public void setRegulatoryFlag(Integer regulatoryFlag) {
        this.regulatoryFlag = regulatoryFlag;
    }

    public Integer getForm() {
        return form;
    }

    public void setForm(Integer form) {
        this.form = form;
    }

    public String getFirstLevelCategoryName() {
        return firstLevelCategoryName;
    }

    public void setFirstLevelCategoryName(String firstLevelCategoryName) {
        this.firstLevelCategoryName = firstLevelCategoryName;
    }

    public Integer getDefaultWarehouseId() {
        return defaultWarehouseId;
    }

    public void setDefaultWarehouseId(Integer defaultWarehouseId) {
        this.defaultWarehouseId = defaultWarehouseId;
    }

    public String getPersonalizedCategoryName() {
        return personalizedCategoryName;
    }

    public void setPersonalizedCategoryName(String personalizedCategoryName) {
        this.personalizedCategoryName = personalizedCategoryName;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public String getMedicalDeviceRegisCertNumber() {
        return medicalDeviceRegisCertNumber;
    }

    public void setMedicalDeviceRegisCertNumber(String medicalDeviceRegisCertNumber) {
        this.medicalDeviceRegisCertNumber = medicalDeviceRegisCertNumber;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderOutWarehouseProductInfoDTO{");
        sb.append("productId=").append(productId);
        sb.append(", productName='").append(productName).append('\'');
        sb.append(", specifications='").append(specifications).append('\'');
        sb.append(", brand='").append(brand).append('\'');
        sb.append(", goodCode='").append(goodCode).append('\'');
        sb.append(", casNo='").append(casNo).append('\'');
        sb.append(", dangerousType=").append(dangerousType);
        sb.append(", dangerousTypeName='").append(dangerousTypeName).append('\'');
        sb.append(", dangerousFlag=").append(dangerousFlag);
        sb.append(", regulatoryFlag=").append(regulatoryFlag);
        sb.append(", unit='").append(unit).append('\'');
        sb.append(", quantity=").append(quantity);
        sb.append(", totalQuantity=").append(totalQuantity);
        sb.append(", quantityUnit='").append(quantityUnit).append('\'');
        sb.append(", supplierId=").append(supplierId);
        sb.append(", supplierName='").append(supplierName).append('\'');
        sb.append(", productPhoto='").append(productPhoto).append('\'');
        sb.append(", needSubmitWarehouseTag=").append(needSubmitWarehouseTag);
        sb.append(", singlePrice='").append(singlePrice).append('\'');
        sb.append(", totalPrice='").append(totalPrice).append('\'');
        sb.append(", categoryId=").append(categoryId);
        sb.append(", firstLevelCategoryName='").append(firstLevelCategoryName).append('\'');
        sb.append(", needShowDangerousInputFlag=").append(needShowDangerousInputFlag);
        sb.append(", form=").append(form);
        sb.append(", defaultWarehouseId=").append(defaultWarehouseId);
        sb.append(", personalizedCategoryName='").append(personalizedCategoryName).append('\'');
        sb.append(", orderDetailId=").append(orderDetailId);
        sb.append(", medicalDeviceRegisCertNumber='").append(medicalDeviceRegisCertNumber).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
