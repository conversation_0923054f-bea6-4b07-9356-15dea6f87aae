package com.ruijing.store.order.api.general.dto;

import java.io.Serializable;

/**
 * @description: 更新订单索引状态 入参对象
 * @author: zhuk
 * @create: 2019-08-29 10:38
 **/
public class UpdateOrderSearchParamDTO implements Serializable {

    private static final long serialVersionUID = -8216911397246604331L;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 订单详情id
     */
    private Integer orderDetailId;

    /**
     * 商品名称
     */
    private String goodName;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public String getGoodName() {
        return goodName;
    }

    public void setGoodName(String goodName) {
        this.goodName = goodName;
    }

    @Override
    public String toString() {
        return "UpdateOrderSearchParamDTO{" +
                "orderId='" + orderId + '\'' +
                ", status=" + status +
                ", orderDetailId=" + orderDetailId +
                ", goodName='" + goodName + '\'' +
                '}';
    }
}
