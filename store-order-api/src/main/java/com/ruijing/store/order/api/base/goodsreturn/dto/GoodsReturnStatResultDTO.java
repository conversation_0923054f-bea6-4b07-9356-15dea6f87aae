package com.ruijing.store.order.api.base.goodsreturn.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2021/7/2 16:52
 */
@RpcModel("退货——统计返回体")
public class GoodsReturnStatResultDTO implements Serializable {

    private static final long serialVersionUID = -7255004386932989950L;

    /**
     * 退货统计总金额
     */
    @RpcModelProperty(value = "退货统计总金额")
    private BigDecimal returnAmount;

    public BigDecimal getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(BigDecimal returnAmount) {
        this.returnAmount = returnAmount;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnStatResultDTO{");
        sb.append("returnAmount=").append(returnAmount);
        sb.append('}');
        return sb.toString();
    }
}
