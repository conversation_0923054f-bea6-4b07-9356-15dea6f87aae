package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @title: PrintOrderDTO
 * @projectName research-statement-web
 * @description: 验收单打印订单信息
 * @author：zhongyulei
 * @date 2019-12-06 11:35
 */
public class PrintOrderDTO implements Serializable {

    private static final long serialVersionUID = 7383102556057248782L;
    /**
     * 供应商
     */
    private String supplierName;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单详情
     */
    private List<PrintOrderDetailDTO> orderDetailDtoList;

    /**
     * 经费卡编号数组
     */
    private Set<String> fundCardNoSet;

    /**
     * 科研卡号及其负责人
     */
    private List<PrintFundCardManagerDTO> fundCardManagerDtoList;

    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;

    /**
     * 订单总数量
     */
    private Integer orderTotalQuantity;

    /**
     *  申购人
     */
    private String buyerName;

    /**
     * 申购人联系方式
     */
    private String buyerTelPhone;

    /**
     * 验收人
     */
    private String acceptor;

    /**
     * 验收日期
     */
    private Date acceptanceDate;

    /**
     * 入库操作人名字
     */
    private Set<String> entryHouseNameSet;

    /**
     * 出库操作人名字
     */
    private Set<String> exitHouseNameSet;

    /**
     * 经费卡负责人
     */
    private Set<String> fundCardManagerSet;

    /**
     * 费用类别
     */
    private Set<String> categoryTagSet;

    /**
     * 订单审批记录
     */
    private List<OrderPurchaseApprovalLogDTO> orderApprovalLogList;

    /**
     * 流程种类， 0:线上, 1:线下
     */
    private Integer species;

    /**
     * 订单收货日期
     */
    private String deliveryAddress;

    public List<OrderPurchaseApprovalLogDTO> getOrderApprovalLogList() {
        return orderApprovalLogList;
    }

    public void setOrderApprovalLogList(List<OrderPurchaseApprovalLogDTO> orderApprovalLogList) {
        this.orderApprovalLogList = orderApprovalLogList;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public List<PrintOrderDetailDTO> getOrderDetailDtoList() {
        return orderDetailDtoList;
    }

    public void setOrderDetailDtoList(List<PrintOrderDetailDTO> orderDetailDtoList) {
        this.orderDetailDtoList = orderDetailDtoList;
    }

    public Set<String> getFundCardNoSet() {
        return fundCardNoSet;
    }

    public void setFundCardNoSet(Set<String> fundCardNoSet) {
        this.fundCardNoSet = fundCardNoSet;
    }

    public List<PrintFundCardManagerDTO> getFundCardManagerDtoList() {
        return fundCardManagerDtoList;
    }

    public void setFundCardManagerDtoList(List<PrintFundCardManagerDTO> fundCardManagerDtoList) {
        this.fundCardManagerDtoList = fundCardManagerDtoList;
    }

    public BigDecimal getOrderTotalAmount() {
        return orderTotalAmount;
    }

    public void setOrderTotalAmount(BigDecimal orderTotalAmount) {
        this.orderTotalAmount = orderTotalAmount;
    }

    public Integer getOrderTotalQuantity() {
        return orderTotalQuantity;
    }

    public void setOrderTotalQuantity(Integer orderTotalQuantity) {
        this.orderTotalQuantity = orderTotalQuantity;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getBuyerTelPhone() {
        return buyerTelPhone;
    }

    public void setBuyerTelPhone(String buyerTelPhone) {
        this.buyerTelPhone = buyerTelPhone;
    }

    public String getAcceptor() {
        return acceptor;
    }

    public void setAcceptor(String acceptor) {
        this.acceptor = acceptor;
    }

    public Date getAcceptanceDate() {
        return acceptanceDate;
    }

    public void setAcceptanceDate(Date acceptanceDate) {
        this.acceptanceDate = acceptanceDate;
    }

    public Set<String> getEntryHouseNameSet() {
        return entryHouseNameSet;
    }

    public void setEntryHouseNameSet(Set<String> entryHouseNameSet) {
        this.entryHouseNameSet = entryHouseNameSet;
    }

    public Set<String> getExitHouseNameSet() {
        return exitHouseNameSet;
    }

    public void setExitHouseNameSet(Set<String> exitHouseNameSet) {
        this.exitHouseNameSet = exitHouseNameSet;
    }

    public Set<String> getFundCardManagerSet() {
        return fundCardManagerSet;
    }

    public void setFundCardManagerSet(Set<String> fundCardManagerSet) {
        this.fundCardManagerSet = fundCardManagerSet;
    }

    public Set<String> getCategoryTagSet() {
        return categoryTagSet;
    }

    public void setCategoryTagSet(Set<String> categoryTagSet) {
        this.categoryTagSet = categoryTagSet;
    }

    public Integer getSpecies() {
        return species;
    }

    public void setSpecies(Integer species) {
        this.species = species;
    }

    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("PrintOrderDTO{");
        sb.append("supplierName='").append(supplierName).append('\'');
        sb.append(", orderNo='").append(orderNo).append('\'');
        sb.append(", orderDetailDtoList=").append(orderDetailDtoList);
        sb.append(", fundCardNoSet=").append(fundCardNoSet);
        sb.append(", fundCardManagerDtoList=").append(fundCardManagerDtoList);
        sb.append(", orderTotalAmount=").append(orderTotalAmount);
        sb.append(", orderTotalQuantity=").append(orderTotalQuantity);
        sb.append(", buyerName='").append(buyerName).append('\'');
        sb.append(", buyerTelPhone='").append(buyerTelPhone).append('\'');
        sb.append(", acceptor='").append(acceptor).append('\'');
        sb.append(", acceptanceDate=").append(acceptanceDate);
        sb.append(", entryHouseNameSet=").append(entryHouseNameSet);
        sb.append(", exitHouseNameSet=").append(exitHouseNameSet);
        sb.append(", fundCardManagerSet=").append(fundCardManagerSet);
        sb.append(", categoryTagSet=").append(categoryTagSet);
        sb.append(", orderApprovalLogList=").append(orderApprovalLogList);
        sb.append(", species=").append(species);
        sb.append(", deliveryAddress='").append(deliveryAddress).append('\'');
        sb.append('}');
        return sb.toString();
    }
}