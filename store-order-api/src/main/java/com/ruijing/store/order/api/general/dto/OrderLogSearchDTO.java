package com.ruijing.store.order.api.general.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * @program: store-order-service
 * @description: 订单日志
 * @author: zhuk
 * @create: 2019-06-01 11:01
 **/
public class OrderLogSearchDTO implements Serializable {
    private static final long serialVersionUID = 1730552886863562632L;
    /**
     * log_id : 123456
     * approve_status : 1
     * operator_id : 123456
     * creation_time : 123456
     */

    /**
     * 日志id
     */
    private Integer logId;

    /**
     * 审批状态
     */
    private Integer approveStatus;

    /**
     * 操作人id
     */
    private Integer operatorId;

    /**
     * 创建时间
     */
    private Date creationTime;

    public Integer getLogId() {
        return logId;
    }

    public void setLogId(Integer logId) {
        this.logId = logId;
    }

    public Integer getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderLogSearchDTO{");
        sb.append("logId=").append(logId);
        sb.append(", approveStatus=").append(approveStatus);
        sb.append(", operatorId=").append(operatorId);
        sb.append(", creationTime=").append(creationTime);
        sb.append('}');
        return sb.toString();
    }
}
