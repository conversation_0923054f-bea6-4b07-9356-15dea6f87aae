package com.ruijing.store.order.api.base.orderextra.enums;

import java.util.Objects;

/**
 * @author: liwenyu
 * @createTime: 2023-08-08 18:09
 * @description:
 **/
public enum OrderDetailExtraEnum {

    /**
     * @see com.ruijing.shop.goods.api.enums.CategoryAttr
     */
    PRESS("出版社", 1),
    CONCENTRATION("纯度/浓度", 2),

    /**
     * 中爆一物一码对接专用
     */
    CB_ITEM_IDENTIFY("中爆物品标识", 3),

    /**
     * 关联的广告id
     */
    ADVERTISEMENT_ID("关联的广告id", 4),

    /**
     * 暂时只有中大合同用，标识商品重量/体积
     */
    GOODS_WEIGHT("商品重量", 5),

    /**
     * 暂时只有中大合同用，配套GOODS_WEIGHT(商品重量)一起使用，表示其单位。中大表示商品重量/体积单位
     */
    GOODS_WEIGHT_UNIT("重量单位", 6),

    PACKING_UNIT("包装规格", 7),

    MIN_PACKING_UNIT("最小包装规格", 8),

    PACKING_VALUE("包装数量", 9),

    MIN_PACKING_VALUE("最小包装数量", 10),

    /**
     * 用来记录与对方平台商品的关联关系用，目前港科大用到
     */
    EXTERNAL_ID("外部平台ID", 11),

    /**
     * 实发数量，针对对接供应商的情况下缺货还发货的情况（目前仅有当当）
     */
    ISSUED_COUNT("实发数量", 12),

    /**
     * 绑定在商品纬度的气瓶码列表，结构为[1,2,3]。
     * 若开启了一物一码的情况下，这里不存放绑定的气瓶码，改由一物一码表进行存放（一物一码粒度）
     */
    BIND_GAS_BOTTLE_BARCODE("绑定的气瓶", 13),

    STORAGE_CONDITION("储存条件", 14),

    MEDICAL_DEVICE_REGIS_CERT_NUMBER("医疗器械注册证书编号", 15),



    MODEL_NUMBER("型号", 19),

    COMPLETION_CYCLE("完成周期", 20),

    PRODUCT_SPECIFICATION("产品规格", 21),
    ;

    OrderDetailExtraEnum(String value, Integer type) {
        this.value = value;
        this.type = type;
    }

    private final String value;
    private final Integer type;

    public String getValue() {
        return this.value;
    }

    public Integer getType() {
        return type;
    }

    public static OrderDetailExtraEnum getByValue(String value) {
        for (OrderDetailExtraEnum orderDetailExtraEnum : OrderDetailExtraEnum.values()) {
            if (Objects.equals(orderDetailExtraEnum.value, value)) {
                return orderDetailExtraEnum;
            }
        }
        return null;
    }

    public static OrderDetailExtraEnum getByType(Integer type) {
        for (OrderDetailExtraEnum orderDetailExtraEnum : OrderDetailExtraEnum.values()) {
            if (Objects.equals(orderDetailExtraEnum.type, type)) {
                return orderDetailExtraEnum;
            }
        }
        return null;
    }
}
