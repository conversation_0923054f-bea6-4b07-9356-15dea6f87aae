package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: orderMasterDTO
 * @author: zhuk
 * @create: 2019-07-02 09:58
 **/
public class OrderMasterDTO implements Serializable {

    private static final long serialVersionUID = 6262824888176386685L;
    /**
     *
     */
    private Integer id;

    /**
     * 订单id
     */
    private String fmasterguid;

    /**
     * 采购申请id
     */
    private Integer ftbuyappid;

    /**
     *
     */
    private String forderno;

    /**
     * 订单签订日期
     */
    private Date forderdate;

    /**
     * 购买者id
     */
    private Integer fbuyerid;

    /**
     * 购买者代码
     */
    private String fbuyercode;

    /**
     * 购买者名称
     */
    private String fbuyername;

    /**
     * 采购人电邮
     */
    private String fbuyeremail;

    /**
     * 采购联系人
     */
    private String fbuyercontactman;

    /**
     * 采购联系电话
     */
    private String fbuyertelephone;

    /**
     * 采购部门id
     */
    private Integer fbuydepartmentid;

    /**
     * 采购部门
     */
    private String fbuydepartment;

    /**
     * 供应商id
     */
    private Integer fsuppid;

    /**
     * 供应商代码
     */
    private String fsuppcode;

    /**
     * 供应商名称
     */
    private String fsuppname;

    /**
     * 送货地点
     */
    private String fbiderdeliveryplace;


    /**
     * 订单总价格
     */
    private BigDecimal forderamounttotal;

    /**
     * 经费状态：1、冻结成功，2、释放中，3、释放失败，4、释放成功5，扣减成功
     */
    private Integer fundStatus;

    /**
     * 经费释放失败原因
     */
    private String failedReason;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 确认日期
     */
    private Date fconfirmdate;

    /**
     * 确认人id
     */
    private String fconfirmmanid;

    /**
     * 确认人
     */
    private String fconfirmman;

    /**
     * 撤消日期
     */
    private Date fcanceldate;

    /**
     * 撤消人id
     */
    private String fcancelmanid;

    /**
     * 撤消人
     */
    private String fcancelman;

    /**
     * 送货日期
     */
    private Date fdeliverydate;

    /**
     * 送货id
     */
    private String fdeliverymanid;

    /**
     * 送货人
     */
    private String fdeliveryman;

    /**
     * 最后收货日期
     */
    private Date flastreceivedate;

    /**
     * 最后收货id
     */
    private String flastreceivemanid;

    /**
     * 最后收货人
     */
    private String flastreceiveman;

    /**
     * 评价日期
     */
    private Date fassessdate;

    /**
     * 评价id
     */
    private String fassessmanid;

    /**
     * 评价人
     */
    private String fassessman;

    /**
     *
     */
    private String piemail;

    /**
     *
     */
    private String projectid;

    /**
     *
     */
    private String projectnumber;

    /**
     *
     */
    private String projecttitle;

    /**
     *
     */
    private Integer fuserid;

    /**
     *
     */
    private String fusercode;

    /**
     *
     */
    private String fusername;


    /**
     * 结算单id
     */
    private Integer statementId;


    /**
     * 撤销原因
     */
    private String fcancelreason;

    /**
     * 拒绝取消原因
     */
    private String frefuseCancelReason;

    /**
     * 订单关闭时间
     */
    private Date shutDownDate;

    /**
     * 发货说明
     */
    private String deliveryInfo;

    /**
     * 发货单号
     */
    private String deliveryNo;

    /**
     * 退货金额
     */
    private Double returnAmount;

    /**
     * 拒绝撤销时间
     */
    private Date frefuseCancelDate;

    /**
     * 地址id
     */
    private Integer fdeliveryid;

    /**
     * 竞价单id
     */
    private String bidOrderId;

    /**
     * order类型,0:采购的,1:竞价单
     */
    private Integer orderType;

    /**
     * 验收时图片
     */
    private String receivePicUrls;

    /**
     * 项目记录id
     */
    private String tpiProjectId;

    /**
     * 订单原价
     */
    private BigDecimal originalAmount;

    /**
     * 出入库状态
     */
    private Byte inventoryStatus;

    /**
     * 流程种类 0:正常, 1:线下
     */
    private Byte species;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 结算中开始时间
     */
    private Date inStateTime;

    /**
     * 采购来源：0 PC；1 小程序
     */
    private Integer purchaseRootinType;

    /**
     * 运费
     */
    private BigDecimal carryFee;

    /**
     * 发票抬头id
     */
    private Integer invoiceTitleId;

    /**
     * 发票抬头名称
     */
    private String invoiceTitle;

    /**
     * 发票抬头号
     */
    private String invoiceTitleNumber;

    /**
     * 经费类型
     */
    private Integer fundType;

    /**
     * 经费类型描述
     */
    private String fundTypeName;

    /**
     * 订单创建时间
     */
    private Date createTime;

    /**
     * 审批流id
     */
    private Integer flowId;

    /**
     * 结算单状态
     */
    private Integer statementStatus;

    /**
     * 是否为旧订单
     */
    private Boolean oldFlag;

    /**
     * 课题组上一级部门id
     */
    private Integer deptParentId;

    /**
     * 课题组上一级部门名字
     */
    private String deptParentName;

    @RpcModelProperty("完成时间")
    private Date finishDate;

    public Integer getId() {
        return id;
    }

    public String getFmasterguid() {
        return fmasterguid;
    }

    public Integer getFtbuyappid() {
        return ftbuyappid;
    }

    public String getForderno() {
        return forderno;
    }

    public Date getForderdate() {
        return forderdate;
    }

    public Integer getFbuyerid() {
        return fbuyerid;
    }

    public String getFbuyercode() {
        return fbuyercode;
    }

    public String getFbuyername() {
        return fbuyername;
    }

    public String getFbuyeremail() {
        return fbuyeremail;
    }

    public String getFbuyercontactman() {
        return fbuyercontactman;
    }

    public String getFbuyertelephone() {
        return fbuyertelephone;
    }

    public Integer getFbuydepartmentid() {
        return fbuydepartmentid;
    }

    public String getFbuydepartment() {
        return fbuydepartment;
    }

    public Integer getFsuppid() {
        return fsuppid;
    }

    public String getFsuppcode() {
        return fsuppcode;
    }

    public String getFsuppname() {
        return fsuppname;
    }

    public String getFbiderdeliveryplace() {
        return fbiderdeliveryplace;
    }

    public BigDecimal getForderamounttotal() {
        return forderamounttotal;
    }

    public Integer getFundStatus() {
        return fundStatus;
    }

    public String getFailedReason() {
        return failedReason;
    }

    public Integer getStatus() {
        return status;
    }

    public Date getFconfirmdate() {
        return fconfirmdate;
    }

    public String getFconfirmmanid() {
        return fconfirmmanid;
    }

    public String getFconfirmman() {
        return fconfirmman;
    }

    public Date getFcanceldate() {
        return fcanceldate;
    }

    public String getFcancelmanid() {
        return fcancelmanid;
    }

    public String getFcancelman() {
        return fcancelman;
    }

    public Date getFdeliverydate() {
        return fdeliverydate;
    }

    public String getFdeliverymanid() {
        return fdeliverymanid;
    }

    public String getFdeliveryman() {
        return fdeliveryman;
    }

    public Date getFlastreceivedate() {
        return flastreceivedate;
    }

    public String getFlastreceivemanid() {
        return flastreceivemanid;
    }

    public String getFlastreceiveman() {
        return flastreceiveman;
    }

    public Date getFassessdate() {
        return fassessdate;
    }

    public String getFassessmanid() {
        return fassessmanid;
    }

    public String getFassessman() {
        return fassessman;
    }

    public String getPiemail() {
        return piemail;
    }

    public String getProjectid() {
        return projectid;
    }

    public String getProjectnumber() {
        return projectnumber;
    }

    public String getProjecttitle() {
        return projecttitle;
    }

    public Integer getFuserid() {
        return fuserid;
    }

    public String getFusercode() {
        return fusercode;
    }

    public String getFusername() {
        return fusername;
    }

    public Integer getStatementId() {
        return statementId;
    }

    public String getFcancelreason() {
        return fcancelreason;
    }

    public String getFrefuseCancelReason() {
        return frefuseCancelReason;
    }

    public Date getShutDownDate() {
        return shutDownDate;
    }

    public String getDeliveryInfo() {
        return deliveryInfo;
    }

    public String getDeliveryNo() {
        return deliveryNo;
    }

    public Double getReturnAmount() {
        return returnAmount;
    }

    public Date getFrefuseCancelDate() {
        return frefuseCancelDate;
    }

    public Integer getFdeliveryid() {
        return fdeliveryid;
    }

    public String getBidOrderId() {
        return bidOrderId;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public String getReceivePicUrls() {
        return receivePicUrls;
    }

    public String getTpiProjectId() {
        return tpiProjectId;
    }

    public BigDecimal getOriginalAmount() {
        return originalAmount;
    }

    public Byte getInventoryStatus() {
        return inventoryStatus;
    }

    public Byte getSpecies() {
        return species;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public Date getInStateTime() {
        return inStateTime;
    }

    public Integer getPurchaseRootinType() {
        return purchaseRootinType;
    }

    public BigDecimal getCarryFee() {
        return carryFee;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setFmasterguid(String fmasterguid) {
        this.fmasterguid = fmasterguid;
    }

    public void setFtbuyappid(Integer ftbuyappid) {
        this.ftbuyappid = ftbuyappid;
    }

    public void setForderno(String forderno) {
        this.forderno = forderno;
    }

    public void setForderdate(Date forderdate) {
        this.forderdate = forderdate;
    }

    public void setFbuyerid(Integer fbuyerid) {
        this.fbuyerid = fbuyerid;
    }

    public void setFbuyercode(String fbuyercode) {
        this.fbuyercode = fbuyercode;
    }

    public void setFbuyername(String fbuyername) {
        this.fbuyername = fbuyername;
    }

    public void setFbuyeremail(String fbuyeremail) {
        this.fbuyeremail = fbuyeremail;
    }

    public void setFbuyercontactman(String fbuyercontactman) {
        this.fbuyercontactman = fbuyercontactman;
    }

    public void setFbuyertelephone(String fbuyertelephone) {
        this.fbuyertelephone = fbuyertelephone;
    }

    public void setFbuydepartmentid(Integer fbuydepartmentid) {
        this.fbuydepartmentid = fbuydepartmentid;
    }

    public void setFbuydepartment(String fbuydepartment) {
        this.fbuydepartment = fbuydepartment;
    }

    public void setFsuppid(Integer fsuppid) {
        this.fsuppid = fsuppid;
    }

    public void setFsuppcode(String fsuppcode) {
        this.fsuppcode = fsuppcode;
    }

    public void setFsuppname(String fsuppname) {
        this.fsuppname = fsuppname;
    }

    public void setFbiderdeliveryplace(String fbiderdeliveryplace) {
        this.fbiderdeliveryplace = fbiderdeliveryplace;
    }

    public void setForderamounttotal(BigDecimal forderamounttotal) {
        this.forderamounttotal = forderamounttotal;
    }

    public void setFundStatus(Integer fundStatus) {
        this.fundStatus = fundStatus;
    }

    public void setFailedReason(String failedReason) {
        this.failedReason = failedReason;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public void setFconfirmdate(Date fconfirmdate) {
        this.fconfirmdate = fconfirmdate;
    }

    public void setFconfirmmanid(String fconfirmmanid) {
        this.fconfirmmanid = fconfirmmanid;
    }

    public void setFconfirmman(String fconfirmman) {
        this.fconfirmman = fconfirmman;
    }

    public void setFcanceldate(Date fcanceldate) {
        this.fcanceldate = fcanceldate;
    }

    public void setFcancelmanid(String fcancelmanid) {
        this.fcancelmanid = fcancelmanid;
    }

    public void setFcancelman(String fcancelman) {
        this.fcancelman = fcancelman;
    }

    public void setFdeliverydate(Date fdeliverydate) {
        this.fdeliverydate = fdeliverydate;
    }

    public void setFdeliverymanid(String fdeliverymanid) {
        this.fdeliverymanid = fdeliverymanid;
    }

    public void setFdeliveryman(String fdeliveryman) {
        this.fdeliveryman = fdeliveryman;
    }

    public void setFlastreceivedate(Date flastreceivedate) {
        this.flastreceivedate = flastreceivedate;
    }

    public void setFlastreceivemanid(String flastreceivemanid) {
        this.flastreceivemanid = flastreceivemanid;
    }

    public void setFlastreceiveman(String flastreceiveman) {
        this.flastreceiveman = flastreceiveman;
    }

    public void setFassessdate(Date fassessdate) {
        this.fassessdate = fassessdate;
    }

    public void setFassessmanid(String fassessmanid) {
        this.fassessmanid = fassessmanid;
    }

    public void setFassessman(String fassessman) {
        this.fassessman = fassessman;
    }

    public void setPiemail(String piemail) {
        this.piemail = piemail;
    }

    public void setProjectid(String projectid) {
        this.projectid = projectid;
    }

    public void setProjectnumber(String projectnumber) {
        this.projectnumber = projectnumber;
    }

    public void setProjecttitle(String projecttitle) {
        this.projecttitle = projecttitle;
    }

    public void setFuserid(Integer fuserid) {
        this.fuserid = fuserid;
    }

    public void setFusercode(String fusercode) {
        this.fusercode = fusercode;
    }

    public void setFusername(String fusername) {
        this.fusername = fusername;
    }

    public void setStatementId(Integer statementId) {
        this.statementId = statementId;
    }

    public void setFcancelreason(String fcancelreason) {
        this.fcancelreason = fcancelreason;
    }

    public void setFrefuseCancelReason(String frefuseCancelReason) {
        this.frefuseCancelReason = frefuseCancelReason;
    }

    public void setShutDownDate(Date shutDownDate) {
        this.shutDownDate = shutDownDate;
    }

    public void setDeliveryInfo(String deliveryInfo) {
        this.deliveryInfo = deliveryInfo;
    }

    public void setDeliveryNo(String deliveryNo) {
        this.deliveryNo = deliveryNo;
    }

    public void setReturnAmount(Double returnAmount) {
        this.returnAmount = returnAmount;
    }

    public void setFrefuseCancelDate(Date frefuseCancelDate) {
        this.frefuseCancelDate = frefuseCancelDate;
    }

    public void setFdeliveryid(Integer fdeliveryid) {
        this.fdeliveryid = fdeliveryid;
    }

    public void setBidOrderId(String bidOrderId) {
        this.bidOrderId = bidOrderId;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public void setReceivePicUrls(String receivePicUrls) {
        this.receivePicUrls = receivePicUrls;
    }

    public void setTpiProjectId(String tpiProjectId) {
        this.tpiProjectId = tpiProjectId;
    }

    public void setOriginalAmount(BigDecimal originalAmount) {
        this.originalAmount = originalAmount;
    }

    public void setInventoryStatus(Byte inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
    }

    public void setSpecies(Byte species) {
        this.species = species;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public void setInStateTime(Date inStateTime) {
        this.inStateTime = inStateTime;
    }

    public void setPurchaseRootinType(Integer purchaseRootinType) {
        this.purchaseRootinType = purchaseRootinType;
    }

    public void setCarryFee(BigDecimal carryFee) {
        this.carryFee = carryFee;
    }

    public Integer getInvoiceTitleId() {
        return invoiceTitleId;
    }

    public void setInvoiceTitleId(Integer invoiceTitleId) {
        this.invoiceTitleId = invoiceTitleId;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public String getInvoiceTitleNumber() {
        return invoiceTitleNumber;
    }

    public void setInvoiceTitleNumber(String invoiceTitleNumber) {
        this.invoiceTitleNumber = invoiceTitleNumber;
    }

    public Integer getFundType() {
        return fundType;
    }

    public void setFundType(Integer fundType) {
        this.fundType = fundType;
    }

    public String getFundTypeName() {
        return fundTypeName;
    }

    public void setFundTypeName(String fundTypeName) {
        this.fundTypeName = fundTypeName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public OrderMasterDTO setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Integer getFlowId() {
        return flowId;
    }

    public void setFlowId(Integer flowId) {
        this.flowId = flowId;
    }

    public Integer getStatementStatus() {
        return statementStatus;
    }

    public void setStatementStatus(Integer statementStatus) {
        this.statementStatus = statementStatus;
    }

    public Boolean getOldFlag() {
        return oldFlag;
    }

    public OrderMasterDTO setOldFlag(Boolean oldFlag) {
        this.oldFlag = oldFlag;
        return this;
    }

    public Integer getDeptParentId() {
        return deptParentId;
    }

    public void setDeptParentId(Integer deptParentId) {
        this.deptParentId = deptParentId;
    }

    public String getDeptParentName() {
        return deptParentName;
    }

    public void setDeptParentName(String deptParentName) {
        this.deptParentName = deptParentName;
    }

    public Date getFinishDate() {
        return finishDate;
    }

    public void setFinishDate(Date finishDate) {
        this.finishDate = finishDate;
    }

    @Override
    public String toString() {
        return "OrderMasterDTO{" +
                "id=" + id +
                ", fmasterguid='" + fmasterguid + '\'' +
                ", ftbuyappid=" + ftbuyappid +
                ", forderno='" + forderno + '\'' +
                ", forderdate=" + forderdate +
                ", fbuyerid=" + fbuyerid +
                ", fbuyercode='" + fbuyercode + '\'' +
                ", fbuyername='" + fbuyername + '\'' +
                ", fbuyeremail='" + fbuyeremail + '\'' +
                ", fbuyercontactman='" + fbuyercontactman + '\'' +
                ", fbuyertelephone='" + fbuyertelephone + '\'' +
                ", fbuydepartmentid=" + fbuydepartmentid +
                ", fbuydepartment='" + fbuydepartment + '\'' +
                ", fsuppid=" + fsuppid +
                ", fsuppcode='" + fsuppcode + '\'' +
                ", fsuppname='" + fsuppname + '\'' +
                ", fbiderdeliveryplace='" + fbiderdeliveryplace + '\'' +
                ", forderamounttotal=" + forderamounttotal +
                ", fundStatus=" + fundStatus +
                ", failedReason='" + failedReason + '\'' +
                ", status=" + status +
                ", fconfirmdate=" + fconfirmdate +
                ", fconfirmmanid='" + fconfirmmanid + '\'' +
                ", fconfirmman='" + fconfirmman + '\'' +
                ", fcanceldate=" + fcanceldate +
                ", fcancelmanid='" + fcancelmanid + '\'' +
                ", fcancelman='" + fcancelman + '\'' +
                ", fdeliverydate=" + fdeliverydate +
                ", fdeliverymanid='" + fdeliverymanid + '\'' +
                ", fdeliveryman='" + fdeliveryman + '\'' +
                ", flastreceivedate=" + flastreceivedate +
                ", flastreceivemanid='" + flastreceivemanid + '\'' +
                ", flastreceiveman='" + flastreceiveman + '\'' +
                ", fassessdate=" + fassessdate +
                ", fassessmanid='" + fassessmanid + '\'' +
                ", fassessman='" + fassessman + '\'' +
                ", piemail='" + piemail + '\'' +
                ", projectid='" + projectid + '\'' +
                ", projectnumber='" + projectnumber + '\'' +
                ", projecttitle='" + projecttitle + '\'' +
                ", fuserid=" + fuserid +
                ", fusercode='" + fusercode + '\'' +
                ", fusername='" + fusername + '\'' +
                ", statementId=" + statementId +
                ", fcancelreason='" + fcancelreason + '\'' +
                ", frefuseCancelReason='" + frefuseCancelReason + '\'' +
                ", shutDownDate=" + shutDownDate +
                ", deliveryInfo='" + deliveryInfo + '\'' +
                ", deliveryNo='" + deliveryNo + '\'' +
                ", returnAmount=" + returnAmount +
                ", frefuseCancelDate=" + frefuseCancelDate +
                ", fdeliveryid=" + fdeliveryid +
                ", bidOrderId='" + bidOrderId + '\'' +
                ", orderType=" + orderType +
                ", receivePicUrls='" + receivePicUrls + '\'' +
                ", tpiProjectId='" + tpiProjectId + '\'' +
                ", originalAmount=" + originalAmount +
                ", inventoryStatus=" + inventoryStatus +
                ", species=" + species +
                ", updateTime=" + updateTime +
                ", inStateTime=" + inStateTime +
                ", purchaseRootinType=" + purchaseRootinType +
                ", carryFee=" + carryFee +
                ", invoiceTitleId=" + invoiceTitleId +
                ", invoiceTitle='" + invoiceTitle + '\'' +
                ", invoiceTitleNumber='" + invoiceTitleNumber + '\'' +
                ", fundType=" + fundType +
                ", fundTypeName='" + fundTypeName + '\'' +
                ", createTime=" + createTime +
                ", flowId=" + flowId +
                ", statementStatus=" + statementStatus +
                ", oldFlag=" + oldFlag +
                ", deptParentId=" + deptParentId +
                ", deptParentName='" + deptParentName + '\'' +
                ", finishDate=" + finishDate +
                '}';
    }
}
