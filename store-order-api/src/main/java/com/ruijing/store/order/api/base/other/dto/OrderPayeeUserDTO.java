package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-06-18 16:11
 * @description:
 */
@Model("订单个人收款信息快照")
public class OrderPayeeUserDTO implements Serializable {

    private static final long serialVersionUID = -1767359225437317684L;

    @ModelProperty("姓名")
    private String name;

    @ModelProperty("工号/学号")
    private String jobNumber;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderPayeeUserDTO.class.getSimpleName() + "[", "]")
                .add("name='" + name + "'")
                .add("jobNumber='" + jobNumber + "'")
                .toString();
    }
}
