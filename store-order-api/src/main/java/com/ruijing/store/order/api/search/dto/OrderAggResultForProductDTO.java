package com.ruijing.store.order.api.search.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: Zeng <PERSON>
 * @Date: 2021/5/1 17:14
 */
public class OrderAggResultForProductDTO implements Serializable {

    private static final long serialVersionUID = 5025459671580179285L;

    /**
     * 商品名称（这个通过数据库或者搜索单独查找吧）
     */
    private String productName;

    /**
     * 商品id
     */
    private String productId;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 供应商id
     */
    private Integer suppId;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 数量
     */
    private Long quantity;

    public String getProductName() {
        return productName;
    }

    public OrderAggResultForProductDTO setProductName(String productName) {
        this.productName = productName;
        return this;
    }

    public String getProductId() {
        return productId;
    }

    public OrderAggResultForProductDTO setProductId(String productId) {
        this.productId = productId;
        return this;
    }

    public String getBrandId() {
        return brandId;
    }

    public OrderAggResultForProductDTO setBrandId(String brandId) {
        this.brandId = brandId;
        return this;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public OrderAggResultForProductDTO setAmount(BigDecimal amount) {
        this.amount = amount;
        return this;
    }

    public Long getQuantity() {
        return quantity;
    }

    public OrderAggResultForProductDTO setQuantity(Long quantity) {
        this.quantity = quantity;
        return this;
    }

    public Integer getSuppId() {
        return suppId;
    }

    public OrderAggResultForProductDTO setSuppId(Integer suppId) {
        this.suppId = suppId;
        return this;
    }
}
