package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.store.order.api.base.enums.InventoryStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.StatementOperatingEnum;

import java.io.Serializable;

/**
 * 仓库订单请求入参数
 * <AUTHOR>
 */
public class InventoryOrderRequestDTO implements Serializable {
    private static final long serialVersionUID = 4418331966283707807L;
    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 医院机构
     */
    private String orgCode;

    /**
     * 出入库类型
     */
    private InventoryStatusEnum inventoryStatus;

    /**
     * 操作人id
     */
    private Integer operatorId;

    /**
     * 操作类型
     */
    private OrderApprovalEnum operatingStatus;

    /**
     * 操作人分类
     */
    private StatementOperatingEnum operatingType;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public InventoryStatusEnum getInventoryStatus() {
        return inventoryStatus;
    }

    public void setInventoryStatus(InventoryStatusEnum inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public OrderApprovalEnum getOperatingStatus() {
        return operatingStatus;
    }

    public void setOperatingStatus(OrderApprovalEnum operatingStatus) {
        this.operatingStatus = operatingStatus;
    }

    public StatementOperatingEnum getOperatingType() {
        return operatingType;
    }

    public void setOperatingType(StatementOperatingEnum operatingType) {
        this.operatingType = operatingType;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("InventoryOrderRequestDTO{");
        sb.append("orderId=").append(orderId);
        sb.append(", orgCode='").append(orgCode).append('\'');
        sb.append(", inventoryStatus=").append(inventoryStatus);
        sb.append(", operatorId=").append(operatorId);
        sb.append(", operatingStatus=").append(operatingStatus);
        sb.append(", operatingType=").append(operatingType);
        sb.append('}');
        return sb.toString();
    }
}
