package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.StringJoiner;

/**
 * @description: 订单商品打印详情
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/9/16 18:36
 **/
public class OrderDetailPrintDTO implements Serializable {
    private static final long serialVersionUID = 5736836709196539810L;
    /**
     * 商品规格
     */
    @RpcModelProperty("商品规格")
    private String specifications;

    /**
     * 商品名称
     */
    @RpcModelProperty("商品名称")
    private String productName;

    /**
     * 一级分类名标签
     */
    @RpcModelProperty("分类标签")
    private String categoryName;

    /**
     * 一级分类Id
     */
    @RpcModelProperty("一级分类Id")
    private Integer firstCategoryId;

    /**
     * 一级分类名称
     */
    @RpcModelProperty("一级分类名称")
    private String firstCategoryName;

    /**
     * 二级分类名标签
     */
    @RpcModelProperty("二级分类名标签")
    private String secondCategoryName;

    /**
     * 品牌名称
     */
    @RpcModelProperty("品牌名称")
    private String brandName;

    /**
     * 商品货号
     */
    @RpcModelProperty("商品货号")
    private String productCode;

    /**
     * cas号
     */
    @RpcModelProperty("cas号")
    private String casNo;

    /**
     * 单位
     */
    @RpcModelProperty("单位")
    private String unit;

    /**
     * 数量
     */
    @RpcModelProperty("数量")
    private BigDecimal quantity;

    /**
     * 商品单价
     */
    @RpcModelProperty("商品单价")
    private BigDecimal price;

    /**
     * 减去退货金额后的商品总价
     */
    @RpcModelProperty("商品总价")
    private BigDecimal totalAmount;

    /**
     * 费用类型
     */
    @RpcModelProperty("费用类型")
    private String feeType;

    @RpcModelProperty("总价-单价*数量 剩余的那个价格，团购优惠衍生物")
    private BigDecimal remainderPrice;

    @RpcModelProperty("商品采购用途")
    private String purpose;

    /**
     * 只有江苏中西医用
     */
    @RpcModelProperty("库存数量--下单时库房对应商品数量")
    private BigDecimal inventoryQuantity;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 订单详情id
     */
    private Integer detailId;

    /**
     * 订单批次信息
     */
    private List<OrderBatchesPrintDTO> orderBatchesPrintDTOList;

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getSecondCategoryName() {
        return secondCategoryName;
    }

    public String getFirstCategoryName() {
        return firstCategoryName;
    }

    public void setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
    }

    public OrderDetailPrintDTO setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
        return this;
    }

    public BigDecimal getRemainderPrice() {
        return remainderPrice;
    }

    public void setRemainderPrice(BigDecimal remainderPrice) {
        this.remainderPrice = remainderPrice;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public BigDecimal getInventoryQuantity() {
        return inventoryQuantity;
    }

    public OrderDetailPrintDTO setInventoryQuantity(BigDecimal inventoryQuantity) {
        this.inventoryQuantity = inventoryQuantity;
        return this;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public List<OrderBatchesPrintDTO> getOrderBatchesPrintDTOList() {
        return orderBatchesPrintDTOList;
    }

    public OrderDetailPrintDTO setOrderBatchesPrintDTOList(List<OrderBatchesPrintDTO> orderBatchesPrintDTOList) {
        this.orderBatchesPrintDTOList = orderBatchesPrintDTOList;
        return this;
    }

    public Integer getFirstCategoryId() {
        return firstCategoryId;
    }

    public OrderDetailPrintDTO setFirstCategoryId(Integer firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderDetailPrintDTO.class.getSimpleName() + "[", "]")
                .add("specifications='" + specifications + "'")
                .add("productName='" + productName + "'")
                .add("categoryName='" + categoryName + "'")
                .add("firstCategoryId='" + firstCategoryId + "'")
                .add("firstCategoryName='" + firstCategoryName + "'")
                .add("secondCategoryName='" + secondCategoryName + "'")
                .add("brandName='" + brandName + "'")
                .add("productCode='" + productCode + "'")
                .add("casNo='" + casNo + "'")
                .add("unit='" + unit + "'")
                .add("quantity=" + quantity)
                .add("price=" + price)
                .add("totalAmount=" + totalAmount)
                .add("feeType='" + feeType + "'")
                .add("remainderPrice=" + remainderPrice)
                .add("purpose='" + purpose + "'")
                .add("inventoryQuantity=" + inventoryQuantity)
                .add("productId=" + productId)
                .add("detailId=" + detailId)
                .add("orderBatchesPrintDTOList=" + orderBatchesPrintDTOList)
                .toString();
    }
}
