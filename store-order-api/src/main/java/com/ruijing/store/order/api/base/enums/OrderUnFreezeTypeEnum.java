package com.ruijing.store.order.api.base.enums;

/**
 * 订单解冻的操作类型枚举
 */
public enum OrderUnFreezeTypeEnum {

    CHANGE_TO_SELF_STATEMENT("订单改为自结算操作"),

    CANCEL("取消订单操作"),
    RETURN("整单退货操作"),
    RETURN_PARTIALLY("部分退货操作"),
    ;
    private String description;

    OrderUnFreezeTypeEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
