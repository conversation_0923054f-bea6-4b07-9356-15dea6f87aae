package com.ruijing.store.order.api.base.orderdetail.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.orderdetail.dto.*;
import com.ruijing.store.order.api.search.dto.StatisticsManagerParamDTO;

import java.util.List;
import java.util.Set;

/**
 * @description: 订单详情 服务
 * @author: zhuk
 * @create: 2019-06-28 17:15
 **/
@RpcApi("订单详情 RPC 接口")
public interface OrderDetailService {

    /**
     * 根据 ordermasterId 查询 对应的订单详情
     * @param orderDetailReq
     * @return
     */
    @RpcMethod("根据 ordermasterId 查询 对应的订单详情")
    RemoteResponse<List<OrderDetailDTO>> findOrderDetailsByMasterId(OrderDetailReq orderDetailReq);

    /**
     * 根据 ordermasterId 数组查询 对应的订单详情
     * @return
     */
    @RpcMethod("根据 ordermasterId 数组查询 对应的订单详情, orderMasterIdList 数组长度不可超500")
    RemoteResponse<List<OrderDetailDTO>> findOrderDetailByMasterIdList(OrderDetailReq orderDetailReq);

    /**
     * 检测订单商品的分类，一级分类, 返回订单商品包含的所有一级分类
     * @param orderId
     * @return
     */
    @RpcMethod("检测订单商品的分类，一级分类, 返回订单商品包含的所有一级分类")
    RemoteResponse<Set<Integer>> findDetailTagValue(Integer orderId);

    /**
     * 根据detailId查找商品id
     * @param detailId
     * @return
     */
    @RpcMethod("根据detailId查找商品id")
    RemoteResponse<Long> findProductSnByDetailId(Integer detailId);

    /**
     * 谨慎使用！目前只有中大在使用；
     * 批量更新订单，采购申请单的商品名，用于将 待确认 的订单，采购申请单商品的的英文名改成中文，更新的条数不宜过多
     * @param request
     * @return
     */
    @RpcMethod("谨慎使用！目前只有中大在使用；批量更新订单，采购申请单的商品名，用于将 待确认 的订单，采购申请单商品的的英文名改成中文，更新的条数不宜过多")
    RemoteResponse updateGoodsNameById(OrderDetailReq request);

    /**
     * 批量更新订单明细
     * 目前只更新detail退货状态，退货数量和退货金额
     * @return
     */
    @RpcMethod("批量更新订单明细，限制数量200个; 目前只更新detail退货状态、退货数量、退货金额和productSn")
    RemoteResponse<Integer> updateByIdList(List<OrderDetailDTO> params);

    /**
     * 计算采购商品详情在课题组中的销量
     * @return
     */
    @RpcMethod("计算采购商品详情在课题组中的销量")
    RemoteResponse<List<OrderDetailStatisticsDTO>> orderDetailSaleStatistics(StatisticsManagerParamDTO request);

    /**
     * 根据 detailId 数组查询 对应的订单详情
     * @return
     */
    @RpcMethod("根据 detailId 数组查询 对应的订单详情, detailId 数组长度不可超500")
    RemoteResponse<List<OrderDetailDTO>> findOrderDetailByIdList(OrderDetailReq request);

    /**
     * 根据 orderDetailIdList 数组查询 对应的订单详情, orderDetailIdList 数组长度不可超200
     * @param request
     * @return
     */
    @RpcMethod("根据 orderDetailIdList 数组查询 对应的订单详情, orderDetailIdList 数组长度不可超200")
    RemoteResponse<List<DetailBatchesDTO>> findOrderDetailBatches(OrderDetailReq request);

    /**
     * 根据订单详情id的范围查询订单详情id列表（主要用于比对统计数据与实际订单数据的差异）
     * @param lowerDetailId
     * @param higherDetailId
     * @return
     */
    @RpcMethod("根据订单详情id的范围查询订单详情id列表")
    RemoteResponse<List<Integer>> findDetailIdListRange(Integer lowerDetailId, Integer higherDetailId);

    @RpcMethod("根据订单id获取商品详情(排除指定的退货状态)，也会返回绑定中的气瓶码（商品维度）。一次最多查询100个订单")
    RemoteResponse<List<OrderDetailDTO>> findDetailByOrderIdListExcludeReturnStatus(OrderDetailReq orderDetailReq);

    @RpcMethod("根据商品id修改商品信息快照，目前中大在用")
    RemoteResponse<Boolean> modifyOrderDetailProductSnapshotById(OrderDetailModifyRequestDTO orderDetailModifyRequestDTO);

    @RpcMethod("根据商品id批量修改商品信息快照，目前中大在用")
    RemoteResponse<Boolean> batchModifyOrderDetailSnapshotById(List<OrderDetailModifyRequestDTO> orderDetailModifyRequestDTOList);
}
