package com.ruijing.store.order.api.base.orderconfirm.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterCommonReqDTO;
import com.ruijing.store.order.api.base.other.dto.OrderConfirmForTheRecordDTO;

import java.util.List;

@RpcApi(value = "订单备案RPC接口")
public interface OrderConfirmRPCService {

    /**
     * 根据订单id数组查询订单备案信息, id.length <= 200
     * @return 订单备案信息
     */
    @RpcMethod("根据订单id数组查询订单备案信息, id.length <= 200")
    RemoteResponse<List<OrderConfirmForTheRecordDTO>> findOrderConfirmByOrderIdList(OrderMasterCommonReqDTO request);

    /**
     * 保存订单备案信息
     * @param request   备案入参
     * @return          成功数
     */
    @RpcMethod("保存订单备案信息")
    RemoteResponse<Integer> saveOrderConfirm(OrderConfirmForTheRecordDTO request);
}
