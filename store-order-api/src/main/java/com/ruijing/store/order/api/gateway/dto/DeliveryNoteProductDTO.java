package com.ruijing.store.order.api.gateway.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: z<PERSON><PERSON>
 * @date : 2020/9/2 4:40 下午
 * @description: 送货单商品详细信息
 */
public class DeliveryNoteProductDTO implements Serializable {

    private static final long serialVersionUID = 3921032227249926415L;

    @RpcModelProperty("货号")
    private String goodCode;

    @RpcModelProperty("商品名称")
    private String goodName;

    @RpcModelProperty("品牌名称")
    private String brandName;

    @RpcModelProperty("规格")
    private String spec;

    @RpcModelProperty("单位")
    private String unit;

    @RpcModelProperty("数量")
    private String quantity;

    @RpcModelProperty("单价")
    private String perPrice;

    @RpcModelProperty("总金额")
    private String totalAmount;

    @RpcModelProperty("总价-单价*数量 剩余的那个价格，团购优惠衍生物")
    private BigDecimal remainderPrice;

    public String getGoodCode() {
        return goodCode;
    }

    public void setGoodCode(String goodCode) {
        this.goodCode = goodCode;
    }

    public String getGoodName() {
        return goodName;
    }

    public void setGoodName(String goodName) {
        this.goodName = goodName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getPerPrice() {
        return perPrice;
    }

    public void setPerPrice(String perPrice) {
        this.perPrice = perPrice;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getRemainderPrice() {
        return remainderPrice;
    }

    public void setRemainderPrice(BigDecimal remainderPrice) {
        this.remainderPrice = remainderPrice;
    }
}
