package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;

/**
 * @author: zhukai
 * @date : 2020/1/16 7:28 下午
 * @description: 订单发票关联关系
 */
public class RefInvoiceOrderDTO implements Serializable {

    private static final long serialVersionUID = -2777128364383537239L;

    /**
     * PK
     */
    private String id;

    /**
     * 发票id
     */
    private Integer invoiceId;

    /**
     * 关系对象id
     */
    private String refId;

    /**
     * 关系对象类型（1：结算单；2：订单）
     */
    private Integer refType;

    /**
     * 结算单号或者订单号（获取信息方便）
     */
    private String refNumber;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public Integer getRefType() {
        return refType;
    }

    public void setRefType(Integer refType) {
        this.refType = refType;
    }

    public String getRefNumber() {
        return refNumber;
    }

    public void setRefNumber(String refNumber) {
        this.refNumber = refNumber;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", invoiceId=").append(invoiceId);
        sb.append(", refId=").append(refId);
        sb.append(", refType=").append(refType);
        sb.append(", refNumber=").append(refNumber);
        sb.append("]");
        return sb.toString();
    }

}
