package com.ruijing.store.order.api.base.ordermaster.service;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.common.OrderCommonPrintParamDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.*;
import com.ruijing.store.order.api.base.other.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * hms订单管理相关业务
 */
@RpcApi("订单管理RPC服务")
public interface OrderManageRpcService {
    /**
     * 解冻经费卡
     * @param orderBasicParamDTO
     * @return
     */
    RemoteResponse orderFundCardUnFreezeById(OrderBasicParamDTO orderBasicParamDTO);

    /**
     * 订单解冻经费卡标准接口
     * @param orderUnFreezeRequestDTO
     * @return
     */
    RemoteResponse orderFundCardUnFreeze(OrderUnFreezeRequestDTO orderUnFreezeRequestDTO);

    /**
     * 用户验收订单
     * @param orderReceiptParamDTO
     * @return
     */
    RemoteResponse userReceiptOrder(OrderReceiptParamDTO orderReceiptParamDTO);

    /**
     * 用户提交验收审批
     * @param orderApprovalParamDTO
     * @return
     */
    RemoteResponse submitOrderApproval(OrderApprovalParamDTO orderApprovalParamDTO);

    /**
     * 多级验收审批流完成，处理验收审批通过逻辑
     * @param orderApprovalParamDTO 验收审批参数
     * @return 是否成功
     */
    RemoteResponse<Boolean> acceptApproveFlowPass(OrderApprovalParamDTO orderApprovalParamDTO);

    /**
     * 江西肿瘤医院 验收 发送信息给采购人
     * @param orderId
     * @return
     */
    RemoteResponse sendReceiptMessageForJiangZhong(Integer orderId);

    /**
     * 根据用户信息查找用户超时 结算/验收 订单
     * @param timeOutOrderParamsDTO 入参
     * @return
     */
    RemoteResponse<BasePageResponseDTO<OrderMasterTimeOutDTO>> findTimeOutOrders(TimeOutOrderParamsDTO timeOutOrderParamsDTO);

    /**
     * 超时订单发起退货
     * @param params
     * @return
     */
    RemoteResponse applyGoodsReturnWithTimeOut(GoodsReturnParamDTO params);

    /**
     * 订单撤销退货
     * @param params
     * @return
     */
    RemoteResponse cancelGoodsReturnWithTimeOut(GoodsReturnParamDTO params);

    /**
     * 待结算订单推送数据接口
     */
    RemoteResponse pushWaitingStatement(OrderBasicParamDTO param);

    /**
     * 同意取消订单
     * @param param
     * @return
     */
    RemoteResponse agreeCancelOrder(OrderMasterDTO param);

    /**
     * 更新验收图片
     */
    RemoteResponse appendReceiveImages(AdditionalAcceptancePicDTO additionalAcceptancePicDTO);

    /**
     * 通过departmentId 查询中爆存储仓库列表
     * @param departmentId
     * @return
     */
    RemoteResponse<List<OrderStoreHouseDTO>> findStorageByDepartmentId(Integer departmentId);

    /**
     * 拒绝取消订单
     * @param cancelOrderReqDTO
     * @return
     */
    RemoteResponse refuseCancelOrder(CancelOrderReqDTO cancelOrderReqDTO);

    /**
     * 通知订单中管制品单位
     * @param orderNo
     * @return
     */
    RemoteResponse noticeRegulatoryByOrderNo(String orderNo);

    /**
     *同意取消订单
     */
    RemoteResponse agreeCancelOrder(CancelOrderReqDTO cancelOrderReqDTO);

    /**
     * 取消订单
     */
    RemoteResponse cancelOrder(ApplyCancelOrderReqDTO applyCancelOrderReqDTO);

    /**
     * 取消采购单关联的订单
     */
    RemoteResponse<Integer> cancelOrderByAppId(ApplyCancelOrderReqDTO applyCancelOrderReqDTO);

    /**
     * thunder调用取消订单
     * @param applyCancelOrderReqDTO 取消的参数 
     * @return RemoteResponse
     */
    RemoteResponse cancelOrderByThunder(ApplyCancelOrderReqDTO applyCancelOrderReqDTO);

    /**
     * 生成订单
     * @param generateOrderDTOS
     * @return
     */
    @RpcMethod("生成订单")
    RemoteResponse<List<GenerateOrderResultDTO>> generateOrder(List<GenerateOrderDTO> generateOrderDTOS);

    /**
     * 打印线下汇总单接口
     */
    RemoteResponse<List<PrintApproveDTO>> getOfflineSummaryPrintData(List<Integer> orderIdList);

    /**
     * 打印汇总单通用接口
     */
    RemoteResponse<List<PrintApproveDTO>> getCommonSummaryPrintData(List<Integer> orderIdList);

    /**
     * 供应商订单钱包扣费接口，目前只单独提供给华农使用, 只需订单id入参
     * @param request
     * @return
     */
    RemoteResponse noticeWaitingChargingOrder(OrderBasicParamDTO request);

    /**
     * 采购人取消线下单，目前是全平台的线下单用的取消订单接口
     * 待收货，待验收审批，待结算等状态都可以取消
     * @param request
     * @return
     */
    RemoteResponse cancelOfflineOrder(CancelOrderReqDTO request);

    /**
     * 通用的订单打印数据接口
     * @return
     * @param request
     */
    RemoteResponse<OrderCommonPrintDataDTO> getCommonPrintData(OrderCommonPrintParamDTO request);

    /**
     * 通过订单号重新推送订单信息到管理平台
     * @param orderNo
     * @return
     */
    RemoteResponse<Boolean> retryPushOrderByOrderNo(String orderNo);

    /**
     * 通过采购单id重新推送订单信息到管理平台
     * @param appId
     * @return
     */
    RemoteResponse<Integer> retryPushOrderByAppId(Integer appId);
}
