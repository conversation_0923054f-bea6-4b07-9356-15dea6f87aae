package com.ruijing.store.order.api.enums;

import java.util.Objects;

public enum SortOrderEnum {


    /**
     * asc
     */
    ASC(1, "asc"),

    /**
     * desc
     */
    DESC(2, "desc");

    private final int code;

    private final String name;

    SortOrderEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static SortOrderEnum getEnumByCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (SortOrderEnum sortOrderEnum : SortOrderEnum.values()) {
            if (Objects.equals(sortOrderEnum.getCode(), code)) {
                return sortOrderEnum;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return "SortOrder{" +
                "code=" + code +
                ", name='" + name + '\'' +
                '}';
    }
}
