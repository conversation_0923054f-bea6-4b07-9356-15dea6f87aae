package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 华农的经费信息dto
 * <AUTHOR>
 * @Date 2020/8/20 3:26 下午
 */
public class HuaNongFundInfoDTO implements Serializable {

    private static final long serialVersionUID = 3822938719657536176L;
    
    /**
     * 锐竞平台的单号
     */
    @RpcModelProperty("锐竞平台的单号")
    private String businessNo;

    /**
     * 第三方对接平台的单号
     */
    @RpcModelProperty("第三方对接平台的单号")
    private String dockingNo;

    /**
     * 经费编码
     */
    @RpcModelProperty("经费编码")
    private String fundCardCode;

    /**
     * 经费负责人
     */
    @RpcModelProperty("经费负责人")
    private String fundCardManagerName;

    /**
     * 收货人
     */
    @RpcModelProperty("收货人")
    private String receiverName;

    /**
     * 经办人
     */
    @RpcModelProperty("经办人")
    private String operatorName;

    /**
     * 采购下单时间
     */
    @RpcModelProperty("采购下单时间")
    private Date purchasedDate;

    /**
     * 审批人
     */
    @RpcModelProperty("审批人")
    private String approveName;

    /**
     * 审批通过时间
     */
    @RpcModelProperty("审批通过时间")
    private Date approvedDate;

    /**
     * 机构id
     */
    @RpcModelProperty("机构id")
    private Integer orgId;

    /**
     * 机构编码
     */
    @RpcModelProperty("机构编码")
    private String orgCode;

    /**
     * 备注
     */
    @RpcModelProperty("备注")
    private String remark;

    public String getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }

    public String getDockingNo() {
        return dockingNo;
    }

    public void setDockingNo(String dockingNo) {
        this.dockingNo = dockingNo;
    }

    public String getFundCardCode() {
        return fundCardCode;
    }

    public void setFundCardCode(String fundCardCode) {
        this.fundCardCode = fundCardCode;
    }

    public String getFundCardManagerName() {
        return fundCardManagerName;
    }

    public void setFundCardManagerName(String fundCardManagerName) {
        this.fundCardManagerName = fundCardManagerName;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getApproveName() {
        return approveName;
    }

    public void setApproveName(String approveName) {
        this.approveName = approveName;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getPurchasedDate() {
        return purchasedDate;
    }

    public void setPurchasedDate(Date purchasedDate) {
        this.purchasedDate = purchasedDate;
    }

    public Date getApprovedDate() {
        return approvedDate;
    }

    public void setApprovedDate(Date approvedDate) {
        this.approvedDate = approvedDate;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("HuaNongFundInfoDTO{");
        sb.append("businessNo='").append(businessNo).append('\'');
        sb.append(", dockingNo='").append(dockingNo).append('\'');
        sb.append(", fundCardCode='").append(fundCardCode).append('\'');
        sb.append(", fundCardManagerName='").append(fundCardManagerName).append('\'');
        sb.append(", receiverName='").append(receiverName).append('\'');
        sb.append(", approveName='").append(approveName).append('\'');
        sb.append(", orgId=").append(orgId);
        sb.append(", orgCode='").append(orgCode).append('\'');
        sb.append(", remark='").append(remark).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
