package com.ruijing.store.order.api.base.enums;

/**
 * 验收时入库配置
 */
public enum OrgReceiptStoreConfigEnum  {
    NO_STORE(0,"无需入库"),
    MANUAL_STORE(1,"手动入库"),
    AUTO_STORE(2,"自动入库");


    private OrgReceiptStoreConfigEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    private Integer value;
    private String name;

    public Integer getValue() {
        return this.value;
    }
}
