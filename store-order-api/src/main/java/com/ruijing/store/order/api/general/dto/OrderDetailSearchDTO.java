package com.ruijing.store.order.api.general.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @program: store-order-service
 * @description: 订单详情
 * @author: zhuk
 * @create: 2019-06-01 10:59
 **/
public class OrderDetailSearchDTO implements Serializable {
    private static final long serialVersionUID = -927037141359173424L;

    /**
     * fgoodcode : siB10005
     * fbrandid : null
     * fbidprice : 471
     * fquantity : 4
     * return_status : null
     * original_amount : 1884
     * fbrand : 锐博生物
     * fspec : 5nmol
     * fgoodname : 常规sirna合成
     * detail_id : 41136
     * original_price : 471
     */

    /**
     * 订单明细 id
     */
    private Integer detailId;

    /**
     * 商品货号
     */
    private String fgoodcode;

    /**
     * 商品名称
     */
    private String fgoodname;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 品牌id
     */
    private Integer fbrandid;

    /**
     * 品牌名称
     */
    private String fbrand;

    /**
     * 招标价格
     */
    private Double fbidprice;

    /**
     * 商品数量
     */
    private Integer fquantity;

    /**
     * 退货状态
     */
    private Integer returnStatus;

    /**
     * 原价
     */
    private Double originalAmount;

    /**
     * 规格
     */
    private String fspec;

    /**
     * 单价
     */
    private Double originalPrice;

    /**
     * 商品分类id
     */
    private Integer categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 采购目录id
     */
    private Integer categoryDirectoryId;

    /**
     * 招标总价
     */
    private Double fbidamount;

    /**
     * cas号
     */
    private String casNo;

    /**
     * 危化品类型id
     *
     */
    private Integer dangerousType;

    /**
     * 危化品标签
     */
    private String dangerousTypeName;

    /**
     * 管制品类型id
     */
    private Integer regulatoryType;

    /**
     * 管制品类型
     */
    private String regulatoryTypeName;

    /**
     * 单位
     */
    private String funit;

    /**
     * 退货数量
     */
    private Double fcancelquantity;

    /**
     * 退货金额
     */
    private Double returnAmount;

    /**
     * 实际数量
     */
    private Double actualQuantity;

    /**
     * 实际金额
     */
    private Double actualAmount;

    /**
     * 一级分类id
     */
    private Integer firstCategoryId;

    /**
     * 一级分类名称
     */
    private String firstCategoryName;

    /**
     * 二级分类id
     */
    private Integer secondCategoryId;

    /**
     * 二级分类名称
     */
    private String secondCategoryName;

    /**
     * 分类标签
     */
    private String categoryTag;

    /**
     * 报账类型
     */
    private String feeTypeTag;

    /**
     * 上传订单图片路径
     */
    private String picPath;

    /**
     * 是否已改价,0否，1是
     */
    private Integer modifyPrice;

    /**
     * 商品对应的供应商id
     */
    private Integer suppId;

    /**
     * 总价 - 单价*数量 剩余的小数
     */
    private BigDecimal remainderPrice;

    /**
     * 商品平台唯一编码
     */
    private String productCode;

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public String getFgoodcode() {
        return fgoodcode;
    }

    public void setFgoodcode(String fgoodcode) {
        this.fgoodcode = fgoodcode;
    }

    public String getFgoodname() {
        return fgoodname;
    }

    public void setFgoodname(String fgoodname) {
        this.fgoodname = fgoodname;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Integer getFbrandid() {
        return fbrandid;
    }

    public void setFbrandid(Integer fbrandid) {
        this.fbrandid = fbrandid;
    }

    public String getFbrand() {
        return fbrand;
    }

    public void setFbrand(String fbrand) {
        this.fbrand = fbrand;
    }

    public Double getFbidprice() {
        return fbidprice;
    }

    public void setFbidprice(Double fbidprice) {
        this.fbidprice = fbidprice;
    }

    public Integer getFquantity() {
        return fquantity;
    }

    public void setFquantity(Integer fquantity) {
        this.fquantity = fquantity;
    }

    public Integer getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(Integer returnStatus) {
        this.returnStatus = returnStatus;
    }

    public Double getOriginalAmount() {
        return originalAmount;
    }

    public void setOriginalAmount(Double originalAmount) {
        this.originalAmount = originalAmount;
    }

    public String getFspec() {
        return fspec;
    }

    public void setFspec(String fspec) {
        this.fspec = fspec;
    }

    public Double getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(Double originalPrice) {
        this.originalPrice = originalPrice;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Integer getCategoryDirectoryId() {
        return categoryDirectoryId;
    }

    public void setCategoryDirectoryId(Integer categoryDirectoryId) {
        this.categoryDirectoryId = categoryDirectoryId;
    }

    public Double getFbidamount() {
        return fbidamount;
    }

    public void setFbidamount(Double fbidamount) {
        this.fbidamount = fbidamount;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public Integer getDangerousType() {
        return dangerousType;
    }

    public void setDangerousType(Integer dangerousType) {
        this.dangerousType = dangerousType;
    }

    public Integer getRegulatoryType() {
        return regulatoryType;
    }

    public void setRegulatoryType(Integer regulatoryType) {
        this.regulatoryType = regulatoryType;
    }

    public String getFunit() {
        return funit;
    }

    public void setFunit(String funit) {
        this.funit = funit;
    }

    public Double getFcancelquantity() {
        return fcancelquantity;
    }

    public void setFcancelquantity(Double fcancelquantity) {
        this.fcancelquantity = fcancelquantity;
    }

    public Double getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(Double returnAmount) {
        this.returnAmount = returnAmount;
    }

    public Double getActualQuantity() {
        return actualQuantity;
    }

    public void setActualQuantity(Double actualQuantity) {
        this.actualQuantity = actualQuantity;
    }

    public Double getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(Double actualAmount) {
        this.actualAmount = actualAmount;
    }

    public String getDangerousTypeName() {
        return dangerousTypeName;
    }

    public void setDangerousTypeName(String dangerousTypeName) {
        this.dangerousTypeName = dangerousTypeName;
    }

    public String getRegulatoryTypeName() {
        return regulatoryTypeName;
    }

    public void setRegulatoryTypeName(String regulatoryTypeName) {
        this.regulatoryTypeName = regulatoryTypeName;
    }

    public Integer getFirstCategoryId() {
        return firstCategoryId;
    }

    public void setFirstCategoryId(Integer firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
    }

    public String getFirstCategoryName() {
        return firstCategoryName;
    }

    public void setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
    }

    public Integer getSecondCategoryId() {
        return secondCategoryId;
    }

    public void setSecondCategoryId(Integer secondCategoryId) {
        this.secondCategoryId = secondCategoryId;
    }

    public String getSecondCategoryName() {
        return secondCategoryName;
    }

    public void setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
    }

    public String getCategoryTag() {
        return categoryTag;
    }

    public void setCategoryTag(String categoryTag) {
        this.categoryTag = categoryTag;
    }

    public String getFeeTypeTag() {
        return feeTypeTag;
    }

    public void setFeeTypeTag(String feeTypeTag) {
        this.feeTypeTag = feeTypeTag;
    }

    public String getPicPath() {
        return picPath;
    }

    public OrderDetailSearchDTO setPicPath(String picPath) {
        this.picPath = picPath;
        return this;
    }

    public Integer getModifyPrice() {
        return modifyPrice;
    }

    public OrderDetailSearchDTO setModifyPrice(Integer modifyPrice) {
        this.modifyPrice = modifyPrice;
        return this;
    }

    public Integer getSuppId() {
        return suppId;
    }

    public void setSuppId(Integer suppId) {
        this.suppId = suppId;
    }

    public BigDecimal getRemainderPrice() {
        return remainderPrice;
    }

    public void setRemainderPrice(BigDecimal remainderPrice) {
        this.remainderPrice = remainderPrice;
    }

    public String getProductCode() {
        return productCode;
    }

    public OrderDetailSearchDTO setProductCode(String productCode) {
        this.productCode = productCode;
        return this;
    }

    @Override
    public String toString() {
        return "OrderDetailSearchDTO{" +
                "detailId=" + detailId +
                ", fgoodcode='" + fgoodcode + '\'' +
                ", fgoodname='" + fgoodname + '\'' +
                ", productId=" + productId +
                ", fbrandid=" + fbrandid +
                ", fbrand='" + fbrand + '\'' +
                ", fbidprice=" + fbidprice +
                ", fquantity=" + fquantity +
                ", returnStatus=" + returnStatus +
                ", originalAmount=" + originalAmount +
                ", fspec='" + fspec + '\'' +
                ", originalPrice=" + originalPrice +
                ", categoryId=" + categoryId +
                ", categoryName='" + categoryName + '\'' +
                ", categoryDirectoryId=" + categoryDirectoryId +
                ", fbidamount=" + fbidamount +
                ", casNo='" + casNo + '\'' +
                ", dangerousType=" + dangerousType +
                ", dangerousTypeName='" + dangerousTypeName + '\'' +
                ", regulatoryType=" + regulatoryType +
                ", regulatoryTypeName='" + regulatoryTypeName + '\'' +
                ", funit='" + funit + '\'' +
                ", fcancelquantity=" + fcancelquantity +
                ", returnAmount=" + returnAmount +
                ", actualQuantity=" + actualQuantity +
                ", actualAmount=" + actualAmount +
                ", firstCategoryId=" + firstCategoryId +
                ", firstCategoryName='" + firstCategoryName + '\'' +
                ", secondCategoryId=" + secondCategoryId +
                ", secondCategoryName='" + secondCategoryName + '\'' +
                ", categoryTag='" + categoryTag + '\'' +
                ", feeTypeTag='" + feeTypeTag + '\'' +
                ", picPath='" + picPath + '\'' +
                ", modifyPrice=" + modifyPrice +
                ", suppId=" + suppId +
                ", remainderPrice=" + remainderPrice +
                ", productCode='" + productCode + '\'' +
                '}';
    }
}
