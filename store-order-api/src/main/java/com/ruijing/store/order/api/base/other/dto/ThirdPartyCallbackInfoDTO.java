package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 第三方接口回调信息体
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/9/30 14:21
 **/
public class ThirdPartyCallbackInfoDTO implements Serializable {


    private static final long serialVersionUID = 2265071056297783660L;

    private Integer id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 单位编号
     */
    private String orgcode;

    /**
     * 单据编号
     */
    private String num;

    /**
     * 信息摘要
     */
    private String message;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOrgcode() {
        return orgcode;
    }

    public void setOrgcode(String orgcode) {
        this.orgcode = orgcode;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "ThirdPartyCallbackInfoDTO{" +
                "id=" + id +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", orgcode='" + orgcode + '\'' +
                ", num='" + num + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
}
