package com.ruijing.store.order.api.base.orderdetail.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

public class OrderDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *订单详情id
     */
    private Integer id;

    /**
     * 订单id
     */
    private Integer fmasterid;

    /**
     * 招标日期
     */
    private Date fbiddate;

    /**
     * 订单b编号
     */
    private String fdetailno;

    /**
     *分类id
     */
    private Integer categoryid;

    /**
     * 商品分类
     */
    private String fclassification;

    /**
     * SPU，即商品下的唯一编码。规格不同，SPU也相同。
     */
    @ModelProperty("SPU,同一种商品下的唯一编码（规格不同，SPU也相同）")
    private String fgoodcode;

    /**
     * 原货号，SKU，平台编码。同一个商品在不同规格下的唯一编码
     */
    @ModelProperty("SKU,平台编码。同一个商品在不同规格下的唯一编码")
    private String productCode;

    /**
     * 商品名称
     */
    private String fgoodname;

    /**
     * 参考品牌
     */
    private String fbrand;

    /**
     * 规格
     */
    private String fspec;

    /**
     * 单位
     */
    private String funit;

    /**
     * 数量
     */
    private BigDecimal fquantity;

    /**
     * 招标价格
     */
    private BigDecimal fbidprice;

    /**
     * 招标总价格
     */
    private BigDecimal fbidamount;

    /**
     * 图片位置
     */
    private String fpicpath;

    private BigDecimal fremainquantity;

    private Integer fbrandid;

    private Integer tsuppmerpassid;

    private BigDecimal fcancelquantity;

    private BigDecimal fcancelamount;

    /**
     *产品id
     */
    private Long productSn;

    /**
     * 退货状态
     */
    private Integer returnStatus;

    /**
     * 退货状态
     */
    private Double returnAmount;

    /**
     * 商品项 原价
     */
    private BigDecimal originalAmount;

    /**
     * 商品单价
     */
    private BigDecimal originalPrice;

    /**
     * 是否修改价格
     */
    private Boolean modifyPrice;

    /**
     * 货期
     */
    private Integer deliveryTime;

    /**
     * 优惠券  商品项余额
     */
    private BigDecimal remainderPrice;

    /**
     * 协议价
     */
    private BigDecimal negotiatedPrice;

    /**
     * 中大二级分类id
     */
    private Integer sysuCategoryId;

    /**
     * 采购目录ID
     */
    private Integer categoryDirectoryId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     *
     */
    private BigDecimal carryFee;

    /**
     * 一级分类id
     */
    private Integer firstCategoryId;

    /**
     * 一级分类名称
     */
    private String firstCategoryName;

    /**
     * 二级分类id
     */
    private Integer secondCategoryId;

    /**
     * 二级分类名称
     */
    private String secondCategoryName;

    /**
     * 报账类型
     *
     * tag_type = 0;
     * tag_value  com.reagent.tags.global.enums.FeeType
     */
    private String feeTypeTag;

    /**
     * 分类标签
     * tag_type = 1;
     * com.reagent.tags.global.enums.FirstTierCategory
     */
    private String categoryTag;

    /**
     * 危化品标签Id
     */
    private Integer dangerousTypeId;

    /**
     * 危化品标签名称
     */
    private String dangerousTypeName;

    /**
     * 管制品类型Id
     */
    private Integer regulatoryTypeId;

    /**
     *管制品类型名称
     */
    private String regulatoryTypeName;

    /**
     *Cas号
     */
    private String casno;

    /**
     *供应商Id
     */
    private Integer suppId;

    /**
     *供应商名称
     */
    private String suppName;

    /**
     * 供应商编号
     */
    private String suppCode;

    /**
     * 品牌英文名
     */
    private String brandEname;

    /**
     * 订单详情额外信息
     */
    private List<OrderDetailExtraDTO> orderDetailExtraDTOList;

    /**
     * 绑定中的气瓶码
     */
    @RpcModelProperty("绑定中的气瓶码，特定方法才会返回")
    private List<String> bindGasBottleBarcodes;

    @RpcModelProperty("商品多规格")
    private String attribute;

    @RpcModelProperty("包装规格")
    private String packingSpec;

    @RpcModelProperty("型号")
    private String modelNumber;

    @RpcModelProperty(value = "注册编码", description = "医疗器械注册证书编号")
    private String medicalDeviceRegisCertNumber;

    @RpcModelProperty("完成周期")
    private String completionCycle;

    @RpcModelProperty("出版社")
    private String press;

    @RpcModelProperty("纯度/浓度")
    private String purity;

    @RpcModelProperty("产品规格")
    private String productSpec;


    public Integer getCategoryDirectoryId() {
        return categoryDirectoryId;
    }

    public void setCategoryDirectoryId(Integer categoryDirectoryId) {
        this.categoryDirectoryId = categoryDirectoryId;
    }

    public Integer getId() {
        return id;
    }

    public Integer getFmasterid() {
        return fmasterid;
    }

    public Date getFbiddate() {
        return fbiddate;
    }

    public String getFdetailno() {
        return fdetailno;
    }

    public void setFdetailno(String fdetailno) {
        this.fdetailno = fdetailno;
    }

    public Integer getCategoryid() {
        return categoryid;
    }

    public String getFclassification() {
        return fclassification;
    }

    public String getFgoodcode() {
        return fgoodcode;
    }

    public String getProductCode() {
        return productCode;
    }

    public String getFgoodname() {
        return fgoodname;
    }

    public String getFbrand() {
        return fbrand;
    }

    public String getFspec() {
        return fspec;
    }

    public String getFunit() {
        return funit;
    }

    public BigDecimal getFquantity() {
        return fquantity;
    }

    public BigDecimal getFbidprice() {
        return fbidprice;
    }

    public BigDecimal getFbidamount() {
        return fbidamount;
    }

    public String getFpicpath() {
        return fpicpath;
    }

    public BigDecimal getFremainquantity() {
        return fremainquantity;
    }

    public Integer getFbrandid() {
        return fbrandid;
    }

    public Integer getTsuppmerpassid() {
        return tsuppmerpassid;
    }

    public BigDecimal getFcancelquantity() {
        return fcancelquantity;
    }

    public BigDecimal getFcancelamount() {
        return fcancelamount;
    }

    public Long getProductSn() {
        return productSn;
    }

    public Integer getReturnStatus() {
        return returnStatus;
    }

    public Double getReturnAmount() {
        return returnAmount;
    }

    public BigDecimal getOriginalAmount() {
        return originalAmount;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public Boolean getModifyPrice() {
        return modifyPrice;
    }

    public Integer getDeliveryTime() {
        return deliveryTime;
    }

    public BigDecimal getRemainderPrice() {
        return remainderPrice;
    }

    public BigDecimal getNegotiatedPrice() {
        return negotiatedPrice;
    }

    public Integer getSysuCategoryId() {
        return sysuCategoryId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public BigDecimal getCarryFee() {
        return carryFee;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setFmasterid(Integer fmasterid) {
        this.fmasterid = fmasterid;
    }

    public void setFbiddate(Date fbiddate) {
        this.fbiddate = fbiddate;
    }

    public void setCategoryid(Integer categoryid) {
        this.categoryid = categoryid;
    }

    public void setFclassification(String fclassification) {
        this.fclassification = fclassification;
    }

    public void setFgoodcode(String fgoodcode) {
        this.fgoodcode = fgoodcode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public void setFgoodname(String fgoodname) {
        this.fgoodname = fgoodname;
    }

    public void setFbrand(String fbrand) {
        this.fbrand = fbrand;
    }

    public void setFspec(String fspec) {
        this.fspec = fspec;
    }

    public void setFunit(String funit) {
        this.funit = funit;
    }

    public void setFquantity(BigDecimal fquantity) {
        this.fquantity = fquantity;
    }

    public void setFbidprice(BigDecimal fbidprice) {
        this.fbidprice = fbidprice;
    }

    public void setFbidamount(BigDecimal fbidamount) {
        this.fbidamount = fbidamount;
    }

    public void setFpicpath(String fpicpath) {
        this.fpicpath = fpicpath;
    }

    public void setFremainquantity(BigDecimal fremainquantity) {
        this.fremainquantity = fremainquantity;
    }

    public void setFbrandid(Integer fbrandid) {
        this.fbrandid = fbrandid;
    }

    public void setTsuppmerpassid(Integer tsuppmerpassid) {
        this.tsuppmerpassid = tsuppmerpassid;
    }

    public void setFcancelquantity(BigDecimal fcancelquantity) {
        this.fcancelquantity = fcancelquantity;
    }

    public void setFcancelamount(BigDecimal fcancelamount) {
        this.fcancelamount = fcancelamount;
    }

    public void setProductSn(Long productSn) {
        this.productSn = productSn;
    }

    public void setReturnStatus(Integer returnStatus) {
        this.returnStatus = returnStatus;
    }

    public void setReturnAmount(Double returnAmount) {
        this.returnAmount = returnAmount;
    }

    public void setOriginalAmount(BigDecimal originalAmount) {
        this.originalAmount = originalAmount;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public void setModifyPrice(Boolean modifyPrice) {
        this.modifyPrice = modifyPrice;
    }

    public void setDeliveryTime(Integer deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public void setRemainderPrice(BigDecimal remainderPrice) {
        this.remainderPrice = remainderPrice;
    }

    public void setNegotiatedPrice(BigDecimal negotiatedPrice) {
        this.negotiatedPrice = negotiatedPrice;
    }

    public void setSysuCategoryId(Integer sysuCategoryId) {
        this.sysuCategoryId = sysuCategoryId;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public void setCarryFee(BigDecimal carryFee) {
        this.carryFee = carryFee;
    }

    public Integer getFirstCategoryId() {
        return firstCategoryId;
    }

    public void setFirstCategoryId(Integer firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
    }

    public String getFirstCategoryName() {
        return firstCategoryName;
    }

    public void setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
    }

    public Integer getSecondCategoryId() {
        return secondCategoryId;
    }

    public void setSecondCategoryId(Integer secondCategoryId) {
        this.secondCategoryId = secondCategoryId;
    }

    public String getSecondCategoryName() {
        return secondCategoryName;
    }

    public void setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
    }

    public String getFeeTypeTag() {
        return feeTypeTag;
    }

    public void setFeeTypeTag(String feeTypeTag) {
        this.feeTypeTag = feeTypeTag;
    }

    public String getCategoryTag() {
        return categoryTag;
    }

    public void setCategoryTag(String categoryTag) {
        this.categoryTag = categoryTag;
    }

    public Integer getDangerousTypeId() {
        return dangerousTypeId;
    }

    public void setDangerousTypeId(Integer dangerousTypeId) {
        this.dangerousTypeId = dangerousTypeId;
    }

    public String getDangerousTypeName() {
        return dangerousTypeName;
    }

    public void setDangerousTypeName(String dangerousTypeName) {
        this.dangerousTypeName = dangerousTypeName;
    }

    public Integer getRegulatoryTypeId() {
        return regulatoryTypeId;
    }

    public void setRegulatoryTypeId(Integer regulatoryTypeId) {
        this.regulatoryTypeId = regulatoryTypeId;
    }

    public String getRegulatoryTypeName() {
        return regulatoryTypeName;
    }

    public void setRegulatoryTypeName(String regulatoryTypeName) {
        this.regulatoryTypeName = regulatoryTypeName;
    }

    public String getCasno() {
        return casno;
    }

    public void setCasno(String casno) {
        this.casno = casno;
    }

    public Integer getSuppId() {
        return suppId;
    }

    public void setSuppId(Integer suppId) {
        this.suppId = suppId;
    }

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    public String getSuppCode() {
        return suppCode;
    }

    public void setSuppCode(String suppCode) {
        this.suppCode = suppCode;
    }

    public String getBrandEname() {
        return brandEname;
    }

    public void setBrandEname(String brandEname) {
        this.brandEname = brandEname;
    }

    public List<OrderDetailExtraDTO> getOrderDetailExtraDTOList() {
        return orderDetailExtraDTOList;
    }

    public void setOrderDetailExtraDTOList(List<OrderDetailExtraDTO> orderDetailExtraDTOList) {
        this.orderDetailExtraDTOList = orderDetailExtraDTOList;
    }

    public List<String> getBindGasBottleBarcodes() {
        return bindGasBottleBarcodes;
    }

    public OrderDetailDTO setBindGasBottleBarcodes(List<String> bindGasBottleBarcodes) {
        this.bindGasBottleBarcodes = bindGasBottleBarcodes;
        return this;
    }

    public String getAttribute() {
        return attribute;
    }

    public void setAttribute(String attribute) {
        this.attribute = attribute;
    }


    public String getPackingSpec() {
        return packingSpec;
    }

    public OrderDetailDTO setPackingSpec(String packingSpec) {
        this.packingSpec = packingSpec;
        return this;
    }

    public String getModelNumber() {
        return modelNumber;
    }

    public OrderDetailDTO setModelNumber(String modelNumber) {
        this.modelNumber = modelNumber;
        return this;
    }

    public String getMedicalDeviceRegisCertNumber() {
        return medicalDeviceRegisCertNumber;
    }

    public OrderDetailDTO setMedicalDeviceRegisCertNumber(String medicalDeviceRegisCertNumber) {
        this.medicalDeviceRegisCertNumber = medicalDeviceRegisCertNumber;
        return this;
    }

    public String getCompletionCycle() {
        return completionCycle;
    }

    public OrderDetailDTO setCompletionCycle(String completionCycle) {
        this.completionCycle = completionCycle;
        return this;
    }

    public String getPress() {
        return press;
    }

    public OrderDetailDTO setPress(String press) {
        this.press = press;
        return this;
    }

    public String getPurity() {
        return purity;
    }

    public OrderDetailDTO setPurity(String purity) {
        this.purity = purity;
        return this;
    }

    public String getProductSpec() {
        return productSpec;
    }

    public OrderDetailDTO setProductSpec(String productSpec) {
        this.productSpec = productSpec;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderDetailDTO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("fmasterid=" + fmasterid)
                .add("fbiddate=" + fbiddate)
                .add("fdetailno='" + fdetailno + "'")
                .add("categoryid=" + categoryid)
                .add("fclassification='" + fclassification + "'")
                .add("fgoodcode='" + fgoodcode + "'")
                .add("productCode='" + productCode + "'")
                .add("fgoodname='" + fgoodname + "'")
                .add("fbrand='" + fbrand + "'")
                .add("fspec='" + fspec + "'")
                .add("funit='" + funit + "'")
                .add("fquantity=" + fquantity)
                .add("fbidprice=" + fbidprice)
                .add("fbidamount=" + fbidamount)
                .add("fpicpath='" + fpicpath + "'")
                .add("fremainquantity=" + fremainquantity)
                .add("fbrandid=" + fbrandid)
                .add("tsuppmerpassid=" + tsuppmerpassid)
                .add("fcancelquantity=" + fcancelquantity)
                .add("fcancelamount=" + fcancelamount)
                .add("productSn=" + productSn)
                .add("returnStatus=" + returnStatus)
                .add("returnAmount=" + returnAmount)
                .add("originalAmount=" + originalAmount)
                .add("originalPrice=" + originalPrice)
                .add("modifyPrice=" + modifyPrice)
                .add("deliveryTime=" + deliveryTime)
                .add("remainderPrice=" + remainderPrice)
                .add("negotiatedPrice=" + negotiatedPrice)
                .add("sysuCategoryId=" + sysuCategoryId)
                .add("categoryDirectoryId=" + categoryDirectoryId)
                .add("updateTime=" + updateTime)
                .add("carryFee=" + carryFee)
                .add("firstCategoryId=" + firstCategoryId)
                .add("firstCategoryName='" + firstCategoryName + "'")
                .add("secondCategoryId=" + secondCategoryId)
                .add("secondCategoryName='" + secondCategoryName + "'")
                .add("feeTypeTag='" + feeTypeTag + "'")
                .add("categoryTag='" + categoryTag + "'")
                .add("dangerousTypeId=" + dangerousTypeId)
                .add("dangerousTypeName='" + dangerousTypeName + "'")
                .add("regulatoryTypeId=" + regulatoryTypeId)
                .add("regulatoryTypeName='" + regulatoryTypeName + "'")
                .add("casno='" + casno + "'")
                .add("suppId=" + suppId)
                .add("suppName='" + suppName + "'")
                .add("suppCode='" + suppCode + "'")
                .add("brandEname='" + brandEname + "'")
                .add("orderDetailExtraDTOList=" + orderDetailExtraDTOList)
                .add("bindGasBottleBarcodes=" + bindGasBottleBarcodes)
                .add("attribute='" + attribute + "'")
                .add("packingSpec='" + packingSpec + "'")
                .add("modelNumber='" + modelNumber + "'")
                .add("medicalDeviceRegisCertNumber='" + medicalDeviceRegisCertNumber + "'")
                .add("completionCycle='" + completionCycle + "'")
                .add("press='" + press + "'")
                .add("purity='" + purity + "'")
                .add("productSpec='" + productSpec + "'")
                .toString();
    }
}

