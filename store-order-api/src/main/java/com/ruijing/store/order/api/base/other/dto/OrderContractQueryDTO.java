package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/15 11:47
 * @description
 */
@RpcMethod("订单合同查询参数")
public class OrderContractQueryDTO implements Serializable {

    private static final long serialVersionUID = 3585197167387990741L;
    
    @RpcModelProperty("订单id列表")
    private List<Integer> orderIdList;

    public List<Integer> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<Integer> orderIdList) {
        this.orderIdList = orderIdList;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderContractQueryDTO{");
        sb.append("orderIdList=").append(orderIdList);
        sb.append('}');
        return sb.toString();
    }
}
