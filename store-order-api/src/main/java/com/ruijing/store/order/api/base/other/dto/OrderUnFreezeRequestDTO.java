package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.store.order.api.base.enums.OrderUnFreezeTypeEnum;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.StringJoiner;

/**
 * 订单解冻经费标准dto入参
 */
public class OrderUnFreezeRequestDTO implements Serializable {

    private static final long serialVersionUID = 1015053242883609733L;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 订单解冻操作类型枚举
     */
    private OrderUnFreezeTypeEnum orderUnFreezeTypeEnum;

    /**
     * 退货单单/单据Id，当操作为部分退货操作时，此单号必填
     */
    private String returnId;

    /**
     * 解冻金额, orderUnFreezeTypeEnum 为退货时，FreezeAmount必填
     */
    private BigDecimal freezeAmount;

    /**
     * 用户id
     */
    private Integer operateUserId;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public OrderUnFreezeTypeEnum getOrderUnFreezeTypeEnum() {
        return orderUnFreezeTypeEnum;
    }

    public void setOrderUnFreezeTypeEnum(OrderUnFreezeTypeEnum orderUnFreezeTypeEnum) {
        this.orderUnFreezeTypeEnum = orderUnFreezeTypeEnum;
    }

    public String getReturnId() {
        return returnId;
    }

    public void setReturnId(String returnId) {
        this.returnId = returnId;
    }

    public BigDecimal getFreezeAmount() {
        return freezeAmount;
    }

    public void setFreezeAmount(BigDecimal freezeAmount) {
        this.freezeAmount = freezeAmount;
    }

    public Integer getOperateUserId() {
        return operateUserId;
    }

    public OrderUnFreezeRequestDTO setOperateUserId(Integer operateUserId) {
        this.operateUserId = operateUserId;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderUnFreezeRequestDTO.class.getSimpleName() + "[", "]")
                .add("orderId=" + orderId)
                .add("orderUnFreezeTypeEnum=" + orderUnFreezeTypeEnum)
                .add("returnId='" + returnId + "'")
                .add("freezeAmount=" + freezeAmount)
                .add("operateUserId=" + operateUserId)
                .toString();
    }
}
