package com.ruijing.store.order.api.base.enums;

/**
 * <AUTHOR>
 * @description：
 * @date ：Created in 2022-11-25 14:43
 */
public enum  DeliveryOperationEnum {
    SORTED(1, "上传分拣图片"),
    DELIVERED(2, "上传配送图片"),
    SORT_APPEND(3, "追加分拣图片"),
    DELIVERY_APPEND(4, "追加配送图片")
    ;
    /**
     * 编码
     */
    private Integer value;

    /**
     * 描述
     */
    private String description;

    DeliveryOperationEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    /**
     * @description: 通过枚举值获取枚举
     * @param value  枚举编码
     * @return      枚举描述
     */
    public static String getDescriptionByValue(Integer value) {
        for (DeliveryOperationEnum operationEnum : DeliveryOperationEnum.values()) {
            if (operationEnum.getValue().equals(value)) {
                return operationEnum.getDescription();
            }
        }
        return "";
    }

    public static DeliveryOperationEnum getByValue(Integer value) {
        for (DeliveryOperationEnum operationEnum : DeliveryOperationEnum.values()) {
            if (operationEnum.getValue().equals(value)) {
                return operationEnum;
            }
        }
        return null;
    }
}
