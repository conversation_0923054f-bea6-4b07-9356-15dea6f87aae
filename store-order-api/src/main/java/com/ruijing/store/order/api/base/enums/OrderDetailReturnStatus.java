package com.ruijing.store.order.api.base.enums;

/**
 * @description: 退货状态
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/10/23 10:01
 **/
public enum OrderDetailReturnStatus // 商品退货枚举
{
    NORETURN((Integer)null, "没有退货信息"),
    WAITINGFORCONFIRM(0, "待确认"),
    AGREETORETURN(1, "同意退货"),
    REFUSEDTORETURN(2, "拒绝退货"),
    //3是供应商未确认之前采购人撤销退货
    CANCELREQUEST_1(3, "取消申请_1"),
    RETURNEDGOODS(4, "退还货物"),
    SUCCESS(5, "成功"),
    //6是供应商确认后准备发货的采购人撤销退货
    CANCELREQUEST_2(6, "取消申请_2");

    private Integer code;
    private String status;

    OrderDetailReturnStatus(Integer code, String status) {
        this.code = code;
        this.status = status;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
