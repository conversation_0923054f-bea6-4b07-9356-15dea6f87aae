package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: z<PERSON><PERSON>
 * @date : 2019/12/20 10:39 上午
 * @description: 跟新订单 DTO
 */
public class UpdateOrderParamDTO implements Serializable {

    private static final long serialVersionUID = 6368198693179926556L;

    /**
     * 订单Id
     */
    @RpcModelProperty("订单Id")
    private Integer orderId;

    /**
     * 订单号
     */
    @RpcModelProperty("订单号")
    private String orderNo;

    /**
     * 订单状态
     */
    @RpcModelProperty("订单状态")
    private Integer status;

    /**
     * 结算单id
     */
    @RpcModelProperty("结算单id")
    private Integer statementId;

    /**
     * 结算时间
     */
    @RpcModelProperty("结算时间")
    private Date inStateTime;

    /**
     * 入库状态
     */
    @RpcModelProperty("入库状态")
    private Integer inventoryStatus;

    /**
     * 经费状态
     */
    @RpcModelProperty("经费状态")
    private Integer fundStatus;

    /**
     * 经费失败原因
     */
    @RpcModelProperty("经费失败原因")
    private String failedReason;

    /**
     * 验收图片
     */
    @RpcModelProperty("验收图片")
    private String receivePicUrls;

    /**
     * 订单关闭日期
     */
    @RpcModelProperty("订单关闭日期")
    private Date shutDownDate;

    /**
     * 收货时间
     */
    @RpcModelProperty("收货时间")
    private Date lastReceiveDate;

    /**
     * 订单确认时间
     */
    @RpcModelProperty("订单确认时间")
    private Date confirmDate;

    /**
     * 供应商确认人Id
     */
    @RpcModelProperty("供应商确认人Id")
    private String confirmManId;

    /**
     * 供应商确认人名称
     */
    @RpcModelProperty("供应商确认人名称")
    private String confirmMan;

    /**
     * 送货日期
     */
    @RpcModelProperty("送货日期")
    private Date deliveryDate;

    /**
     * 送货人姓名
     */
    @RpcModelProperty("送货人姓名")
    private String deliveryMan;

    /**
     * 送货人id
     */
    @RpcModelProperty("送货人id")
    private String deliveryManId;

    /**
     * 发货说明
     */
    @RpcModelProperty("发货说明")
    private String deliveryInfo;

    /**
     * 发货单号
     */
    @RpcModelProperty("发货单号")
    private String deliveryNo;

    /**
     * 结算子状态
     */
    @RpcModelProperty("结算子状态")
    private Integer statementStatus;

    /**
     * 退货金额，历史原因采用的是double类型
     */
    @RpcModelProperty("退货金额，历史原因采用的是double类型")
    private Double returnAmount;

    /**
     * 取消日期
     */
    @RpcModelProperty("取消日期")
    private Date cancelDate;

    /**
     * 取消原因
     */
    @RpcModelProperty("取消原因")
    private String cancelReason;

    /**
     * 收货人姓名
     */
    @RpcModelProperty("收货人姓名")
    private String lastReceiveMan;

    /**
     * 收货人id
     */
    @RpcModelProperty("收货人id")
    private String lastReceiveManId;

    /**
     * 收货地址
     */
    @RpcModelProperty("收货地址")
    private String deliveryPlace;

    /**
     * 采购联系人
     */
    @RpcModelProperty("采购联系人")
    private String buyerContactMan;

    /**
     * 采购人联系电话
     */
    @RpcModelProperty("采购人联系电话")
    private String buyerTelephone;

    @RpcModelProperty("更新时间，仅不需要更新更新时间的业务可设值")
    private Date updateTime;

    @RpcModelProperty("父一级部门id")
    private Integer deptParentId;

    @RpcModelProperty("父一级部门名称")
    private String deptParentName;

    @RpcModelProperty("订单完成时间")
    private Date finishDate;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatementId() {
        return statementId;
    }

    public void setStatementId(Integer statementId) {
        this.statementId = statementId;
    }

    public Date getInStateTime() {
        return inStateTime;
    }

    public void setInStateTime(Date inStateTime) {
        this.inStateTime = inStateTime;
    }

    public Integer getInventoryStatus() {
        return inventoryStatus;
    }

    public void setInventoryStatus(Integer inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
    }

    public Integer getFundStatus() {
        return fundStatus;
    }

    public void setFundStatus(Integer fundStatus) {
        this.fundStatus = fundStatus;
    }

    public String getFailedReason() {
        return failedReason;
    }

    public void setFailedReason(String failedReason) {
        this.failedReason = failedReason;
    }

    public String getReceivePicUrls() {
        return receivePicUrls;
    }

    public void setReceivePicUrls(String receivePicUrls) {
        this.receivePicUrls = receivePicUrls;
    }

    public Date getShutDownDate() {
        return shutDownDate;
    }

    public void setShutDownDate(Date shutDownDate) {
        this.shutDownDate = shutDownDate;
    }

    public Date getLastReceiveDate() {
        return lastReceiveDate;
    }

    public void setLastReceiveDate(Date lastReceiveDate) {
        this.lastReceiveDate = lastReceiveDate;
    }

    public Date getConfirmDate() {
        return confirmDate;
    }

    public void setConfirmDate(Date confirmDate) {
        this.confirmDate = confirmDate;
    }

    public String getConfirmManId() {
        return confirmManId;
    }

    public void setConfirmManId(String confirmManId) {
        this.confirmManId = confirmManId;
    }

    public String getConfirmMan() {
        return confirmMan;
    }

    public void setConfirmMan(String confirmMan) {
        this.confirmMan = confirmMan;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getDeliveryMan() {
        return deliveryMan;
    }

    public void setDeliveryMan(String deliveryMan) {
        this.deliveryMan = deliveryMan;
    }

    public String getDeliveryManId() {
        return deliveryManId;
    }

    public void setDeliveryManId(String deliveryManId) {
        this.deliveryManId = deliveryManId;
    }

    public String getDeliveryInfo() {
        return deliveryInfo;
    }

    public void setDeliveryInfo(String deliveryInfo) {
        this.deliveryInfo = deliveryInfo;
    }

    public String getDeliveryNo() {
        return deliveryNo;
    }

    public void setDeliveryNo(String deliveryNo) {
        this.deliveryNo = deliveryNo;
    }

    public Integer getStatementStatus() {
        return statementStatus;
    }

    public void setStatementStatus(Integer statementStatus) {
        this.statementStatus = statementStatus;
    }

    public Double getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(Double returnAmount) {
        this.returnAmount = returnAmount;
    }

    public Date getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(Date cancelDate) {
        this.cancelDate = cancelDate;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public String getLastReceiveMan() {
        return lastReceiveMan;
    }

    public void setLastReceiveMan(String lastReceiveMan) {
        this.lastReceiveMan = lastReceiveMan;
    }

    public String getLastReceiveManId() {
        return lastReceiveManId;
    }

    public void setLastReceiveManId(String lastReceiveManId) {
        this.lastReceiveManId = lastReceiveManId;
    }

    public String getDeliveryPlace() {
        return deliveryPlace;
    }

    public void setDeliveryPlace(String deliveryPlace) {
        this.deliveryPlace = deliveryPlace;
    }

    public String getBuyerContactMan() {
        return buyerContactMan;
    }

    public void setBuyerContactMan(String buyerContactMan) {
        this.buyerContactMan = buyerContactMan;
    }

    public String getBuyerTelephone() {
        return buyerTelephone;
    }

    public void setBuyerTelephone(String buyerTelephone) {
        this.buyerTelephone = buyerTelephone;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeptParentId() {
        return deptParentId;
    }

    public void setDeptParentId(Integer deptParentId) {
        this.deptParentId = deptParentId;
    }

    public String getDeptParentName() {
        return deptParentName;
    }

    public void setDeptParentName(String deptParentName) {
        this.deptParentName = deptParentName;
    }

    public Date getFinishDate() {
        return finishDate;
    }

    public void setFinishDate(Date finishDate) {
        this.finishDate = finishDate;
    }

    @Override
    public String toString() {
        return "UpdateOrderParamDTO{" +
                "orderId=" + orderId +
                ", orderNo='" + orderNo + '\'' +
                ", status=" + status +
                ", statementId=" + statementId +
                ", inStateTime=" + inStateTime +
                ", inventoryStatus=" + inventoryStatus +
                ", fundStatus=" + fundStatus +
                ", failedReason='" + failedReason + '\'' +
                ", receivePicUrls='" + receivePicUrls + '\'' +
                ", shutDownDate=" + shutDownDate +
                ", lastReceiveDate=" + lastReceiveDate +
                ", confirmDate=" + confirmDate +
                ", confirmManId='" + confirmManId + '\'' +
                ", confirmMan='" + confirmMan + '\'' +
                ", deliveryDate=" + deliveryDate +
                ", deliveryMan='" + deliveryMan + '\'' +
                ", deliveryManId='" + deliveryManId + '\'' +
                ", deliveryInfo='" + deliveryInfo + '\'' +
                ", deliveryNo='" + deliveryNo + '\'' +
                ", statementStatus=" + statementStatus +
                ", returnAmount=" + returnAmount +
                ", cancelDate=" + cancelDate +
                ", cancelReason='" + cancelReason + '\'' +
                ", lastReceiveMan='" + lastReceiveMan + '\'' +
                ", lastReceiveManId='" + lastReceiveManId + '\'' +
                ", deliveryPlace='" + deliveryPlace + '\'' +
                ", buyerContactMan='" + buyerContactMan + '\'' +
                ", buyerTelephone='" + buyerTelephone + '\'' +
                ", updateTime=" + updateTime +
                ", deptParentId=" + deptParentId +
                ", deptParentName='" + deptParentName + '\'' +
                ", finishDate=" + finishDate +
                '}';
    }
}
