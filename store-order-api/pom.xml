<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>store-order-service</artifactId>
        <groupId>com.ruijing.store</groupId>
        <version>1.0.6-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>store-order-api</artifactId>
    <name>store-order-api</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>msharp-api-info-bom</artifactId>
            <version>${msharp.version}</version>
            <type>pom</type>
        </dependency>

        <dependency>
            <groupId>com.ruijing.shop</groupId>
            <artifactId>shop-goods-api</artifactId>
            <version>1.0.7-SNAPSHOT</version>
        </dependency>
    </dependencies>
</project>