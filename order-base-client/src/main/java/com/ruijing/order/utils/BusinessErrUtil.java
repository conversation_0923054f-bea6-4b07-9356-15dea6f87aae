package com.ruijing.order.utils;

import com.ruijing.fundamental.api.annotation.Nullable;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.enums.IBaseTemplateEnum;
import com.ruijing.order.exception.BusinessInterceptException;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collection;

/**
 * @author: li<PERSON><PERSON>
 * @createTime: 2023-05-15 17:32
 * @description: 在Precondition基础上封装，简化抛出业务异常的代码。
 **/
public class BusinessErrUtil {

    public static void isTrue(boolean expression, String msg) {
        Preconditions.isTrue(expression, new BusinessInterceptException(msg));
    }

    public static void notEmpty(@Nullable Collection<?> collection, String message) {
        BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(collection), message);
    }

    public static void isEmpty(@Nullable Collection<?> collection, String message){
        BusinessErrUtil.isTrue(CollectionUtils.isEmpty(collection), message);
    }

    public static void notNull(@Nullable Object object, String message) {
        Preconditions.notNull(object, new BusinessInterceptException(message));
    }

    public static void isTrue(boolean expression, IBaseTemplateEnum templateEnum, Object... localeTemplateParams) {
        Preconditions.isTrue(expression, new BusinessInterceptException(templateEnum, localeTemplateParams));
    }

    public static void notEmpty(@Nullable Collection<?> collection, IBaseTemplateEnum templateEnum, Object... localeTemplateParams) {
        BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(collection), templateEnum, localeTemplateParams);
    }

    public static void isEmpty(@Nullable Collection<?> collection, IBaseTemplateEnum templateEnum, Object... localeTemplateParams) {
        BusinessErrUtil.isTrue(CollectionUtils.isEmpty(collection), templateEnum, localeTemplateParams);
    }

    public static void notNull(@Nullable Object object, IBaseTemplateEnum templateEnum, Object... localeTemplateParams) {
        Preconditions.notNull(object, new BusinessInterceptException(templateEnum, localeTemplateParams));
    }

}
