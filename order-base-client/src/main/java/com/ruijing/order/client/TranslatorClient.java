package com.ruijing.order.client;

import com.ruijing.base.translator.api.TranslatorTemplateRpcService;
import com.ruijing.base.translator.api.dto.TemplateDTO;
import com.ruijing.base.translator.api.dto.TemplateRenderDTO;
import com.ruijing.base.translator.api.dto.TemplateRenderParam;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;

/**
 * <AUTHOR>
 * @Description 国际化相关Client
 * @Date: 2024/10/22 14:49
 **/
@ServiceClient
public class TranslatorClient {

    @MSharpReference(remoteAppkey = "base-biz-translator-service")
    private TranslatorTemplateRpcService templateRpcService;

    @ServiceLog(description = "添加模板", serviceType = ServiceType.RPC_CLIENT)
    public Boolean addTemplate(TemplateDTO template) {
        RemoteResponse response = templateRpcService.addTemplate(template);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return true;
    }

    @ServiceLog(description = "更新模板", serviceType = ServiceType.RPC_CLIENT)
    public Boolean renderTemplate(TemplateDTO param) {
        RemoteResponse response = templateRpcService.updateTemplate(param);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return true;
    }

    @ServiceLog(description = "查询模板", serviceType = ServiceType.RPC_CLIENT)
    public RemoteResponse<TemplateDTO> queryTemplate(String code) {
        RemoteResponse<TemplateDTO> response = templateRpcService.queryTemplate(code);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response;
    }

    @ServiceLog(description = "多语言转换", serviceType = ServiceType.RPC_CLIENT)
    public TemplateRenderDTO render(TemplateRenderParam param) {
        RemoteResponse<TemplateRenderDTO> response = templateRpcService.render(param);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

}
