package com.ruijing.store.delivery.search;

import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.store.order.api.delivery.search.dto.DeliveryAggResultDTO;
import com.ruijing.store.order.api.delivery.search.dto.DeliveryDateHistogramResultDTO;
import com.ruijing.store.order.api.delivery.search.dto.DeliveryStatisticsRequestDTO;
import com.ruijing.store.order.api.search.dto.StatisticsManagerResultDTO;

import java.util.List;

/**
 * @author: liwenyu
 * @createTime: 2023-03-29 17:12
 * @description:
 **/
public interface DeliveryProxySearchService {

    /**
     * 聚合指定条件下的代配送订单
     * @param paramDTO 参数
     * @return 聚合结果
     */
    StatisticsManagerResultDTO aggTotal(DeliveryStatisticsRequestDTO paramDTO);

    /**
     * 根据聚合字段，聚合订单数量和总金额
     * @param paramDTO 聚合参数
     * @return 聚合结果
     */
    PageableResponse<List<DeliveryAggResultDTO>> aggOrderAmountAndCountByAggField(DeliveryStatisticsRequestDTO paramDTO);

    /**
     * 按照时间段聚合订单数量和金额（直方图）。如果传入aggField，会先按aggField聚合，再根据时间聚合（用于导出excel）
     * @param paramDTO 聚合参数
     * @return 聚合结果
     */
    PageableResponse<List<DeliveryDateHistogramResultDTO>> aggOrderAmountAndCountDateHistogram(DeliveryStatisticsRequestDTO paramDTO);
}
