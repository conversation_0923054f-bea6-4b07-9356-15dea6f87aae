package com.ruijing.store.order.gateway.buyercenter.request.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;
import com.ruijing.store.order.api.base.ordermaster.dto.AcceptAttachmentDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.AttachmentDTO;

import java.io.Serializable;
import java.util.List;


@Model("覆盖上传附件请求体")
public class OverrideUploadAttachmentRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty("订单id")
    private Integer orderId;

    @RpcModelProperty(value = "关联订单附件集合",description = "订单维度")
    private List<AttachmentDTO> attachmentList;

    @RpcModelProperty(value = "关联商品的附件集合",description = "商品维度")
    private List<AcceptAttachmentDTO> detailAttachmentDTOList;

    public Integer getOrderId() {
        return orderId;
    }

    public OverrideUploadAttachmentRequest setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }


    public List<AcceptAttachmentDTO> getDetailAttachmentDTOList() {
        return detailAttachmentDTOList;
    }

    public OverrideUploadAttachmentRequest setDetailAttachmentDTOList(List<AcceptAttachmentDTO> detailAttachmentDTOList) {
        this.detailAttachmentDTOList = detailAttachmentDTOList;
        return this;
    }

    public List<AttachmentDTO> getAttachmentList() {
        return attachmentList;
    }

    public OverrideUploadAttachmentRequest setAttachmentList(List<AttachmentDTO> attachmentList) {
        this.attachmentList = attachmentList;
        return this;
    }
}
