package com.ruijing.store.order.base.orderconfirm.service;

import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.store.order.api.base.other.dto.OrderConfirmForTheRecordDTO;
import com.ruijing.store.order.base.orderconfirm.dto.request.OrderConfirmForTheRecordRequest;
import com.ruijing.store.order.base.orderconfirm.dto.request.OrderConfirmRecordInfoRequest;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/8 09
 */
public interface OrderConfirmForTheRecordService {
    void confirmForTheRecord(RjSessionInfo rjSessionInfo, OrderConfirmForTheRecordRequest request);

    void addConfirmPics(RjSessionInfo rjSessionInfo, OrderConfirmForTheRecordRequest request);

    OrderConfirmForTheRecordDTO confirmRecordInfo(RjSessionInfo rjSessionInfo, OrderConfirmRecordInfoRequest request);
}
