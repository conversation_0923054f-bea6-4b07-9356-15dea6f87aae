package com.ruijing.store.order.business.service.constant;

import com.reagent.commonbase.constant.org.OrgConst;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.env.Environment;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/8 18:19
 * @description 个性化特殊订单单位-收货人配置
 */
public class CustomAcceptorConstant {

    /**
     * 正式环境管制品收货人配置
     */
    private final static Map<String, List<String>> PROD_REGULATORY_ORDER_ORG_USER_GUID_MAP = new HashMap<String, List<String>>() {{
        put(OrgConst.JIANG_XI_ZHONG_YI_YAO_DA_XUE, New.list("493906243249770505", "591303378743595009"));
    }};

    /**
     * 测试环境管制品收货人配置
     */
    private final static Map<String, List<String>> TEST_REGULATORY_ORDER_ORG_USER_GUID_MAP = new HashMap<String, List<String>>() {{
        put(OrgConst.JIANG_XI_ZHONG_YI_YAO_DA_XUE, New.list("1418019134687412224"));
    }};

    /**
     * 获取管制品特定接收人guid，无配置则返回null
     *
     * @param orgCode 单位code
     * @return 接收人guid
     */
    public static List<String> getRegulatoryAcceptorUserGuid(String orgCode) {
        return Environment.isProdEnv() ? PROD_REGULATORY_ORDER_ORG_USER_GUID_MAP.get(orgCode) : TEST_REGULATORY_ORDER_ORG_USER_GUID_MAP.get(orgCode);
    }

    /**
     * 江西肿瘤实验动物/科研服务指定验收人
     */
    public final static List<Integer> JIANG_XI_ZHONG_LIU_EXP_ANIMAL_OR_SERVICE_CUSTOM_ACCEPT_MAN_ID = Environment.isProdEnv() ? New.list(142012, 17316) : New.list(16749);
}
