package com.ruijing.store.order.gateway.oms.financial.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2023-04-12 11:23
 * @description:
 **/
@RpcModel("财务对接订单查询请求")
public class FinancialDockingOrderQueryRequestDTO implements Serializable {

    private static final long serialVersionUID = -489730785019815659L;

    @RpcModelProperty("采购单号")
    private String applicationNo;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("对接单号")
    private String dockingNumber;

    public String getApplicationNo() {
        return applicationNo;
    }

    public void setApplicationNo(String applicationNo) {
        this.applicationNo = applicationNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getDockingNumber() {
        return dockingNumber;
    }

    public void setDockingNumber(String dockingNumber) {
        this.dockingNumber = dockingNumber;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", FinancialDockingOrderQueryRequestDTO.class.getSimpleName() + "[", "]")
                .add("applicationNo='" + applicationNo + "'")
                .add("orderNo='" + orderNo + "'")
                .add("dockingNumber='" + dockingNumber + "'")
                .toString();
    }
}
