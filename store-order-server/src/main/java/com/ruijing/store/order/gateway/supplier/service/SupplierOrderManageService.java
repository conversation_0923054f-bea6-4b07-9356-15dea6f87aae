package com.ruijing.store.order.gateway.supplier.service;

import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderSplitUpRequestDTO;

public interface SupplierOrderManageService {

    /**
     * 拆分订单
     * @param request       拆单入参
     * @param rjSessionInfo 登陆会话
     * @return              拆分数量
     */
    default int orderSplitUp(OrderSplitUpRequestDTO request, RjSessionInfo rjSessionInfo) {
        return 0;
    }
}
