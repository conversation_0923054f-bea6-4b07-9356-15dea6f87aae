package com.ruijing.store.order.base.minor.translator;

import com.ruijing.store.order.api.base.other.dto.DangerousTagDTO;
import com.ruijing.store.order.api.base.other.dto.OrderConfirmForTheRecordDTO;
import com.ruijing.store.order.api.base.other.dto.OrderRemarkDTO;
import com.ruijing.store.order.api.base.other.dto.RefInvoiceOrderDTO;
import com.ruijing.store.order.base.minor.model.DangerousTagDO;
import com.ruijing.store.order.base.minor.model.OrderConfirmForTheRecordDO;
import com.ruijing.store.order.base.minor.model.OrderRemark;
import com.ruijing.store.order.base.minor.model.RefInvoiceOrder;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: OrderRemark转换类
 * @author: zhong<PERSON><PERSON>i
 * @create: 2019/10/18 10:27
 **/
public class OrderRelateTranslator {

    /**
     * 订单发票关联关系转换
     * @param refInvoiceOrder
     * @return
     */
    public static RefInvoiceOrderDTO refInvoiceOrder2DTO(RefInvoiceOrder refInvoiceOrder){
        RefInvoiceOrderDTO refInvoiceOrderDTO = new RefInvoiceOrderDTO();
        refInvoiceOrderDTO.setId(refInvoiceOrder.getId());
        refInvoiceOrderDTO.setInvoiceId(refInvoiceOrder.getInvoiceId());
        refInvoiceOrderDTO.setRefId(refInvoiceOrder.getRefId());
        refInvoiceOrderDTO.setRefType(refInvoiceOrder.getRefType());
        refInvoiceOrderDTO.setRefNumber(refInvoiceOrder.getRefNumber());
        return refInvoiceOrderDTO;
    }

    /**
     * 订单发票关联关系转换
     * @param dto
     * @return
     */
    public static RefInvoiceOrder dtoToRefInvoiceOrder2(RefInvoiceOrderDTO dto){
        RefInvoiceOrder refInvoiceOrder = new RefInvoiceOrder();
        refInvoiceOrder.setId(dto.getId());
        refInvoiceOrder.setInvoiceId(dto.getInvoiceId());
        refInvoiceOrder.setRefId(dto.getRefId());
        refInvoiceOrder.setRefType(dto.getRefType());
        refInvoiceOrder.setRefNumber(dto.getRefNumber());
        return refInvoiceOrder;
    }

    /**
     * OrderRemarkDTO 转换为 OrderRemark
     * @param orderRemarkDTOS
     * @return
     */
    public static List<OrderRemark> dto2OrderRemark(List<OrderRemarkDTO> orderRemarkDTOS) {
        List<OrderRemark> list = orderRemarkDTOS.stream().map(OrderRelateTranslator::dto2OrderRemark).collect(Collectors.toList());
        return list;
    }

    /**
     * OrderRemarkDTO 转换为 OrderRemark
     * @param dto
     * @return
     */
    public static OrderRemark dto2OrderRemark(OrderRemarkDTO dto) {
        OrderRemark orderRemark = new OrderRemark();
        orderRemark.setFtbuyappid(dto.getFtbuyappid());
        orderRemark.setFsuppid(dto.getFsuppid());
        orderRemark.setRemark(dto.getRemark());
        orderRemark.setCreationTime(dto.getCreationTime());
        orderRemark.setUpdateTime(dto.getUpdateTime());

        return orderRemark;
    }

    public static List<OrderRemarkDTO> orderRemark2DTO(List<OrderRemark> orderRemarkList) {
        List<OrderRemarkDTO> list = new ArrayList<>(orderRemarkList.size());

        for (OrderRemark orderRemark : orderRemarkList) {
            OrderRemarkDTO dto = new OrderRemarkDTO();
            dto.setFtbuyappid(orderRemark.getFtbuyappid());
            dto.setFsuppid(orderRemark.getFsuppid());
            dto.setRemark(orderRemark.getRemark());
            dto.setCreationTime(orderRemark.getCreationTime());
            dto.setUpdateTime(orderRemark.getUpdateTime());

            list.add(dto);
        }

        return list;
    }

    /**
     * 订单商品详情关联对危化品标签
     * DangerousTagDTO转DangerousTagDO
     * @param dangerousTagDTO
     * @return
     */
    public static DangerousTagDO dangerousTagDTO2DO(DangerousTagDTO dangerousTagDTO) {
            if (dangerousTagDTO == null){
                return null;
            }
            DangerousTagDO dangerousTagDO = new DangerousTagDO();
            dangerousTagDO.setBusinessId(dangerousTagDTO.getBusinessId());
            dangerousTagDO.setBusinessType(dangerousTagDTO.getBusinessType());
            dangerousTagDO.setCasNo(dangerousTagDTO.getCasNo());
            dangerousTagDO.setCreationTime(dangerousTagDTO.getCreationTime());
            dangerousTagDO.setDangerousType(dangerousTagDTO.getDangerousType());
            dangerousTagDO.setId(dangerousTagDTO.getId());
            dangerousTagDO.setRegulatoryType(dangerousTagDTO.getRegulatoryType());
            dangerousTagDO.setUpdateTime(dangerousTagDTO.getUpdateTime());

        return dangerousTagDO;
    }

    /**
     * 订单商品详情关联对危化品标签
     * DangerousTagDTO集合转DangerousTagDO集合
     * @param dangerousTagDTOS
     * @return
     */
    public static List<DangerousTagDO> dangerousTagDTO2DO(List<DangerousTagDTO> dangerousTagDTOS) {
        if (CollectionUtils.isEmpty(dangerousTagDTOS)) {
            return Collections.emptyList();
        }

        return dangerousTagDTOS.stream().map(r -> dangerousTagDTO2DO(r)).collect(Collectors.toList());
    }

    /**
     * 订单商品详情关联对危化品标签
     * DangerousTagDO转DangerousTagDTO
     * @param dangerousTagDO
     * @return
     */
    public static DangerousTagDTO dangerousTagDO2DTO(DangerousTagDO dangerousTagDO) {
        if (dangerousTagDO==null) {
            return null;
        }
        DangerousTagDTO dangerousTagDTO = new DangerousTagDTO();
        dangerousTagDTO.setBusinessId(dangerousTagDO.getBusinessId());
        dangerousTagDTO.setBusinessType(dangerousTagDO.getBusinessType());
        dangerousTagDTO.setCasNo(dangerousTagDO.getCasNo());
        dangerousTagDTO.setCreationTime(dangerousTagDO.getCreationTime());
        dangerousTagDTO.setDangerousType(dangerousTagDO.getDangerousType());
        dangerousTagDTO.setId(dangerousTagDO.getId());
        dangerousTagDTO.setRegulatoryType(dangerousTagDO.getRegulatoryType());
        dangerousTagDTO.setUpdateTime(dangerousTagDO.getUpdateTime());
        return dangerousTagDTO;
    }

    /**
     * 订单商品详情关联对危化品标签
     * DangerousTagDO集合转DangerousTagDTO集合
     * @param dangerousTagDOS
     * @return
     */
    public static List<DangerousTagDTO> dangerousTagDO2DTO(List<DangerousTagDO> dangerousTagDOS) {
        if (CollectionUtils.isEmpty(dangerousTagDOS)) {
            return Collections.emptyList();
        }

        return dangerousTagDOS.stream().map(r -> dangerousTagDO2DTO(r)).collect(Collectors.toList());
    }

    /**
     * 订单确认备案信息
     * OrderConfirmForTheRecordDTO转OrderConfirmForTheRecordDO
     * @param orderConfirmForTheRecordDTO
     * @return
     */
    public static OrderConfirmForTheRecordDO orderConfirmForTheRecordDTO2DO(OrderConfirmForTheRecordDTO orderConfirmForTheRecordDTO) {
        if (orderConfirmForTheRecordDTO == null) {
            return null;
        }
        OrderConfirmForTheRecordDO orderConfirmForTheRecordDO = new OrderConfirmForTheRecordDO();
        orderConfirmForTheRecordDO.setId(orderConfirmForTheRecordDTO.getId());
        orderConfirmForTheRecordDO.setAddPics(orderConfirmForTheRecordDTO.getAddPics());
        orderConfirmForTheRecordDO.setPics(orderConfirmForTheRecordDTO.getPics());
        orderConfirmForTheRecordDO.setCreationTime(orderConfirmForTheRecordDTO.getCreationTime());
        orderConfirmForTheRecordDO.setDeletionTime(orderConfirmForTheRecordDTO.getDeletionTime());
        orderConfirmForTheRecordDO.setConfirm(orderConfirmForTheRecordDTO.getConfirm());
        orderConfirmForTheRecordDO.setDeleted(orderConfirmForTheRecordDTO.getDeleted());
        orderConfirmForTheRecordDO.setOrderId(orderConfirmForTheRecordDTO.getOrderId());
        orderConfirmForTheRecordDO.setType(orderConfirmForTheRecordDTO.getType());
        orderConfirmForTheRecordDO.setUpdateTime(orderConfirmForTheRecordDTO.getUpdateTime());

        return orderConfirmForTheRecordDO;
    }

    /**
     * 订单确认备案信息
     * OrderConfirmForTheRecordDTO集合转OrderConfirmForTheRecordDO集合
     * @param orderConfirmForTheRecordDTOS
     * @return
     */
    public static List<OrderConfirmForTheRecordDO> orderConfirmForTheRecordDTO2DO(List<OrderConfirmForTheRecordDTO> orderConfirmForTheRecordDTOS) {
        if (CollectionUtils.isEmpty(orderConfirmForTheRecordDTOS)) {
            return Collections.emptyList();
        }

        return orderConfirmForTheRecordDTOS.stream().map(r -> orderConfirmForTheRecordDTO2DO(r)).collect(Collectors.toList());
    }

    /**
     * 订单确认备案信息
     * OrderConfirmForTheRecordDO转OrderConfirmForTheRecordDTO
     * @param orderConfirmForTheRecordDO
     * @return
     */
    public static OrderConfirmForTheRecordDTO orderConfirmForTheRecordDO2DTO(OrderConfirmForTheRecordDO orderConfirmForTheRecordDO) {
       if (orderConfirmForTheRecordDO == null) {
           return null;
       }
        OrderConfirmForTheRecordDTO orderConfirmForTheRecordDTO = new OrderConfirmForTheRecordDTO();
        orderConfirmForTheRecordDTO.setId(orderConfirmForTheRecordDO.getId());
        orderConfirmForTheRecordDTO.setAddPics(orderConfirmForTheRecordDO.getAddPics());
        orderConfirmForTheRecordDTO.setPics(orderConfirmForTheRecordDO.getPics());
        orderConfirmForTheRecordDTO.setCreationTime(orderConfirmForTheRecordDO.getCreationTime());
        orderConfirmForTheRecordDTO.setDeletionTime(orderConfirmForTheRecordDO.getDeletionTime());
        orderConfirmForTheRecordDTO.setConfirm(orderConfirmForTheRecordDO.getConfirm());
        orderConfirmForTheRecordDTO.setDeleted(orderConfirmForTheRecordDO.getDeleted());
        orderConfirmForTheRecordDTO.setOrderId(orderConfirmForTheRecordDO.getOrderId());
        orderConfirmForTheRecordDTO.setType(orderConfirmForTheRecordDO.getType());
        orderConfirmForTheRecordDTO.setUpdateTime(orderConfirmForTheRecordDO.getUpdateTime());
        return orderConfirmForTheRecordDTO;
    }

    /**
     * 订单确认备案信息
     * OrderConfirmForTheRecordDO集合转OrderConfirmForTheRecordDTO集合
     * @param orderConfirmForTheRecordDOS
     * @return
     */
    public static List<OrderConfirmForTheRecordDTO> orderConfirmForTheRecordDO2DTO(List<OrderConfirmForTheRecordDO> orderConfirmForTheRecordDOS) {
        if (CollectionUtils.isEmpty(orderConfirmForTheRecordDOS)) {
            return Collections.emptyList();
        }

        return orderConfirmForTheRecordDOS.stream().map(r -> orderConfirmForTheRecordDO2DTO(r)).collect(Collectors.toList());
    }
}
