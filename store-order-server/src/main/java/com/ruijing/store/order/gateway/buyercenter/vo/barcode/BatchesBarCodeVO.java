package com.ruijing.store.order.gateway.buyercenter.vo.barcode;

import com.ruijing.fundamental.api.annotation.ModelProperty;
import com.ruijing.store.order.gateway.buyercenter.vo.other.GasBottleVO;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-05-10 10:06
 * @description:
 */
public class BatchesBarCodeVO implements Serializable {
    
    private static final long serialVersionUID = 4440318664331501594L;
    /**
     * 条形码, 长整型
     */
    @ModelProperty("条形码")
    private String barCode;

    /**
     * 只有部分接口的中爆条形码返回，确保效率
     */
    @ModelProperty("条形码图片")
    private String barCodeImg;

    @ModelProperty("条形码的二维码形态图片，base64")
    private String barCodeQrImg;

    @ModelProperty(value = "条形码类型", enumLink = "com.ruijing.store.order.api.base.enums.UniqueBarCodeTypeEnum")
    private Integer type;

    /**
     * 批号
     */
    @ModelProperty("批号")
    private String batches;

    /**
     * 有效期
     */
    @ModelProperty("有效期")
    private String expiration;

    @ModelProperty("生产厂家")
    private String manufacturer;

    @ModelProperty("生产日期")
    private String productionDate;

    @ModelProperty("气瓶码")
    private String gasBottleBarcode;

    @ModelProperty("气瓶")
    private GasBottleVO gasBottle;

    @ModelProperty("数量")
    private Integer quantity;

    /**
     * 耐久度(外观) 0正常1破损
     */
    @ModelProperty("耐久度 0正常1破损")
    private Integer exterior;

    /**
     * 0：待录入批次
     * 1：待发货
     * 2：待收货
     * 3：待入库审批
     * 4：已入库
     * --入库驳回
     * 5：待出库审批
     * 6：已出库
     * 7：退货待确认
     * 8：取消退货
     * 9：同意退货
     * 10：退还货物
     * 11：已退货
     * 12: 拒绝退货
     */
    @ModelProperty("码的状态")
    private Integer status;

    @ModelProperty(value = "批次状态")
    private Integer batchesStatus;

    @ModelProperty(value = "交易状态")
    private Integer transactionStatus;

    @ModelProperty(value = "库房状态")
    private Integer inventoryStatus;

    /**
     * 打印状态, 0未1已打印
     */
    @ModelProperty("打印状态, 0未1已打印")
    private Integer printed;

    @ModelProperty("退货原因")
    private String returnReason;

    /**
     * 退货说明
     */
    @ModelProperty("退货说明")
    private String returnDescription;

    public String getBarCode() {
        return barCode;
    }

    public BatchesBarCodeVO setBarCode(String barCode) {
        this.barCode = barCode;
        return this;
    }

    public String getBarCodeImg() {
        return barCodeImg;
    }

    public BatchesBarCodeVO setBarCodeImg(String barCodeImg) {
        this.barCodeImg = barCodeImg;
        return this;
    }

    public String getBarCodeQrImg() {
        return barCodeQrImg;
    }

    public BatchesBarCodeVO setBarCodeQrImg(String barCodeQrImg) {
        this.barCodeQrImg = barCodeQrImg;
        return this;
    }

    public Integer getType() {
        return type;
    }

    public BatchesBarCodeVO setType(Integer type) {
        this.type = type;
        return this;
    }

    public String getBatches() {
        return batches;
    }

    public BatchesBarCodeVO setBatches(String batches) {
        this.batches = batches;
        return this;
    }

    public String getExpiration() {
        return expiration;
    }

    public BatchesBarCodeVO setExpiration(String expiration) {
        this.expiration = expiration;
        return this;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public BatchesBarCodeVO setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
        return this;
    }

    public String getProductionDate() {
        return productionDate;
    }

    public BatchesBarCodeVO setProductionDate(String productionDate) {
        this.productionDate = productionDate;
        return this;
    }

    public String getGasBottleBarcode() {
        return gasBottleBarcode;
    }

    public BatchesBarCodeVO setGasBottleBarcode(String gasBottleBarcode) {
        this.gasBottleBarcode = gasBottleBarcode;
        return this;
    }

    public GasBottleVO getGasBottle() {
        return gasBottle;
    }

    public BatchesBarCodeVO setGasBottle(GasBottleVO gasBottle) {
        this.gasBottle = gasBottle;
        return this;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public BatchesBarCodeVO setQuantity(Integer quantity) {
        this.quantity = quantity;
        return this;
    }

    public Integer getExterior() {
        return exterior;
    }

    public BatchesBarCodeVO setExterior(Integer exterior) {
        this.exterior = exterior;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public BatchesBarCodeVO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Integer getBatchesStatus() {
        return batchesStatus;
    }

    public BatchesBarCodeVO setBatchesStatus(Integer batchesStatus) {
        this.batchesStatus = batchesStatus;
        return this;
    }

    public Integer getTransactionStatus() {
        return transactionStatus;
    }

    public BatchesBarCodeVO setTransactionStatus(Integer transactionStatus) {
        this.transactionStatus = transactionStatus;
        return this;
    }

    public Integer getInventoryStatus() {
        return inventoryStatus;
    }

    public BatchesBarCodeVO setInventoryStatus(Integer inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
        return this;
    }

    public Integer getPrinted() {
        return printed;
    }

    public BatchesBarCodeVO setPrinted(Integer printed) {
        this.printed = printed;
        return this;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public BatchesBarCodeVO setReturnReason(String returnReason) {
        this.returnReason = returnReason;
        return this;
    }

    public String getReturnDescription() {
        return returnDescription;
    }

    public BatchesBarCodeVO setReturnDescription(String returnDescription) {
        this.returnDescription = returnDescription;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", BatchesBarCodeVO.class.getSimpleName() + "[", "]")
                .add("barCode='" + barCode + "'")
                .add("barCodeImg='" + barCodeImg + "'")
                .add("type=" + type)
                .add("batches='" + batches + "'")
                .add("expiration='" + expiration + "'")
                .add("manufacturer='" + manufacturer + "'")
                .add("productionDate='" + productionDate + "'")
                .add("gasBottleBarcode='" + gasBottleBarcode + "'")
                .add("gasBottle=" + gasBottle)
                .add("quantity=" + quantity)
                .add("exterior=" + exterior)
                .add("status=" + status)
                .add("batchesStatus=" + batchesStatus)
                .add("transactionStatus=" + transactionStatus)
                .add("inventoryStatus=" + inventoryStatus)
                .add("printed=" + printed)
                .add("returnReason='" + returnReason + "'")
                .add("returnDescription='" + returnDescription + "'")
                .toString();
    }
}
