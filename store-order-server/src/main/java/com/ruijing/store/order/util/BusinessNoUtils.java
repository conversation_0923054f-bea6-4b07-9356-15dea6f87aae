package com.ruijing.store.order.util;

import com.ruijing.fundamental.common.date.DateUtils;
import org.apache.commons.lang3.RandomUtils;

import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @description: 业务编号工具类
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/11/25 16:28
 **/
public class BusinessNoUtils {

    private static final String SIMPLE_DATE_FORMAT_YYYY_MM_DD = "yyyyMMdd";

    private static final AtomicInteger FOUR_DIGIT = new AtomicInteger(0);

    /**
     * 两位数的数据阈值
     */
    private static final int TWO_THRESHOLD = 99;
    /**
     * 四位数的数据阈值
     */
    private static final int FOUR_THRESHOLD = 9999;

    /**
     * 数据类型(数据位数)
     */
    public enum NumberType {
        /**
         * 两位数位数
         */
        TWO,
        /**
         * 四位数位数
         */
        FOUR;
    }

    /**
     * 业务类型
     */
    public enum BusinessType {
        /**
         * 订单业务
         */
        ORDER,
        /**
         * 采购业务
         */
        PUCHASESUPPLY,
        /**
         * 验收业务
         */
        ACCEPTANCE,
        /**
         * 退货业务
         */
        RETURN,
        /**
         * 线下采购单业务
         */
        OFFLINE_SMALL_PURCHASE_SUPPLY
    }

    /**
     * 订单不同业务的编号枚举
     */
    public enum OrderNumberType {
        //采购申请编号
        C("C"),
        //订单编号
        D("D"),
        //验收单号
        Y("Y"),
        //R开头的订单号
        R("R"),
        //退货单号
        T("T"),
        ;

        OrderNumberType(String code) {
            this.code = code;
        }

        private String code;

        public String getCode() {
            return code;
        }
    }

    /**
     * 生成业务单编号
     * @param businessType 业务类型
     * @param orderNo      订单编号
     * @param lastNum      后缀尾数
     * @return
     */
    public static String getBusinessNumber(BusinessType businessType, String orderNo, String lastNum) {
        String businessNo = null;

        //退货单号(T+关联订单编号的13位数字+2位排序数)
        if (businessType == BusinessType.RETURN) {
            if (orderNo.startsWith(OrderNumberType.R.getCode())) {
                businessNo = OrderNumberType.T.getCode() + orderNo + getMantissa(lastNum);
            } else {
                businessNo = OrderNumberType.T.getCode() + orderNo.substring(1, 15) + getMantissa(lastNum);
            }
        }

        return businessNo;
    }

    /**
     * 生成不同的后缀
     */
    private static String getMantissa(String lastNum) {
        String returnMantissa = null;

        if (null == lastNum) {
            lastNum = "0";
        }
        int a = Integer.parseInt(lastNum) + 1;
        // 如果超过2位数阈值, 重置为0
        if (a > TWO_THRESHOLD) {
            a = 0;
        }
        returnMantissa = String.format("%02d", a);

        return returnMantissa;
    }
}
