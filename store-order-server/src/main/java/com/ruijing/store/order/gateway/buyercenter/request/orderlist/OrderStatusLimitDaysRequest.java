package com.ruijing.store.order.gateway.buyercenter.request.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/4/12 16:08
 * @Description
 **/
@RpcModel("订单管理-订单某状态在限制天数内请求体")
public class OrderStatusLimitDaysRequest implements Serializable {

    private static final long serialVersionUID = -1527005410815376056L;

    /**
     * 订单id
     */
    @RpcModelProperty("订单id")
    private Integer orderId;

    /**
     * 订单状态（目前接受已完成11，不传默认11）
     */
    @RpcModelProperty("订单状态（目前接受已完成11，不传默认11）")
    private Integer status = 11;

    public Integer getOrderId() {
        return orderId;
    }

    public OrderStatusLimitDaysRequest setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public OrderStatusLimitDaysRequest setStatus(Integer status) {
        this.status = status;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderStatusLimitDaysRequest{");
        sb.append("orderId=").append(orderId);
        sb.append(", status=").append(status);
        sb.append('}');
        return sb.toString();
    }
}
