package com.ruijing.store.order.gateway.oms;

import com.reagent.order.base.order.dto.BasePageResultDTO;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.order.whitehole.database.dto.address.data.DeliveryOperationLogDTO;
import com.ruijing.order.whitehole.database.dto.address.request.DeliveryOperationLogRequestDTO;
import com.ruijing.store.oms.api.enums.UserOrgModuleEnum;
import com.ruijing.store.order.base.excel.dto.OrderExcelInfoQueryDTO;
import com.ruijing.store.order.base.excel.dto.OrderExcelInfoResponseDTO;
import com.ruijing.store.order.base.excel.dto.OrderExportRequestDTO;
import com.ruijing.store.order.business.service.OMSOrderService;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;
import com.ruijing.store.order.gateway.oms.request.DeliveryUpdateRequest;
import com.ruijing.store.order.rpc.client.UserClient;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.List;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2022/7/8 15:33
 */
@MSharpService
@RpcApi(value = "oms网关服务",description = "oms网关服务")
@RpcMapping("/oms")
public class OmsOrderGWController {

    @Resource
    private OMSOrderService omsOrderService;

    @Resource
    private UserClient userClient;

    @RpcMethod(value = "代配送订单管理列表")
    @RpcMapping("/getDeliveryProxyOrderList")
    public PageableResponse<List<OrderInfoVO>> getDeliveryProxyOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request) throws ParseException {
        // 权限校验
        omsOrderService.checkUserHasAccessOMS(rjSessionInfo.getGuid(), New.list("DELIVERY_ORDER_MANAGE"));
        return omsOrderService.getDeliveryProxyOrderList(request, null, rjSessionInfo, UserOrgModuleEnum.AGENT_DELIVERY_ORDER);
    }

    @RpcMethod(value = "代配送订单查询列表")
    @RpcMapping("/viewDeliveryProxyOrderList")
    public PageableResponse<List<OrderInfoVO>> viewDeliveryProxyOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request) throws ParseException{
        // 权限校验
        omsOrderService.checkUserHasAccessOMS(rjSessionInfo.getGuid(), New.list("DELIVERY_ORDER_BROWSE"));
        return omsOrderService.getDeliveryProxyOrderList(request, null, rjSessionInfo, UserOrgModuleEnum.AGENT_DELIVERY_ORDER_QUERY);
    }

    @RpcMethod(value = "代配送订单管理详情")
    @RpcMapping("/getDeliveryProxyOrderDetail")
    public RemoteResponse<OrderInfoVO> getDeliveryProxyOrderDetail(RjSessionInfo rjSessionInfo, OrderListRequest request) throws ParseException {
        return this.getDeliveryProxyOrderDetail(rjSessionInfo, request, "DELIVERY_ORDER_MANAGE", UserOrgModuleEnum.AGENT_DELIVERY_ORDER);
    }

    @RpcMethod(value = "代配送订单查询详情")
    @RpcMapping("/viewDeliveryProxyOrderDetail")
    public RemoteResponse<OrderInfoVO> viewDeliveryProxyOrderDetail(RjSessionInfo rjSessionInfo, OrderListRequest request) throws ParseException {
        return this.getDeliveryProxyOrderDetail(rjSessionInfo, request, "DELIVERY_ORDER_BROWSE", UserOrgModuleEnum.AGENT_DELIVERY_ORDER_QUERY);
    }

    private RemoteResponse<OrderInfoVO> getDeliveryProxyOrderDetail(RjSessionInfo rjSessionInfo, OrderListRequest request, String accessCode, UserOrgModuleEnum userOrgModuleEnum){
        // 权限校验
        omsOrderService.checkUserHasAccessOMS(rjSessionInfo.getGuid(), New.list(accessCode));
        // 针对订单号和货号搜索特殊处理（双空则返回，双占则货号要去除）
        if (StringUtils.isBlank(request.getOrderNo()) && StringUtils.isBlank(request.getProductCode())) {
            return RemoteResponse.<OrderInfoVO>custom().setData(null).setSuccess().setTotal(0);
        }
        if (StringUtils.isNotBlank(request.getOrderNo()) && StringUtils.isNotBlank(request.getProductCode())) {
            request.setProductCode(null);
        }
        return RemoteResponse.success(omsOrderService.getDeliveryProxyOrderDetail(request, rjSessionInfo, userOrgModuleEnum));
    }

    @RpcMethod(value = "导出代配送列表")
    @RpcMapping("/exportDeliveryProxyOrderList")
    public RemoteResponse<Boolean> exportDeliveryProxyOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request) {
        // 权限校验
        omsOrderService.checkUserHasAccessOMS(rjSessionInfo.getGuid(), "DELIVERY_ORDER_MANAGE_EXPORT");
        omsOrderService.exportDeliveryProxyOrderList(request, rjSessionInfo);
        return RemoteResponse.success();
    }

    @RpcMethod(value = "导出了的代配送列表")
    @RpcMapping("/exportedDeliveryProxyList")
    public RemoteResponse<BasePageResultDTO<OrderExcelInfoResponseDTO>> exportedDeliveryProxyList(RjSessionInfo rjSessionInfo, OrderExcelInfoQueryDTO orderExcelInfoQueryDTO) {
        // 权限校验
        omsOrderService.checkUserHasAccessOMS(rjSessionInfo.getGuid(), "DELIVERY_ORDER_MANAGE_EXPORT");
        return omsOrderService.exportedDeliveryProxyList(rjSessionInfo, orderExcelInfoQueryDTO);
    }

    @RpcMethod(value = "删除导出的代配送列表项")
    @RpcMapping("/deleteExportedDeliveryProxyItem")
    public RemoteResponse<Boolean> deleteExportedDeliveryProxyItem(RjSessionInfo rjSessionInfo, OrderExportRequestDTO orderExportRequestDTO) {
        // 权限校验
        omsOrderService.checkUserHasAccessOMS(rjSessionInfo.getGuid(), "DELIVERY_ORDER_MANAGE_EXPORT");
        omsOrderService.deleteExportedDeliveryProxyItem(orderExportRequestDTO.getId());
        return RemoteResponse.<Boolean>custom().setSuccess();
    }

    @RpcMethod(value = "完成分拣或配送")
    @RpcMapping("/delivery/update")
    public RemoteResponse<Boolean> updateDelivery(RjSessionInfo rjSessionInfo, DeliveryUpdateRequest deliveryUpdateRequest){
        omsOrderService.updateDelivery(rjSessionInfo, deliveryUpdateRequest);
        return RemoteResponse.success();
    }

    @RpcMethod(value = "代配送日志列表")
    @RpcMapping("/deliveryOperationLog/list")
    public PageableResponse<List<DeliveryOperationLogDTO>> listDeliveryOperationLog(RjSessionInfo rjSessionInfo, DeliveryOperationLogRequestDTO deliveryUpdateRequest){
        return omsOrderService.listDeliveryOperationLog(rjSessionInfo, deliveryUpdateRequest);
    }

    @RpcMethod(value = "OMS-单据查询-订单查询列表")
    @RpcMapping("/receipt/order/list")
    public PageableResponse<List<OrderInfoVO>> getReceiptOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request) {
        return omsOrderService.getReceiptOrderList(request);
    }
}
