package com.ruijing.store.order.gateway.fundcard.service;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-12-20 16:47
 * @description: 经费解冻相关操作后续都拆分到这里
 */
public interface OrderFundCardUnfreezeService {

    /**
     * 将订单解冻并改为自结算。订单的经费为冻结状态就先解冻，回调时变为自结算。如果已经解冻完了，直接变为自结算
     *
     * @param orgId 单位id
     * @param operateUserId 操作人id
     * @param orderId 订单id
     */
    void unFreezeAndChangeOrderToSelfStatement(Integer orgId, Integer operateUserId, Integer orderId);

    /**
     * 将订单改为自结算
     *
     * @param operateUserId 操作人id
     * @param orderId 订单id
     */
    void changeOrderToSelfStatement(Integer operateUserId, Integer orderId);
}
