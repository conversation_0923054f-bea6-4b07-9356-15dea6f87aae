package com.ruijing.store.order.base.excel.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import java.math.BigDecimal;

public class ProductPrintDetailDTO {

    /**
     * 发票抬头
     */
    @ExcelIgnore
    private String invoiceTitle;

    /**
     * 订单编号
     */
    @ExcelProperty("订单编号")
    @ColumnWidth(value = 30)
    private String orderNo;

    /**
     * 采购组
     */
    @ExcelProperty("采购组")
    @ColumnWidth(value = 30)
    private String departmentName;

    /**
     * 采购人
     */
    @ExcelProperty("采购人")
    @ColumnWidth(value = 20)
    private String purchaseName;

    /**
     * 收货人
     */
    @ExcelProperty("收货人")
    @ColumnWidth(value = 20)
    private String  receiver;

    /**
     * 供应商
     */
    @ExcelProperty("供应商")
    @ColumnWidth(value = 50)
    private String suppName;

    /**
     * 给供应商备注
     */
    @ExcelProperty("给供应商备注")
    @ColumnWidth(value = 50)
    private String remark;


    /**
     * 订单日期
     */
    @ExcelProperty("订单日期")
    @ColumnWidth(value = 30)
    private String orderDate;

    /**
     * 订单金额
     */
    @ExcelProperty("订单金额")
    @ColumnWidth(value = 20)
    private BigDecimal orderPrice;

    /**
     * 订单减去退货后的金额
     */
    @ExcelIgnore
    private BigDecimal orderPriceAfterReturn;

    /**
     * 状态
     */
    @ExcelProperty("状态")
    @ColumnWidth(value = 20)
    private String statusName;

    /**
     * 商品名称
     */
    @ExcelProperty("商品名称")
    @ColumnWidth(value = 50)
    private String productName;

    /**
     * 品牌
     */
    @ExcelProperty("商品品牌")
    @ColumnWidth(value = 20)
    private String brand;

    /**
     * 商品数量
     */
    @ExcelProperty("商品数量")
    @ColumnWidth(value = 20)
    private Integer count;

    /**
     * 商品货号
     */
    @ExcelProperty("商品货号")
    @ColumnWidth(value = 30)
    private String productCode;

    /**
     * 商品单价
     */
    @ExcelProperty("商品单价")
    @ColumnWidth(value = 20)
    private BigDecimal unitPrice;

    /**
     * 商品总价
     */
    @ExcelProperty("总价")
    @ColumnWidth(value = 20)
    private BigDecimal totalPrice;

    /**
     * 销售单位
     */
    @ExcelProperty("销售单位")
    @ColumnWidth(value = 20)
    private String unit;

    /**
     * 规格
     */
    @ExcelProperty("规格")
    @ColumnWidth(value = 20)
    private String specifications;

    /**
     * 顶级分类
     */
    @ExcelProperty("商品类型")
    @ColumnWidth(value = 20)
    private String topCategoryName;

    /**
     * 一级分类
     */
    @ExcelProperty("一级分类")
    @ColumnWidth(value = 20)
    private String firstCategoryName;

    /**
     * 二级分类
     */
    @ExcelProperty("二级分类")
    @ColumnWidth(value = 20)
    private String secondCategoryName;

    /**
     * 三级分类
     */
    @ExcelProperty("三级分类")
    @ColumnWidth(value = 20)
    private String thirdCategoryName;

    /**
     * 用于后续匹配的 结算单id
     */
    @ExcelIgnore
    private Long statementId;

    /**
     * 用于匹配的，订单详情id
     */
    @ExcelIgnore
    private Integer orderDetailId;

    /**
     * 成功退货数量
     */
    @ExcelIgnore
    private BigDecimal successfulReturnQuantity;

    /**
     * 成功退货总计
     */
    @ExcelIgnore
    private BigDecimal successfulReturnAmount;

    /**
     * 订单维度的成功退货总额
     */
    @ExcelIgnore
    private BigDecimal successfulReturnOrderAmount;

    /**
     * 退货后的商品总计金额
     */
    @ExcelIgnore
    private BigDecimal productAmountAfterReturn;

    /**
     * 退货后的商品数量
     */
    @ExcelIgnore
    private Integer productCountAfterReturn;

    @ExcelIgnore
    private String statementType;

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public ProductPrintDetailDTO setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getPurchaseName() {
        return purchaseName;
    }

    public void setPurchaseName(String purchaseName) {
        this.purchaseName = purchaseName;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(String orderDate) {
        this.orderDate = orderDate;
    }

    public BigDecimal getOrderPrice() {
        return orderPrice;
    }

    public void setOrderPrice(BigDecimal orderPrice) {
        this.orderPrice = orderPrice;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getTopCategoryName() {
        return topCategoryName;
    }

    public void setTopCategoryName(String topCategoryName) {
        this.topCategoryName = topCategoryName;
    }

    public String getFirstCategoryName() {
        return firstCategoryName;
    }

    public void setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
    }

    public String getSecondCategoryName() {
        return secondCategoryName;
    }

    public void setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
    }

    public String getThirdCategoryName() {
        return thirdCategoryName;
    }

    public void setThirdCategoryName(String thirdCategoryName) {
        this.thirdCategoryName = thirdCategoryName;
    }

    public Long getStatementId() {
        return statementId;
    }

    public void setStatementId(Long statementId) {
        this.statementId = statementId;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public BigDecimal getSuccessfulReturnQuantity() {
        return successfulReturnQuantity;
    }

    public void setSuccessfulReturnQuantity(BigDecimal successfulReturnQuantity) {
        this.successfulReturnQuantity = successfulReturnQuantity;
    }

    public BigDecimal getSuccessfulReturnAmount() {
        return successfulReturnAmount;
    }

    public void setSuccessfulReturnAmount(BigDecimal successfulReturnAmount) {
        this.successfulReturnAmount = successfulReturnAmount;
    }

    public BigDecimal getOrderPriceAfterReturn() {
        return orderPriceAfterReturn;
    }

    public void setOrderPriceAfterReturn(BigDecimal orderPriceAfterReturn) {
        this.orderPriceAfterReturn = orderPriceAfterReturn;
    }

    public String getStatementType() {
        return statementType;
    }

    public void setStatementType(String statementType) {
        this.statementType = statementType;
    }

    public BigDecimal getSuccessfulReturnOrderAmount() {
        return successfulReturnOrderAmount;
    }

    public void setSuccessfulReturnOrderAmount(BigDecimal successfulReturnOrderAmount) {
        this.successfulReturnOrderAmount = successfulReturnOrderAmount;
    }

    public BigDecimal getProductAmountAfterReturn() {
        return productAmountAfterReturn;
    }

    public void setProductAmountAfterReturn(BigDecimal productAmountAfterReturn) {
        this.productAmountAfterReturn = productAmountAfterReturn;
    }

    public Integer getProductCountAfterReturn() {
        return productCountAfterReturn;
    }

    public void setProductCountAfterReturn(Integer productCountAfterReturn) {
        this.productCountAfterReturn = productCountAfterReturn;
    }
}
