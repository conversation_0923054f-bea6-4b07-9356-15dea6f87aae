package com.ruijing.store.order.base.freezedeptlog.mapper;
import com.ruijing.store.order.base.freezedeptlog.model.FreezeDeptLogDO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface FreezeDeptLogDOMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FreezeDeptLogDO record);

    int insertSelective(FreezeDeptLogDO record);

    FreezeDeptLogDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FreezeDeptLogDO record);

    int updateByPrimaryKey(FreezeDeptLogDO record);

    /**
     * 查找冻结/未冻结的课题组
     * @param isDeleted
     * @return
     */
    List<FreezeDeptLogDO> findAllByIsDeleted(@Param("isDeleted")Integer isDeleted);

    /**
     * 批量插入新的冻结课题组
     * @param list
     * @return
     */
    int insertList(@Param("list")List<FreezeDeptLogDO> list);

    int updateIsDeletedByIdIn(@Param("updatedIsDeleted")Integer updatedIsDeleted,@Param("idCollection")Collection<Long> idCollection);

    /**
     * 解冻课题组
     * @param orgId 机构id
     * @param depId 课题组id
     * @return
     */
    int updateDeletedByOrgIdAndDepId(@Param("orgId")Integer orgId, @Param("depId")Integer depId);

    /**
     * 根据时间范围的冻结课题组记录
     * @param minCreationTime
     * @param maxCreationTime
     * @param hasDeleted
     * @return
     */
    List<FreezeDeptLogDO> findAllByCreationTimeBetweenAndHasDeleted(@Param("minCreationTime")Date minCreationTime,
                                                                    @Param("maxCreationTime")Date maxCreationTime,
                                                                    @Param("hasDeleted")Integer hasDeleted);

    /**
     * 按医院维id，获取由被冻结的部门信息
     * @param orgId
     * @return
     */
    default List<FreezeDeptLogDO> findByOrgId(Integer orgId){
        return this.findFreezeByOrgIdAndDepId(orgId, null);
    }

    /**
     * 按单位id+部门id，获取由被冻结的部门信息
     * @param orgId 单位id
     * @param deptIds 部门id列表
     * @return 冻结记录
     */
    List<FreezeDeptLogDO> findFreezeByOrgIdAndDepId(@Param("orgId") Integer orgId, @Param("deptIds") List<Integer> deptIds);
}