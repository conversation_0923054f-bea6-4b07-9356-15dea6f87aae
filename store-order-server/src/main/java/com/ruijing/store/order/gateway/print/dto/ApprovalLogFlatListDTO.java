package com.ruijing.store.order.gateway.print.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.other.dto.OrderPurchaseApprovalLogPrintDTO;
import com.ruijing.store.order.gateway.print.dto.order.OrderPrintApprovalLogDTO;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2023/2/28 14:38
 * @description 根据自定义打印需求列表展平为一级一个属性
 */
@RpcModel("审批日志列表")
public class ApprovalLogFlatListDTO implements Serializable {

    private static final long serialVersionUID = -1937788020772579443L;
    
    @RpcModelProperty("一级审批日志")
    private OrderPrintApprovalLogDTO firstLevelLog;

    @RpcModelProperty("二级审批日志")
    private OrderPrintApprovalLogDTO secondLevelLog;

    @RpcModelProperty("三级审批日志")
    private OrderPrintApprovalLogDTO thirdLevelLog;

    @RpcModelProperty("四级审批日志")
    private OrderPrintApprovalLogDTO fourthLevelLog;

    @RpcModelProperty("五级审批日志")
    private OrderPrintApprovalLogDTO fifthLevelLog;

    @RpcModelProperty("六级审批日志")
    private OrderPrintApprovalLogDTO sixthLevelLog;

    @RpcModelProperty("七级审批日志")
    private OrderPrintApprovalLogDTO seventhLevelLog;

    @RpcModelProperty("八级审批日志")
    private OrderPrintApprovalLogDTO eighthLevelLog;

    public OrderPrintApprovalLogDTO getFirstLevelLog() {
        return firstLevelLog;
    }

    public void setFirstLevelLog(OrderPrintApprovalLogDTO firstLevelLog) {
        this.firstLevelLog = firstLevelLog;
    }

    public OrderPrintApprovalLogDTO getSecondLevelLog() {
        return secondLevelLog;
    }

    public void setSecondLevelLog(OrderPrintApprovalLogDTO secondLevelLog) {
        this.secondLevelLog = secondLevelLog;
    }

    public OrderPrintApprovalLogDTO getThirdLevelLog() {
        return thirdLevelLog;
    }

    public void setThirdLevelLog(OrderPrintApprovalLogDTO thirdLevelLog) {
        this.thirdLevelLog = thirdLevelLog;
    }

    public OrderPrintApprovalLogDTO getFourthLevelLog() {
        return fourthLevelLog;
    }

    public void setFourthLevelLog(OrderPrintApprovalLogDTO fourthLevelLog) {
        this.fourthLevelLog = fourthLevelLog;
    }

    public OrderPrintApprovalLogDTO getFifthLevelLog() {
        return fifthLevelLog;
    }

    public void setFifthLevelLog(OrderPrintApprovalLogDTO fifthLevelLog) {
        this.fifthLevelLog = fifthLevelLog;
    }

    public OrderPrintApprovalLogDTO getSixthLevelLog() {
        return sixthLevelLog;
    }

    public void setSixthLevelLog(OrderPrintApprovalLogDTO sixthLevelLog) {
        this.sixthLevelLog = sixthLevelLog;
    }

    public OrderPrintApprovalLogDTO getSeventhLevelLog() {
        return seventhLevelLog;
    }

    public void setSeventhLevelLog(OrderPrintApprovalLogDTO seventhLevelLog) {
        this.seventhLevelLog = seventhLevelLog;
    }

    public OrderPrintApprovalLogDTO getEighthLevelLog() {
        return eighthLevelLog;
    }

    public void setEighthLevelLog(OrderPrintApprovalLogDTO eighthLevelLog) {
        this.eighthLevelLog = eighthLevelLog;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ApprovalLogFlatListDTO.class.getSimpleName() + "[", "]")
                .add("firstLevelLog=" + firstLevelLog)
                .add("secondLevelLog=" + secondLevelLog)
                .add("thirdLevelLog=" + thirdLevelLog)
                .add("fourthLevelLog=" + fourthLevelLog)
                .add("fifthLevelLog=" + fifthLevelLog)
                .add("sixthLevelLog=" + sixthLevelLog)
                .add("seventhLevelLog=" + seventhLevelLog)
                .add("eighthLevelLog=" + eighthLevelLog)
                .toString();
    }
}
