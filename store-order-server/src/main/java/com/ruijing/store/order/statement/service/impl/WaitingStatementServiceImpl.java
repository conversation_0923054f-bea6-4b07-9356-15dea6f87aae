package com.ruijing.store.order.statement.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.reagent.research.statement.api.enums.StatementWayEnum;
import com.reagent.research.statement.api.order.dto.WaitingStatementOrderRequestDTO;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.store.order.api.base.enums.InventoryStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.api.base.enums.ProcessSpeciesEnum;
import com.ruijing.store.order.api.base.enums.TimeOutBusinessType;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.util.OrderCommonUtils;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.constant.OrderDateConstant;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.order.rpc.client.StatementPlatformClient;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import com.ruijing.store.order.statement.enums.WaitingStatementOrderStatusEnum;
import com.ruijing.store.order.statement.service.WaitingStatementService;
import com.ruijing.store.order.util.DictionaryUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 待结算业务表记录的生产类
 *
 * <AUTHOR>
 */
@Service
public class WaitingStatementServiceImpl implements WaitingStatementService {

    private static final String CAT_TYPE = "WaitingStatementServiceImpl";

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderManageService orderManageService;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Resource
    private SysConfigClient sysConfigClient;

    /**
     * 待结算业务表记录生成
     *
     * @param orderIdList
     * @param orgCode
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public List<WaitingStatementOrderRequestDTO> waitingStatementGenerate(List<Integer> orderIdList, String orgCode) {
        List<OrderMasterDO> orderList = orderMasterMapper.findByIdIn(orderIdList);
        Assert.notEmpty(orderList, "订单结算驳回失败！无订单信息");
        // 驳回的订单经费卡查询
        List<RefFundcardOrderDTO> refFundcardOrderDtoList = refFundcardOrderService.findByOrderIdList(orderIdList);
        // 驳回的订单经费卡的字典
        Map<String, List<RefFundcardOrderDTO>> orderIdRefFundCardMap = DictionaryUtils.groupBy(refFundcardOrderDtoList, RefFundcardOrderDTO::getOrderId);
        // 驳回的订单商品详情
        List<OrderDetailDO> rejectOrderDetailList = orderDetailMapper.findAllByFmasteridIn(orderIdList);
        // 驳回的订单商品详情字典
        Map<Integer, List<OrderDetailDO>> orderIdDetailListMap = DictionaryUtils.groupBy(rejectOrderDetailList, OrderDetailDO::getFmasterid);

        // 获取医院的超时配置
        Map<String, Integer> timeOutConfigMap = orderManageService.getTimeOutConfigMap(orgCode);

        Date nowDate = new Date();
        List<WaitingStatementOrderRequestDTO> waitingStatementOrderDtoList = new ArrayList<>(orderIdList.size());
        for (OrderMasterDO masterDo : orderList) {
            WaitingStatementOrderRequestDTO dto = new WaitingStatementOrderRequestDTO();
            dto.setOrderId(masterDo.getId());
            dto.setOrderNo(masterDo.getForderno());
            dto.setOrgId(masterDo.getFuserid());
            dto.setDepartmentId(masterDo.getFbuydepartmentid());
            dto.setDepartmentName(masterDo.getFbuydepartment());
            // 某些单位收货后马上进入待结算，还未生成待结算时间，取当前时间
            dto.setReceiveDate(masterDo.getFlastreceivedate() != null ? masterDo.getFlastreceivedate() : nowDate);
            dto.setSupplierId(masterDo.getFsuppid());
            dto.setSupplierName(masterDo.getFsuppname());
            dto.setInvoiceTitleId(masterDo.getInvoiceTitleId());
            if(OrgEnum.JI_NAN_DA_XUE.getValue() == masterDo.getFuserid()){
                dto.setStatementWay(OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue().equals(masterDo.getFundStatus()) ? StatementWayEnum.SELF_STATEMENT.getStatementWay() : StatementWayEnum.UNION_STATEMENT.getStatementWay());
            }

            List<OrderDetailDO> detailDOList = orderIdDetailListMap.get(masterDo.getId());
            if (CollectionUtils.isNotEmpty(detailDOList)) {
                // 设置订单的最底层分类，即商品的品类
                String secondTierCategory = detailDOList
                        .stream()
                        .map(OrderDetailDO::getFclassification)
                        .filter(StringUtils::isNotBlank)
                        .distinct()
                        .collect(Collectors.joining(";"));
                dto.setSecondTierCategory(secondTierCategory);

                // 设置订单的最底层分类id，即商品的品类id
                String secondTierCategoryId = detailDOList
                        .stream()
                        .filter(d -> d.getCategoryid() != null)
                        .map(d -> String.valueOf(d.getCategoryid()))
                        .distinct()
                        .collect(Collectors.joining(";"));
                dto.setSecondTierCategoryId(secondTierCategoryId);

                // 用订单商品 品类标签
                String firstTierCategoryResult = detailDOList.stream().map(OrderDetailDO::getCategoryTag).distinct().collect(Collectors.joining(";"));
                // 设置顶级分类
                dto.setFirstTierCategory(firstTierCategoryResult);
                // 设置待结算单超时标签
                if (OrderCommonUtils.isStatementTimeOut(masterDo, detailDOList, timeOutConfigMap)) {
                    dto.setTimeOutStatus(TimeOutBusinessType.BALANCE.getValue());
                }
            } else {
                // 设置待结算单超时标签
                if (OrderCommonUtils.isStatementTimeOut(masterDo, timeOutConfigMap)) {
                    dto.setTimeOutStatus(TimeOutBusinessType.BALANCE.getValue());
                }
            }
            dto.setSpecies(masterDo.getSpecies().intValue());
            dto.setBuyerId(masterDo.getFbuyerid());
            dto.setBuyerName(masterDo.getFbuyername());

            // 设置待结算单的经费卡信息
            List<RefFundcardOrderDTO> fundcardList = orderIdRefFundCardMap.get(String.valueOf(masterDo.getId()));
            String fundCardListString = "";
            if (CollectionUtils.isNotEmpty(fundcardList)) {
                fundCardListString = fundcardList
                        .stream()
                        .map(RefFundcardOrderDTO::getCardNo)
                        .filter(cardNo -> StringUtils.isNotBlank(cardNo))
                        .collect(Collectors.joining(";"));
            }

            dto.setFundCards(fundCardListString);
            dto.setStatementStatus(WaitingStatementOrderStatusEnum.REJECTED.getCode());
            dto.setUpdateTime(nowDate);
            dto.setCreateTime(nowDate);
            dto.setInventoryStatus(masterDo.getInventoryStatus().intValue());
            dto.setPaymentAmount(masterDo.getPaymentAmount());

            waitingStatementOrderDtoList.add(dto);
        }

        return waitingStatementOrderDtoList;
    }

    /**
     * 推送待结算单
     *
     * @param orgCode
     * @param orderIdList
     */
    @Override
    public void pushWaitingStatement(String orgCode, List<Integer> orderIdList) {
        if (checkStatementCondition(orgCode, orderIdList)) {
            return;
        }
        AsyncExecutor.listenableRunAsync(() -> {
            List<WaitingStatementOrderRequestDTO> waitingStatementList = this.waitingStatementGenerate(orderIdList, orgCode);
            String offlineStatementValue = sysConfigClient.getConfigByOrgCodeAndConfigCode(orgCode, ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM);
            //非线下结算的单位，不推入线下单到结算单
            if (!ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM_VALUE.equals(offlineStatementValue)) {
                waitingStatementList = waitingStatementList.stream().filter(d -> d.getSpecies().equals(ProcessSpeciesEnum.NORMAL.getValue())).collect(Collectors.toList());
            }
            // 插入业务数据到待结算表
            statementPlatformClient.saveWaitingStatement(waitingStatementList);

        }).addFailureCallback(throwable -> {
            logger.error("订单待结算数据推送同步失败！同步对象: {}", JsonUtils.toJsonIgnoreNull(orderIdList), throwable);
            Cat.logError(CAT_TYPE, "saveWaitingStatement", "订单待结算数据推送同步失败！", throwable);
            throw new IllegalStateException("订单待结算数据推送同步失败！" + throwable);
        });

    }

    @Override
    @ServiceLog(description = "调用推送到待结算", operationType = OperationType.WRITE,serviceType = ServiceType.COMMON_SERVICE)
    public void pushWaitingStatement(String orgCode, List<Integer> orderIdList, final Integer currentInventoryStatus) {
        if (checkStatementCondition(orgCode, orderIdList)) {
            return;
        }
        AsyncExecutor.listenableRunAsync(() -> {
            List<WaitingStatementOrderRequestDTO> waitingStatementList = this.waitingStatementGenerate(orderIdList, orgCode);
            String offlineStatementValue = sysConfigClient.getConfigByOrgCodeAndConfigCode(orgCode, ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM);
            //非线下结算的单位，不推入线下单到结算单
            if (!ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM_VALUE.equals(offlineStatementValue)) {
                waitingStatementList = waitingStatementList.stream().filter(d -> d.getSpecies().equals(ProcessSpeciesEnum.NORMAL.getValue())).collect(Collectors.toList());
            }

            if (InventoryStatusEnum.NOT_INBOUND.getCode().equals(currentInventoryStatus) || InventoryStatusEnum.COMPLETE.getCode().equals(currentInventoryStatus)) {
                waitingStatementList.forEach(it -> it.setInventoryStatus(currentInventoryStatus));
            }
            // 插入业务数据到待结算表
            statementPlatformClient.saveWaitingStatement(waitingStatementList);

        }).addFailureCallback(throwable -> {
            logger.error("订单待结算数据推送同步失败！同步对象: {}", JsonUtils.toJsonIgnoreNull(orderIdList), throwable);
            Cat.logError(CAT_TYPE, "saveWaitingStatement", "订单待结算数据推送同步失败！", throwable);
            throw new IllegalStateException("订单待结算数据推送同步失败！" + throwable);
        });
    }

    /**
     * 是否无需推送到待结算
     *
     * @param orgCode     机构编码
     * @param orderIdList 订单id
     * @return 是/否
     */
    private boolean checkStatementCondition(String orgCode, List<Integer> orderIdList) {
        // 中大和中大深圳的医院不推送到待结算
        if (ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgCode(orgCode) || OrgEnum.GUANG_ZHOU_YI_KE_DA_XUE.getCode().equals(orgCode)) {
            return true;
        }
        // 广西肿瘤，新的订单不需要推送推送到待结算
        String oldDate = OrderDateConstant.NO_NEED_WAITING_STATEMENT_ORG_CODE_OLD_ORDER_DATE_MAP.get(orgCode);
        if (oldDate != null) {
            List<OrderMasterDO> orderList = orderMasterMapper.findByIdIn(orderIdList);
            long count = orderList.stream().filter(o -> !OrderDateConstant.isOldOrderForNormal(orgCode, o.getForderdate())).count();
            return count > 0L;
        }
        return false;
    }
}
