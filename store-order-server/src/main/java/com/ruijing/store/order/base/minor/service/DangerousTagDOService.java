package com.ruijing.store.order.base.minor.service;

import com.ruijing.store.order.base.minor.model.DangerousTagDO;

import java.util.List;
import java.util.Map;

/**
 * @description: 获取危化品信息
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/06/21 15:33
 **/
public interface DangerousTagDOService {

    /**
     * 根据订单详情id查询危化品信息
     * @param orderDetailIdList   入参
     * @return                  订单备注记录
     */
    Map<String, DangerousTagDO> selectByOrderDetailIdIn(List<String> orderDetailIdList);
}
