package com.ruijing.store.order.gateway.wechat;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.other.dto.OrgConfigResponseDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.service.BuyerOrderService;
import com.ruijing.store.order.business.service.HMSWechatOrderService;
import com.ruijing.store.order.business.service.OrderApprovalService;
import com.ruijing.store.order.business.service.OrderDetailRelatedService;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderConfigsVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderListRespVO;
import com.ruijing.store.order.rpc.client.UserClient;

import javax.annotation.Resource;
import java.text.ParseException;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2021/9/13 11:48
 */
@MSharpService(isGateway = "true")
@RpcApi(value = "微信网关服务",description = "微信网关服务")
@RpcMapping("/wechat")
public class WechatGWController {

    @Resource
    private UserClient userClient;

    @Resource
    private BuyerOrderService buyerOrderService;

    @Resource
    private OrderDetailRelatedService orderDetailRelatedService;

    @Resource
    private HMSWechatOrderService hmsWechatOrderService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderApprovalService orderApprovalService;

    @RpcMethod("wechat订单管理-我的订单")
    @RpcMapping("/getOrderList")
    public PageableResponse<OrderListRespVO> getMyOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request) throws ParseException {
        BusinessErrUtil.notNull(request,"请求我的订单入参不可为空");
        BusinessErrUtil.isTrue(request.getPageNo() > 0 && request.getPageSize() > 0, "分页参数有误");
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), orgId, true, ConfigConstant.ORDER_VIEW);
        // 微信订单的特殊权限逻辑
        LoginUserInfoBO loginInfoBuyerCenter = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), orgId, true, ConfigConstant.BUYER_CENTER);
        loginInfo.getDeptIdList().addAll(loginInfoBuyerCenter.getDeptIdList());
        loginInfo.getDeptList().addAll(loginInfoBuyerCenter.getDeptList());

        // 如果是取待验收审批状态的订单，则返回采购人中心待我审批订单的数据
        if (OrderStatusEnum.OrderReceiveApproval.getValue().equals(request.getStatus())) {
            return orderApprovalService.getMyPendingOrderList(request, loginInfo, false);
        }
        
        request.setMyOrderCheck(false);
        request.setExcludeStatusList(New.list(OrderStatusEnum.ORDER_SPLIT_UP.getValue()));
        return buyerOrderService.getOrderListForWWW(request, loginInfo,false);
    }


    @RpcMethod("wechat订单管理-我的订单-已审批")
    @RpcMapping("/getApproveOrderList")
    public PageableResponse<OrderListRespVO> getApproveOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request) throws ParseException {
        BusinessErrUtil.notNull(request,"请求我的订单入参不可为空");
        BusinessErrUtil.isTrue(request.getPageNo() > 0 && request.getPageSize() > 0, "分页参数有误");
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), orgId, true, ConfigConstant.ORDER_VIEW);
        // 微信订单的特殊权限逻辑
        LoginUserInfoBO loginInfoBuyerCenter = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), orgId, true, ConfigConstant.BUYER_CENTER);
        loginInfo.getDeptIdList().addAll(loginInfoBuyerCenter.getDeptIdList());
        loginInfo.getDeptList().addAll(loginInfoBuyerCenter.getDeptList());
        return orderApprovalService.myApprovedList(request, loginInfo, false);
    }

    @RpcMethod("wechat订单管理-订单详情")
    @RpcMapping("/orderDetail")
    public RemoteResponse<OrderInfoVO> getOrderDetail(RjSessionInfo rjSessionInfo, OrderBasicParamDTO request) throws ParseException {
        // 订单号与订单id查询的兼容
        BusinessErrUtil.isTrue(request.getOrderId() != null || request.getOrderNo() != null, ExecptionMessageEnum.ENTER_ORDER_NUMBER_OR_ID);
        OrderMasterDO orderMasterInfo;
        // 没传入订单主表数据，才去查
        if (request.getOrderId() != null) {
            orderMasterInfo = orderMasterMapper.selectByPrimaryKey(request.getOrderId());
        } else {
            orderMasterInfo = orderMasterMapper.findByForderno(request.getOrderNo());
        }
        BusinessErrUtil.notNull(orderMasterInfo, ExecptionMessageEnum.CANNOT_FIND_ORDER_PLEASE_RETRY);

        // 防止多单位下点击微信卡片无法跳转，先查订单数据，将其机构id作为orgId传入
        Integer orgId = orderMasterInfo.getFuserid();
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), orgId, true, ConfigConstant.ORDER_VIEW);
        // 微信订单的特殊权限逻辑
        LoginUserInfoBO loginInfoBuyerCenter = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), orgId, true, ConfigConstant.BUYER_CENTER);
        loginInfo.getDeptIdList().addAll(loginInfoBuyerCenter.getDeptIdList());
        loginInfo.getDeptList().addAll(loginInfoBuyerCenter.getDeptList());

        OrderInfoVO orderInfo = orderDetailRelatedService.getOrderDetail(loginInfo, request, false);
        return RemoteResponse.<OrderInfoVO>custom().setData(orderInfo).setSuccess();
    }

    @RpcMethod("wechat订单管理-订单计数")
    @RpcMapping("/getOrderCount")
    public RemoteResponse<Integer> getOrderCount(RjSessionInfo rjSessionInfo, OrderListRequest request) {
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), orgId, true, ConfigConstant.ORDER_VIEW);
        Integer orderCount = hmsWechatOrderService.getOrderCountByStatus(loginInfo, request);
        return RemoteResponse.<Integer>custom().setData(orderCount).setSuccess();
    }


    /**
     * store迁移-获取订单相关OMS配置
     *
     * @param rjSessionInfo
     * @return
     */
    @RpcMapping("/order/getConfigsAboutOrder")
    @RpcMethod(value = "获取订单相关OMS配置")
    public RemoteResponse<OrderConfigsVO> getConfigsAboutOrder(RjSessionInfo rjSessionInfo) {
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(), true, ConfigConstant.ORDER_VIEW);
        RemoteResponse<OrderConfigsVO> remoteResponse = hmsWechatOrderService.getOrderConfig(loginInfo.getOrgCode());
        return remoteResponse;
    }


    /**
     * store迁移-查询用户拍照验收配置项
     *
     * @param rjSessionInfo
     */
    @RpcMapping("/order/getPictureConfig")
    @RpcMethod(value = "获取拍照验收配置项", notes = "{\n" +
            "orgCode: \"医院编码\",\n" +
            "configCode: \"ORG_RECEIPT_PIC_CONFIG\",\n" +
            "configValue:  \"0无需拍照验收/1强制拍照验收/2非强制拍照验收/3除服务类强制拍照验收\"\n" +
            "}")
    public RemoteResponse<OrgConfigResponseDTO> getPictureConfig(RjSessionInfo rjSessionInfo) {
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(), true, ConfigConstant.ORDER_VIEW);
        Preconditions.notNull(loginInfo, "用户信息获取失败");
        // 查询用户配置项 rpc 接口
        OrgConfigResponseDTO orgConfigResponseDTO = hmsWechatOrderService.findSingleConfigByOrgCode(loginInfo.getOrgCode(), ConfigConstant.ORG_RECEIPT_PIC_CONFIG);
        return RemoteResponse.<OrgConfigResponseDTO>custom().setSuccess().setData(orgConfigResponseDTO).build();
    }
}
