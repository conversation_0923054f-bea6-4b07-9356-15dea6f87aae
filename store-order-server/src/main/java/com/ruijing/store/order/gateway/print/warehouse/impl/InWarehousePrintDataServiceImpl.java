package com.ruijing.store.order.gateway.print.warehouse.impl;

import com.google.common.collect.Lists;
import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.shop.category.api.dto.CategoryDTO;
import com.ruijing.shop.category.api.enums.InboundTypeEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderDetailExtraEnum;
import com.ruijing.store.order.api.base.other.dto.OrderPurchaseApprovalLogDTO;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.gateway.print.dto.warehouse.InWarehousePrintDataDTO;
import com.ruijing.store.order.gateway.print.dto.warehouse.WarehouseProductPrintDataDTO;
import com.ruijing.store.order.gateway.print.service.impl.GetPrintCommonDataService;
import com.ruijing.store.order.gateway.print.util.ApprovalLogTranslator;
import com.ruijing.store.order.gateway.print.util.WarehouseDataTranslator;
import com.ruijing.store.order.gateway.print.warehouse.InWarehousePrintDataService;
import com.ruijing.store.order.rpc.client.BizWareHouseClient;
import com.ruijing.store.order.rpc.client.CategoryServiceClient;
import com.ruijing.store.order.rpc.client.OrderDetailExtraClient;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.warehouse.utils.CategoryUtil;
import com.ruijing.store.warehouse.utils.PriceUtil;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDetailDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryLogDTO;
import com.ruijing.store.wms.api.dto.WmsPersionDTO;
import com.ruijing.store.wms.api.enums.ApprovalTaskStatusEnum;
import com.ruijing.store.wms.api.enums.InboundStatus;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2023/3/1 11:43
 * @description
 */
@Service
public class InWarehousePrintDataServiceImpl implements InWarehousePrintDataService {
    
    /**
     * 需要获取入库审核人的单位code
     */
    private static final List<String> ORG_CODE_NEED_WAREHOUSE_APPROVER_TO_PRINT = Lists.newArrayList(OrgEnum.YUE_BEI_REN_MIN_YI_YUAN.getCode(),
            OrgEnum.GUANG_DONG_YI_KE_DA_XUE.getCode(), OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode(),
            OrgEnum.GUANG_ZHOU_HONG_SHI_ZI_HUI_YI_YUAN.getCode(), OrgEnum.ZHONG_SHAN_SHI_REN_MIN_YI_YUAN.getCode());

    /**
     * 需要打印入库单条形码的单位code
     */
    private static final List<String> ORG_CODE_NEED_ENTRY_NO_BARCODE = Lists.newArrayList(OrgEnum.GUANG_DONG_YAO_KE_DA_XUE.getCode());

    @Resource
    private BizWareHouseClient bizWareHouseClient;

    @Resource
    private GetPrintCommonDataService getPrintCommonDataService;

    @Resource
    private CategoryServiceClient categoryServiceClient;

    @Resource
    private OrderDetailExtraClient orderDetailExtraClient;

    @Override
    public List<BizWarehouseEntryDTO> getBizWarehouseEntryDtoListByOrderNo(List<String> orderNoList) {
        //根据订单号查找入库单列表,入库单查询接口比较特殊，商品详情需要额外根据入库单号查找
        List<BizWarehouseEntryDTO> warehouseApplicationInfoList = bizWareHouseClient.findEntryByOrderNoList(orderNoList);
        //根据入库单id列表查找入库单商品信息
        if (CollectionUtils.isEmpty(warehouseApplicationInfoList)) {
            return New.emptyList();
        }
        // 撤销入库需求——单据打印去除已撤销、撤销中、未入库的出入库单据；
        warehouseApplicationInfoList = warehouseApplicationInfoList.stream().filter(s -> (!InboundStatus.HAVE_BEEN_WITHDRAW.getValue().equals(s.getStatus()))
                && (!InboundStatus.HAVING_WITHDRAW.getValue().equals(s.getStatus()))
                && (!InboundStatus.NOTINSTORAGE.getValue().equals(s.getStatus()))).collect(toList());
        if (CollectionUtils.isEmpty(warehouseApplicationInfoList)) {
            return New.emptyList();
        }
        // 查商品
        List<Integer> entryIdList = warehouseApplicationInfoList.stream().map(BizWarehouseEntryDTO::getId).collect(Collectors.toList());
        List<BizWarehouseEntryDetailDTO> bizWarehouseEntryDetailDTOList = bizWareHouseClient.findEntryDetailByIdList(entryIdList);
        Preconditions.notEmpty(bizWarehouseEntryDetailDTOList, "查询入库单失败：" + JsonUtils.toJson(entryIdList));
        Map<Integer, List<BizWarehouseEntryDetailDTO>> entryIdDetailMap = DictionaryUtils.groupBy(bizWarehouseEntryDetailDTOList, BizWarehouseEntryDetailDTO::getEntryId);
        // 查日志
        Map<Integer, List<BizWarehouseEntryLogDTO>> entryIdLogMap = bizWareHouseClient.queryLogByEntryId(entryIdList);

        //把入库单商品、日志信息放到入库单列表中
        warehouseApplicationInfoList.forEach(bizWarehouseEntryDTO ->{
            bizWarehouseEntryDTO.setDetailDTOList(entryIdDetailMap.get(bizWarehouseEntryDTO.getId()));
            bizWarehouseEntryDTO.setLogDTOList(entryIdLogMap.get(bizWarehouseEntryDTO.getId()));
        });

        return warehouseApplicationInfoList;
    }

    @Override
    public List<InWarehousePrintDataDTO> getInWarehousePrintData(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList, List<BizWarehouseEntryDTO> warehouseApplicationInfoList) {
        List<InWarehousePrintDataDTO> inWarehousePrintDataDTOList = new ArrayList<>();
        // 入库成功的日志
        Map<Integer, List<BizWarehouseEntryLogDTO>> entryIdApprovalSuccessLogMap  = bizWareHouseClient.queryEntryApprovalSuccessLog(warehouseApplicationInfoList.stream().map(BizWarehouseEntryDTO::getId).collect(toList()));
        for (BizWarehouseEntryDTO bizWarehouseEntryDTO : warehouseApplicationInfoList) {
            if (InboundStatus.WAREHOUSING.getValue().equals(bizWarehouseEntryDTO.getStatus())) {
                // 先装填入库数据相关数据
                InWarehousePrintDataDTO inWarehousePrintDataDTO = this.constructWarehouseData(orderMasterDO, bizWarehouseEntryDTO, orderDetailDOList, entryIdApprovalSuccessLogMap.get(bizWarehouseEntryDTO.getId()));
                inWarehousePrintDataDTOList.add(inWarehousePrintDataDTO);
            }
        }
        return inWarehousePrintDataDTOList;
    }

    

    /**
     * 组装入库单相关数据
     * @param orgCode 机构代码
     * @param bizWarehouseEntryDTO 入库数据
     * @param orderDetailDOList 订单详情数据
     * @return 入库打印数据
     */
    private InWarehousePrintDataDTO constructWarehouseData(OrderMasterDO orderMasterDO, BizWarehouseEntryDTO bizWarehouseEntryDTO, List<OrderDetailDO> orderDetailDOList, List<BizWarehouseEntryLogDTO> approvalSuccessLogList) {
        InWarehousePrintDataDTO whData = new InWarehousePrintDataDTO();
        //封装入库单信息
        whData.setWarehouseApplicationNo(bizWarehouseEntryDTO.getEntryNo());
        whData.setWarehouseApplicationId(bizWarehouseEntryDTO.getId());
        whData.setApprovalStatus(bizWarehouseEntryDTO.getApprovalStatus());
        whData.setApprovalStatusName(bizWarehouseEntryDTO.getApprovalStatus() == null ? null : ApprovalTaskStatusEnum.valueOf(bizWarehouseEntryDTO.getApprovalStatus()).getDesc());
        whData.setStatus(bizWarehouseEntryDTO.getStatus());
        whData.setStatusName(bizWarehouseEntryDTO.getStatus() == null ? null : InboundStatus.valueOf(bizWarehouseEntryDTO.getStatus()).getDesc());
        whData.setWarehouseApplicant(bizWarehouseEntryDTO.getApplyUserName());
        whData.setWarehouseApplicationTime(bizWarehouseEntryDTO.getCreateTime() == null ? null : bizWarehouseEntryDTO.getCreateTime().getTime());
        whData.setWarehouseId(bizWarehouseEntryDTO.getRoomId());
        whData.setWarehouseName(bizWarehouseEntryDTO.getRoomName());
        whData.setInWarehouseTime(bizWarehouseEntryDTO.getEntryTime() == null ? null : DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, bizWarehouseEntryDTO.getEntryTime()).getTime());
        whData.setRemark(bizWarehouseEntryDTO.getRemark());
        whData.setBusinessType(bizWarehouseEntryDTO.getBusinessType());
        if (StringUtils.isNotBlank(bizWarehouseEntryDTO.getReceivePicUrls())) {
            whData.setInWarehousePictureUrlList(Arrays.asList(bizWarehouseEntryDTO.getReceivePicUrls().split(";")));
        }

        //获取入库审核人和审核时间
        if (bizWarehouseEntryDTO.getApprovalStatus() != null) {
            List<WmsPersionDTO> wmsPersonDTOList = bizWareHouseClient.queryEntryPersonByEntryNoList(Collections.singletonList(bizWarehouseEntryDTO.getEntryNo()));
            if (CollectionUtils.isNotEmpty(wmsPersonDTOList)) {
                whData.setApproverName(wmsPersonDTOList.get(0).getOperator());
                String optTimeStr = wmsPersonDTOList.get(0).getOptTime();
                whData.setApprovalTimeString(wmsPersonDTOList.get(0).getOptTime());
                whData.setApprovalTime(optTimeStr != null ? DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, optTimeStr).getTime() : null);
            }
        }

        if (ORG_CODE_NEED_ENTRY_NO_BARCODE.contains(orderMasterDO.getFusercode())) {
            //获取入库单号对应条形码
            whData.setEntryNoBarcode(getPrintCommonDataService.getBarCode(bizWarehouseEntryDTO.getEntryNo()));
        }

        List<BizWarehouseEntryDetailDTO> inWarehousehouseDetailList = bizWarehouseEntryDTO.getDetailDTOList();
        //提取申请单相关商品信息
        whData.setWarehouseProductInfoVOList(this.constructInWarehouseProduct(inWarehousehouseDetailList, orderDetailDOList));
        String totalPrice = whData.getWarehouseProductInfoVOList().stream().map(product -> new BigDecimal(product.getTotalPrice())).reduce(BigDecimal.ZERO, BigDecimal::add).toString();
        whData.setTotalPrice(totalPrice);
        whData.setTotalPriceInChinese(PriceUtil.convert(totalPrice));
        // 计算入库单商品数量总和
        int totalProductQuantity = 0;
        if (CollectionUtils.isNotEmpty(whData.getWarehouseProductInfoVOList())) {
            totalProductQuantity = whData.getWarehouseProductInfoVOList().stream()
                    .filter(Objects::nonNull)
                    .mapToInt(product -> Objects.nonNull(product.getQuantity()) ? product.getQuantity() : 0)
                    .sum();
        }
        whData.setTotalProductQuantity(totalProductQuantity);
        List<OrderPurchaseApprovalLogDTO> whLogList = ApprovalLogTranslator.warehouseLog2ListDTO(this.getLastPassLog(bizWarehouseEntryDTO.getLogDTOList()));
        if(whLogList != null){
            whData.setWareHouseApprovalLog(whLogList.stream().map(ApprovalLogTranslator::orderPurchaseApprovalLog2PrintLog).collect(toList()));
        }
        if(CollectionUtils.isNotEmpty(approvalSuccessLogList)){
            // 入库日志（扁平）
            whData.setWareHouseApprovalFlatLog(ApprovalLogTranslator.orderPurchaseApprovalLog2FlatListDTO(ApprovalLogTranslator.warehouseLog2ListDTO(approvalSuccessLogList)));
        }
        // 设置顶级分类 金额字段
        setTopCategoryAmount(whData);
        return whData;
    }

    /**
     * 设置顶级分类金额字段
     * 
     * @param whData 入库打印数据
     */
    private void setTopCategoryAmount(InWarehousePrintDataDTO whData) {
        if (Objects.isNull(whData) || CollectionUtils.isEmpty(whData.getWarehouseProductInfoVOList())) {
            return;
        }

        // 获取所有一级分类ID
        List<Integer> firstLevelCategoryIds = whData.getWarehouseProductInfoVOList().stream()
                .map(WarehouseProductPrintDataDTO::getFirstLevelCategoryId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(firstLevelCategoryIds)) {
            return;
        }

        // 获取分类信息
        List<Long> categoryLongIds = firstLevelCategoryIds.stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        List<CategoryDTO> categoryDTOList = categoryServiceClient.batchLoadSelf(categoryLongIds);

        if (CollectionUtils.isEmpty(categoryDTOList)) {
            return;
        }

        // 构建分类ID与分类信息的映射
        Map<Integer, CategoryDTO> categoryMap = categoryDTOList.stream()
                .collect(Collectors.toMap(c -> c.getId().intValue(), Function.identity(), (o, n) -> n));

        // 按inboundType分类计算金额
        BigDecimal reagentTotalPrice = BigDecimal.ZERO;
        BigDecimal consumablesAndAnimalTotalPrice = BigDecimal.ZERO;

        for (WarehouseProductPrintDataDTO product : whData.getWarehouseProductInfoVOList()) {
            if (Objects.isNull(product.getFirstLevelCategoryId()) || StringUtils.isBlank(product.getTotalPrice())) {
                continue;
            }

            CategoryDTO categoryDTO = categoryMap.get(product.getFirstLevelCategoryId());
            if (Objects.isNull(categoryDTO) || Objects.isNull(categoryDTO.getInboundType())) {
                continue;
            }

            BigDecimal productPrice = new BigDecimal(product.getTotalPrice());

            // 试剂类型
            if (Objects.equals(categoryDTO.getInboundType(), InboundTypeEnum.REAGENT.getValue())) {
                reagentTotalPrice = reagentTotalPrice.add(productPrice);
            } else if (Objects.equals(categoryDTO.getInboundType(), InboundTypeEnum.CONSUMABLES.getValue())
                    || Objects.equals(categoryDTO.getInboundType(), InboundTypeEnum.ANIMAL.getValue())) {
                // 耗材、动物类型
                consumablesAndAnimalTotalPrice = consumablesAndAnimalTotalPrice.add(productPrice);
            }
        }

        whData.setReagentTotalPrice(reagentTotalPrice.setScale(2, RoundingMode.HALF_UP).toString());
        whData.setConsumablesAndAnimalTotalPrice(consumablesAndAnimalTotalPrice.setScale(2, RoundingMode.HALF_UP).toString());
    }

    /**
     * 获取最后一次审批驳回后通过的日志
     * @param originalLogList 入库日志
     * @return 最后一次审批驳回后审批通过的记录
     */
    private List<BizWarehouseEntryLogDTO> getLastPassLog(List<BizWarehouseEntryLogDTO> originalLogList){
        if(CollectionUtils.isEmpty(originalLogList)){
            return New.emptyList();
        }
        // 先获取有序的库房审批日志
        List<BizWarehouseEntryLogDTO> whApprovalLogInOrder = originalLogList.stream().filter(log->log.getBusinessType() == 1).sorted(Comparator.comparing(BizWarehouseEntryLogDTO::getCreateTime)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(whApprovalLogInOrder)){
            return New.emptyList();
        }
        int lastRejectLogIdx = -1;
        final String rejectText = "入库审批拒绝";
        for(int i = whApprovalLogInOrder.size() - 1; i > -1; i--){
            String businessDesc = whApprovalLogInOrder.get(i).getBusinessDesc();
            if(StringUtils.isNotEmpty(businessDesc) && businessDesc.contains(rejectText)){
                lastRejectLogIdx = i;
                break;
            }
        }
        return New.list(whApprovalLogInOrder.subList(lastRejectLogIdx + 1, whApprovalLogInOrder.size()));
    }

    
    /**
     * 入库商品数据装填
     * @param entryDetails 入库商品数据
     * @param orderDetailDOList 订单商品数据
     * @return 入库商品数据
     */
    private List<WarehouseProductPrintDataDTO> constructInWarehouseProduct(List<BizWarehouseEntryDetailDTO> entryDetails, List<OrderDetailDO> orderDetailDOList) {
        // 映射为商品详情id和货号的map，用于补充数据
        Map<String, OrderDetailDO> goodCodeAndOrderDetailMap = orderDetailDOList.stream().collect(Collectors.toMap(o -> o.getFgoodcode().trim(), Function.identity(), (o, n) -> n));
        Map<Integer, OrderDetailDO> detailIdAndOrderDetailMap = orderDetailDOList.stream().collect(Collectors.toMap(OrderDetailDO::getId, Function.identity(), (o, n) -> n));
        List<WarehouseProductPrintDataDTO> warehouseProductPrintDataDTOList = new ArrayList<>();
        // 这里暂存detailID和结果映射，根据批次划分用到
        Map<WarehouseProductPrintDataDTO, Integer> result2DetailId = New.map();
        for (BizWarehouseEntryDetailDTO entryDetail : entryDetails) {
            Preconditions.notNull(entryDetail.getProductCode(), "入库商品的货号为空：" + entryDetail.getId());
            OrderDetailDO matchDetail;
            // 优先detailId,旧数据没有的用货号来匹配
            if (entryDetail.getOrderDetailId() != null) {
                matchDetail = detailIdAndOrderDetailMap.get(entryDetail.getOrderDetailId());
            } else {
                matchDetail = goodCodeAndOrderDetailMap.get(entryDetail.getProductCode().trim());
            }
            BusinessErrUtil.notNull(matchDetail, ExecptionMessageEnum.INBOUND_ORDER_DETAILS_NOT_FOUND, entryDetail.getProductCode().trim(), entryDetail.getOrderDetailId());
            WarehouseProductPrintDataDTO warehouseProductPrintDataDTO = WarehouseDataTranslator.inWarehouseProductDto2PrintDTO(entryDetail);
            //商品单价
            BigDecimal singleProductPrice = entryDetail.getUnitPrice() != null && entryDetail.getUnitPrice().compareTo(BigDecimal.ZERO) > 0 ? entryDetail.getUnitPrice() : matchDetail.getFbidprice();
            warehouseProductPrintDataDTO.setSinglePrice(singleProductPrice.toString());
            //商品总价
            BigDecimal singleProductTotalPrice = entryDetail.getPrice() != null && entryDetail.getPrice().compareTo(BigDecimal.ZERO) > 0 ?
                    entryDetail.getPrice() : singleProductPrice.multiply(BigDecimal.valueOf(entryDetail.getReceivedNum())).add(entryDetail.getRemainderPrice());
            warehouseProductPrintDataDTO.setTotalPrice(singleProductTotalPrice.toString());

            warehouseProductPrintDataDTO.setCategoryId(matchDetail.getCategoryid());
            warehouseProductPrintDataDTO.setProductId(matchDetail.getProductSn());
            warehouseProductPrintDataDTO.setCategoryTag(matchDetail.getCategoryTag());
            warehouseProductPrintDataDTO.setFirstLevelCategoryId(matchDetail.getFirstCategoryId());
            warehouseProductPrintDataDTO.setOrderDetailId(matchDetail.getId());
            warehouseProductPrintDataDTOList.add(warehouseProductPrintDataDTO);
            result2DetailId.put(warehouseProductPrintDataDTO, entryDetail.getOrderDetailId());
        }

        //获取商品一级分类(个性化)
        List<Integer> categoryIds = warehouseProductPrintDataDTOList.stream().map(WarehouseProductPrintDataDTO::getCategoryId).distinct().collect(Collectors.toList());
        List<CategoryDTO> categoryDTOList = categoryServiceClient.getAllCategoryByIds(categoryIds);
        BusinessErrUtil.notEmpty(categoryDTOList, ExecptionMessageEnum.FAILED_TO_OBTAIN_PRODUCT_CATEGORY_INFO);
        warehouseProductPrintDataDTOList.forEach(product -> {
            CategoryDTO categoryDTO = CategoryUtil.getFirstLevelCategory(product.getCategoryId(), categoryDTOList);
            BusinessErrUtil.notNull(categoryDTO, ExecptionMessageEnum.PRIMARY_CATEGORY_INFO_NOT_FOUND, product.getCategoryId());
            product.setFirstLevelCategoryName(categoryDTO.getName());
        });

        // 填充订单详情扩展信息
        fillOrderDetailExtraInfo(warehouseProductPrintDataDTOList);

        // 有批次则-按批次维度来构建数据
        List<WarehouseProductPrintDataDTO> secondResult = New.list();
        warehouseProductPrintDataDTOList.forEach(warehouseDTO -> {
            Integer detailId = result2DetailId.get(warehouseDTO);
            warehouseDTO.setOrderDetailId(detailId);
        });

        return CollectionUtils.isNotEmpty(secondResult) ? secondResult : warehouseProductPrintDataDTOList;
    }

    /**
     * 填充订单详情扩展信息
     *
     * @param warehouseProductPrintDataDTOList 商品打印数据列表
     */
    private void fillOrderDetailExtraInfo(List<WarehouseProductPrintDataDTO> warehouseProductPrintDataDTOList) {
        if (CollectionUtils.isEmpty(warehouseProductPrintDataDTOList)) {
            return;
        }

        // 获取所有需要查询扩展信息的订单详情ID
        List<Integer> orderDetailIds = warehouseProductPrintDataDTOList.stream()
                .map(WarehouseProductPrintDataDTO::getOrderDetailId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderDetailIds)) {
            return;
        }

        // 查询订单详情扩展信息
        List<OrderDetailExtraDTO> orderDetailExtraDTOList = orderDetailExtraClient.listOrderDetailExtra(null, orderDetailIds);
        if (CollectionUtils.isEmpty(orderDetailExtraDTOList)) {
            return;
        }

        // 按扩展类型分组
        Map<Integer, Map<Integer, String>> orderDetailId2ExtraInfoMap = orderDetailExtraDTOList.stream()
                .collect(Collectors.groupingBy(
                        OrderDetailExtraDTO::getOrderDetailId,
                        Collectors.toMap(
                                OrderDetailExtraDTO::getExtraKeyType,
                                OrderDetailExtraDTO::getExtraValue,
                                (v1, v2) -> v1
                        )
                ));

        // 填充扩展信息到商品数据中
        warehouseProductPrintDataDTOList.forEach(product -> {
            if (Objects.nonNull(product.getOrderDetailId())) {
                Map<Integer, String> extraInfoMap = orderDetailId2ExtraInfoMap.get(product.getOrderDetailId());
                if (Objects.nonNull(extraInfoMap)) {
                    // 填充医疗器械注册证书编号
                    String medicalDeviceRegisCertNumber = extraInfoMap.get(OrderDetailExtraEnum.MEDICAL_DEVICE_REGIS_CERT_NUMBER.getType());
                    product.setMedicalDeviceRegisCertNumber(medicalDeviceRegisCertNumber);
                }
            }
        });
    }
}
