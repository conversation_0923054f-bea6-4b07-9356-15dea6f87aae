package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020/11/17 17:54
 * @Description
 **/
public class OrderOfflineInfoVO implements Serializable {

    private static final long serialVersionUID = 3654478748333680295L;

    @RpcModelProperty(value = "订单id")
    private Integer orderId;

    @RpcModelProperty(value = "是否线下单")
    private Boolean offline;

    @RpcModelProperty(value = "线下单购买渠道")
    private String procurementChannel;

    @RpcModelProperty(value = "线下单其他信息")
    private OrderOfflineExtraVO offlineExtraDto;

    public Boolean isOffline() {
        return offline;
    }

    public void setOffline(Boolean offline) {
        this.offline = offline;
    }

    public String getProcurementChannel() {
        return procurementChannel;
    }

    public void setProcurementChannel(String procurementChannel) {
        this.procurementChannel = procurementChannel;
    }

    public OrderOfflineExtraVO getOfflineExtraDto() {
        return offlineExtraDto;
    }

    public void setOfflineExtraDto(OrderOfflineExtraVO offlineExtraDto) {
        this.offlineExtraDto = offlineExtraDto;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public OrderOfflineInfoVO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderOfflineInfoVO{");
        sb.append("orderId=").append(orderId);
        sb.append(", offline=").append(offline);
        sb.append(", procurementChannel='").append(procurementChannel).append('\'');
        sb.append(", offlineExtraDto=").append(offlineExtraDto);
        sb.append('}');
        return sb.toString();
    }
}
