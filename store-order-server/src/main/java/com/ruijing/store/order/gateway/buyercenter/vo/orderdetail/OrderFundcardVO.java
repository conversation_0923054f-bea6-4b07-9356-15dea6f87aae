package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;


import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2020/12/23 11:50
 */
public class OrderFundcardVO implements Serializable {

    private static final long serialVersionUID = -6662418741268453419L;

    private Integer orderId;

    private String cardId;

    private String cardNo;

    private String cardName;

    private String cardsStartTime;

    /**
     * 中科大附一特殊，经费卡院区名字
     */
    @ModelProperty("中科大附一特殊，经费卡院区名字")
    private String campusName;

    @ModelProperty("中科大附一特殊，经费卡科室编码")
    private String deptCode;

    /**
     * 旧打印把这个当做了fundTypeName提供给了前端，后续尽量统一
     */
    @ModelProperty("经费类型")
    private String fundType;

    @ModelProperty("经费支出申请单号")
    private String expenseApplyNo;

    @ModelProperty("经费类型名称")
    private String fundTypeName;

    /**
     * 对外并未使用，目前仅用做验收按钮判断
     */
    @ModelProperty("经费负责人id")
    private List<Integer> fundManagerUserIds;

    @ModelProperty("一级卡号")
    private String firstLevelCardNo;

    public Integer getOrderId() {
        return orderId;
    }

    public OrderFundcardVO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public String getCardId() {
        return cardId;
    }

    public OrderFundcardVO setCardId(String cardId) {
        this.cardId = cardId;
        return this;
    }

    public String getCardNo() {
        return cardNo;
    }

    public OrderFundcardVO setCardNo(String cardNo) {
        this.cardNo = cardNo;
        return this;
    }

    public String getCardName() {
        return cardName;
    }

    public OrderFundcardVO setCardName(String cardName) {
        this.cardName = cardName;
        return this;
    }

    public String getCardsStartTime() {
        return cardsStartTime;
    }

    public OrderFundcardVO setCardsStartTime(String cardsStartTime) {
        this.cardsStartTime = cardsStartTime;
        return this;
    }

    public String getCampusName() {
        return campusName;
    }

    public OrderFundcardVO setCampusName(String campusName) {
        this.campusName = campusName;
        return this;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public OrderFundcardVO setDeptCode(String deptCode) {
        this.deptCode = deptCode;
        return this;
    }

    public String getFundType() {
        return fundType;
    }

    public OrderFundcardVO setFundType(String fundType) {
        this.fundType = fundType;
        return this;
    }

    public String getExpenseApplyNo() {
        return expenseApplyNo;
    }

    public OrderFundcardVO setExpenseApplyNo(String expenseApplyNo) {
        this.expenseApplyNo = expenseApplyNo;
        return this;
    }

    public String getFundTypeName() {
        return fundTypeName;
    }

    public OrderFundcardVO setFundTypeName(String fundTypeName) {
        this.fundTypeName = fundTypeName;
        return this;
    }

    public List<Integer> getFundManagerUserIds() {
        return fundManagerUserIds;
    }

    public OrderFundcardVO setFundManagerUserIds(List<Integer> fundManagerUserIds) {
        this.fundManagerUserIds = fundManagerUserIds;
        return this;
    }

    public String getFirstLevelCardNo() {
        return firstLevelCardNo;
    }

    public OrderFundcardVO setFirstLevelCardNo(String firstLevelCardNo) {
        this.firstLevelCardNo = firstLevelCardNo;
        return this;
    }

    @Override
    public String toString() {
        return "OrderFundcardVO{" +
                "orderId=" + orderId +
                ", cardId='" + cardId + '\'' +
                ", cardNo='" + cardNo + '\'' +
                ", cardName='" + cardName + '\'' +
                ", cardsStartTime='" + cardsStartTime + '\'' +
                ", campusName='" + campusName + '\'' +
                ", deptCode='" + deptCode + '\'' +
                ", fundType='" + fundType + '\'' +
                ", expenseApplyNo='" + expenseApplyNo + '\'' +
                ", fundTypeName='" + fundTypeName + '\'' +
                ", fundManagerUserIds=" + fundManagerUserIds +
                ", firstLevelCardNo='" + firstLevelCardNo + '\'' +
                '}';
    }
}
