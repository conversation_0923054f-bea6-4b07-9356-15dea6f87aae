package com.ruijing.store.order.business.bo;

import java.math.BigDecimal;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/3/24 16:25
 **/
public class ThirdPartyPlatformOrderDetailBO {
    /**
     * 订单明细id
     */
    private Integer detailId;

    /**
     * 明细商品数量
     */
    private BigDecimal quantity;

    /**
     * 商品id
     */
    private String goodsId;

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ThirdPartyPlatformOrderDetailBO{");
        sb.append("detailId=").append(detailId);
        sb.append(", quantity=").append(quantity);
        sb.append(", goodsId=").append(goodsId);
        sb.append('}');
        return sb.toString();
    }
}
