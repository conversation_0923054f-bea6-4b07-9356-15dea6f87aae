package com.ruijing.store.order.gateway.fundcard.request;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 经费卡换卡入参
 */
public class OrderFundCardParam implements Serializable {
    private static final long serialVersionUID = -4201117612371544330L;

    /**
     * 经费项目id
     */
    private String projectId;

    /**
     * 经费项目名
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 经费卡id
     */
    private String fundCardId;

    /**
     * 经费卡哈
     */
    private String fundCardNo;

    /**
     * 冻结金额
     */
    private BigDecimal freezeAmount;

    /**
     * 订单id
     */
    private Integer orderId;

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getFundCardId() {
        return fundCardId;
    }

    public void setFundCardId(String fundCardId) {
        this.fundCardId = fundCardId;
    }

    public String getFundCardNo() {
        return fundCardNo;
    }

    public void setFundCardNo(String fundCardNo) {
        this.fundCardNo = fundCardNo;
    }

    public BigDecimal getFreezeAmount() {
        return freezeAmount;
    }

    public void setFreezeAmount(BigDecimal freezeAmount) {
        this.freezeAmount = freezeAmount;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderFundCardParam{");
        sb.append("projectId='").append(projectId).append('\'');
        sb.append(", projectName='").append(projectName).append('\'');
        sb.append(", projectCode='").append(projectCode).append('\'');
        sb.append(", fundCardId='").append(fundCardId).append('\'');
        sb.append(", fundCardNo='").append(fundCardNo).append('\'');
        sb.append(", freezeAmount=").append(freezeAmount);
        sb.append(", orderId=").append(orderId);
        sb.append('}');
        return sb.toString();
    }
}
