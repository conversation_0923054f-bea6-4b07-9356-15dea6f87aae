package com.ruijing.store.order.gateway.print.dto.warehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/2 14:33
 * @description
 */
public class WareHouseOrderPrintDataDTO implements Serializable {

    private static final long serialVersionUID = 7975912893225967871L;
    
    @RpcModelProperty(value = "订单号")
    private String orderNo;

    @RpcModelProperty(value = "订单号对应条形码(base64编码后的字符串)")
    private String orderNoBarcode;

    @RpcModelProperty("订单号二维码")
    private String orderNoQrCode;

    @RpcModelProperty("微信订单详情二维码")
    private String wechatOrderDetailQrCode;

    @RpcModelProperty(value = "订单Id")
    private Integer orderId;

    @RpcModelProperty(value = "订单类型")
    private OrderTypeEnum orderType;

    @RpcModelProperty(value = "订单总金额")
    private BigDecimal orderTotalPrice;

    @RpcModelProperty(value = "采购人姓名")
    private String purchaserName;

    @RpcModelProperty(value = "采购人联系方式")
    private String purchaserPhone;

    @RpcModelProperty(value = "订单流程种类 0:线上, 1:线下")
    private Integer orderSpecies;

    @RpcModelProperty(value = "订单id")
    private Integer orgId;

    @RpcModelProperty(value = "单位名称")
    private String orgName;

    @RpcModelProperty(value = "该入库单对应的订单的供应商编码")
    private String supplierCode;

    @RpcModelProperty(value = "该入库单对应的订单的供应商名称")
    private String supplierName;

    @RpcModelProperty("供应商联系电话")
    private String supplierPhone;

    @RpcModelProperty(value = "部门名称")
    private String departmentName;

    @RpcModelProperty(value = "当前部门上一级部门名称")
    private String departmentParentName;

    @RpcModelProperty(value = "单位logo")
    private String orgLogo;

    @RpcModelProperty(value = "订单验收人(对宁波二院：验收审批人，暂时写死，其他机构取订单验收人)")
    private String acceptor;

    @RpcModelProperty("订单验收时间")
    private Long orderReceiptDate;

    @RpcModelProperty(value = "关联订单的收货图片")
    private List<String> receivedPictures;

    @RpcModelProperty(value = "该入库单对应的订单选择的经费卡号")
    private String funCardNo;

    @RpcModelProperty("经费负责人")
    private String fundCardManagerName;

    @RpcModelProperty("经费类型")
    private String fundTypeName;

    @RpcModelProperty(value = "该入库单对应的订单选择的经费卡所属的项目编码")
    private String projectCode;

    @RpcModelProperty(value = "该入库单对应的订单选择的经费卡所属的项目名称")
    private String projectName;

    @RpcModelProperty(value = "课题组负责人（订单的采购人所属的课题组的负责人姓名）")
    private String departmentDirector;

    @RpcModelProperty(value = "发票单号，多个的话用逗号隔开")
    private String invoiceNo;

    @RpcModelProperty(value = "发票号列表，发票号字段的冗余，适应单位个性需求, invoiceNo的列表化")
    private List<String> invoiceNoList;

    @RpcModelProperty(value = "发票时间，列表")
    private List<Long> invoiceDateTimeList;

    @RpcModelProperty("采购申请说明")
    private String purchaseNote;

    public String getFundTypeName() {
        return fundTypeName;
    }

    public WareHouseOrderPrintDataDTO setFundTypeName(String fundTypeName) {
        this.fundTypeName = fundTypeName;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderNoQrCode() {
        return orderNoQrCode;
    }

    public WareHouseOrderPrintDataDTO setOrderNoQrCode(String orderNoQrCode) {
        this.orderNoQrCode = orderNoQrCode;
        return this;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public OrderTypeEnum getOrderType() {
        return orderType;
    }

    public void setOrderType(OrderTypeEnum orderType) {
        this.orderType = orderType;
    }

    public BigDecimal getOrderTotalPrice() {
        return orderTotalPrice;
    }

    public void setOrderTotalPrice(BigDecimal orderTotalPrice) {
        this.orderTotalPrice = orderTotalPrice;
    }

    public String getPurchaserName() {
        return purchaserName;
    }

    public void setPurchaserName(String purchaserName) {
        this.purchaserName = purchaserName;
    }

    public String getPurchaserPhone() {
        return purchaserPhone;
    }

    public void setPurchaserPhone(String purchaserPhone) {
        this.purchaserPhone = purchaserPhone;
    }

    public Integer getOrderSpecies() {
        return orderSpecies;
    }

    public void setOrderSpecies(Integer orderSpecies) {
        this.orderSpecies = orderSpecies;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierPhone() {
        return supplierPhone;
    }

    public WareHouseOrderPrintDataDTO setSupplierPhone(String supplierPhone) {
        this.supplierPhone = supplierPhone;
        return this;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getDepartmentParentName() {
        return departmentParentName;
    }

    public void setDepartmentParentName(String departmentParentName) {
        this.departmentParentName = departmentParentName;
    }

    public String getOrgLogo() {
        return orgLogo;
    }

    public void setOrgLogo(String orgLogo) {
        this.orgLogo = orgLogo;
    }

    public String getAcceptor() {
        return acceptor;
    }

    public void setAcceptor(String acceptor) {
        this.acceptor = acceptor;
    }


    public Long getOrderReceiptDate() {
        return orderReceiptDate;
    }

    public WareHouseOrderPrintDataDTO setOrderReceiptDate(Long orderReceiptDate) {
        this.orderReceiptDate = orderReceiptDate;
        return this;
    }

    public List<String> getReceivedPictures() {
        return receivedPictures;
    }

    public void setReceivedPictures(List<String> receivedPictures) {
        this.receivedPictures = receivedPictures;
    }

    public String getFunCardNo() {
        return funCardNo;
    }

    public void setFunCardNo(String funCardNo) {
        this.funCardNo = funCardNo;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getDepartmentDirector() {
        return departmentDirector;
    }

    public void setDepartmentDirector(String departmentDirector) {
        this.departmentDirector = departmentDirector;
    }

    public String getOrderNoBarcode() {
        return orderNoBarcode;
    }

    public void setOrderNoBarcode(String orderNoBarcode) {
        this.orderNoBarcode = orderNoBarcode;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public List<String> getInvoiceNoList() {
        return invoiceNoList;
    }

    public void setInvoiceNoList(List<String> invoiceNoList) {
        this.invoiceNoList = invoiceNoList;
    }

    public List<Long> getInvoiceDateTimeList() {
        return invoiceDateTimeList;
    }

    public void setInvoiceDateTimeList(List<Long> invoiceDateTimeList) {
        this.invoiceDateTimeList = invoiceDateTimeList;
    }

    public String getPurchaseNote() {
        return purchaseNote;
    }

    public WareHouseOrderPrintDataDTO setPurchaseNote(String purchaseNote) {
        this.purchaseNote = purchaseNote;
        return this;
    }

    public String getWechatOrderDetailQrCode() {
        return wechatOrderDetailQrCode;
    }

    public WareHouseOrderPrintDataDTO setWechatOrderDetailQrCode(String wechatOrderDetailQrCode) {
        this.wechatOrderDetailQrCode = wechatOrderDetailQrCode;
        return this;
    }

    public String getFundCardManagerName() {
        return fundCardManagerName;
    }

    public WareHouseOrderPrintDataDTO setFundCardManagerName(String fundCardManagerName) {
        this.fundCardManagerName = fundCardManagerName;
        return this;
    }

    @Override
    public String toString() {
        return "WareHouseOrderPrintDataDTO{" +
                "orderNo='" + orderNo + '\'' +
                ", orderNoBarcode='" + orderNoBarcode + '\'' +
                ", orderNoQrCode='" + orderNoQrCode + '\'' +
                ", wechatOrderDetailQrCode='" + wechatOrderDetailQrCode + '\'' +
                ", orderId=" + orderId +
                ", orderType=" + orderType +
                ", orderTotalPrice=" + orderTotalPrice +
                ", purchaserName='" + purchaserName + '\'' +
                ", purchaserPhone='" + purchaserPhone + '\'' +
                ", orderSpecies=" + orderSpecies +
                ", orgId=" + orgId +
                ", orgName='" + orgName + '\'' +
                ", supplierCode='" + supplierCode + '\'' +
                ", supplierName='" + supplierName + '\'' +
                ", supplierPhone='" + supplierPhone + '\'' +
                ", departmentName='" + departmentName + '\'' +
                ", departmentParentName='" + departmentParentName + '\'' +
                ", orgLogo='" + orgLogo + '\'' +
                ", acceptor='" + acceptor + '\'' +
                ", orderReceiptDate=" + orderReceiptDate +
                ", receivedPictures=" + receivedPictures +
                ", funCardNo='" + funCardNo + '\'' +
                ", fundTypeName='" + fundTypeName + '\'' +
                ", projectCode='" + projectCode + '\'' +
                ", projectName='" + projectName + '\'' +
                ", departmentDirector='" + departmentDirector + '\'' +
                ", invoiceNo='" + invoiceNo + '\'' +
                ", invoiceNoList=" + invoiceNoList +
                ", invoiceDateTimeList=" + invoiceDateTimeList +
                ", purchaseNote='" + purchaseNote + '\'' +
                '}';
    }
}
