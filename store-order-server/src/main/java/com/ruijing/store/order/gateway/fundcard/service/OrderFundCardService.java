package com.ruijing.store.order.gateway.fundcard.service;


import com.reagent.research.api.dto.CallbackRequest;
import com.reagent.research.fundcard.dto.UnfreezeCallbackResult;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.other.dto.OrderFundCardResponseDTO;
import com.ruijing.store.order.api.base.other.dto.OrderUnFreezeRequestDTO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.gateway.fundcard.request.FundCardSpecialRequestDTO;
import com.ruijing.store.order.gateway.fundcard.request.OrderFundCardListParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2021/5/21 12:48
 * @Description: 经费卡换卡入参DTO
 */
public interface OrderFundCardService {

    /**
     * 保存修改经费卡缓存的信息
     * @param request
     * @return
     */
    RemoteResponse saveFundCardCache(FundCardSpecialRequestDTO request);

    /**
     * 订单释放经费/解冻
     * @param request
     * @return
     */
    boolean unfreezeFundCard(OrderBasicParamDTO request);

    /**
     * 重新解冻经费
     * @param request
     * @return
     */
    boolean reUnfreezeFundCard(OrderBasicParamDTO request);


    void orderFundCardUnFreeze(OrderMasterDO orderMasterDO, OrderUnFreezeRequestDTO unFreezeRequestDTO);

    /**
     * 订单释放经费/解冻
     * @param orderMasterDO         订单信息
     * @param unFreezeRequestDTO    解冻信息
     * @param callInterfaceFlag     是否调用外部接口
     */
    void orderFundCardUnFreeze(OrderMasterDO orderMasterDO, OrderUnFreezeRequestDTO unFreezeRequestDTO, boolean callInterfaceFlag);

    /**
     * 解冻回调
     * @param callbackRequest
     */
    void unFrozenCallback(CallbackRequest<UnfreezeCallbackResult> callbackRequest);

    /**
     * 查询课题组经费卡
     * @param rjSessionInfo
     * @param param
     * @return
     */
    List<OrderFundCardResponseDTO> findFundCardByDepartmentId(RjSessionInfo rjSessionInfo, OrderFundCardListParam param);
}
