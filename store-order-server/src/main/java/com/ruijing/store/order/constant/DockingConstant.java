package com.ruijing.store.order.constant;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.collections.SetBuilder;
import com.ruijing.order.enums.IBaseTemplateEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;

import java.util.List;
import java.util.Set;

/**
 * @description: 单号对接的常量配置
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/2/26 14:50
 **/
public class DockingConstant {

    /**
     * 单号对接的单位集合，广工，华师, 广州医, 广西肿瘤
     */
    public final static Set<String> THIRD_PARTY_PLATFORM_ORGANIZATION = SetBuilder.<String>custom()
            .add(OrgEnum.GUANG_XI_ZHONG_LIU.getCode())
            .build();

    /**
     * 单号对接的订单状态集合
     */
    public final static Set<Integer> THIRD_PART_PLATFORM_STATUS_COLLECT = SetBuilder.<Integer>custom()
            .add(DockingConstant.WAITING_FOR_RECEIVE)
            .add(DockingConstant.CLOSE)
            .add(DockingConstant.SUPP_REJECT)
            .add(DockingConstant.WAITING_FOR_DELIVERY)
            .add(DockingConstant.FINISH)
            .add(DockingConstant.WAITING_FOR_CONFIRM)
            .add(DockingConstant.WAITING_STATEMENT)
            .add(DockingConstant.PAID)
            .build();

    /**
     * 对接单位，订单号缓存命名空间
     */
    public final static String DOCKING_ORDER_NO_CACHE = "DOCKING_ORDER_NO_CACHE";

    /**
     * 对接单位，广工的编码
     */
    public final static String GUANG_DONG_GONG_YE_DA_XUE = "GUANG_DONG_GONG_YE_DA_XUE";

    /**
     * 对接单位，华南师范的编码
     */
    public final static String HUA_NAN_SHI_FAN_DA_XUE = "HUA_NAN_SHI_FAN_DA_XUE";

    /**
     * 对接单位，华南农业的编码
     */
    public final static String HUA_NAN_NONG_YE_DA_XUE = "HUA_NAN_NONG_YE_DA_XUE";

    /**
     * 对接单位，广州医科大的编码
     */
    public final static String GUANG_ZHOU_YI_KE_DA_XUE = "GUANG_ZHOU_YI_KE_DA_XUE";

    /**
     * 对接单位，广西肿瘤的编码
     */
    public final static String GUANG_XI_ZHONG_LIU = "GUANG_XI_ZHONG_LIU";

    /**
     * 订单关闭
     */
    public final static int CLOSE = 3;

    /**
     * 订单待供应商确认
     */
    public final static int WAITING_FOR_CONFIRM = 8;

    /**
     * 订单取消待供应商同意
     */
    public final static int PURCHASE_APPLY_TO_CANCEL = 9;

    /**
     * 订单确认收货待结算
     */
    public final static int WAITING_STATEMENT = 6;

    /**
     * 退货中
     */
    public final static int RETURNING = 33;

    /**
     * 已付款
     */
    public final static int PAID = 10;

    /**
     * 已付款
     */
    public final static int FINISH = 11;

    /**
     * 待收货/已发货
     */
    public final static int WAITING_FOR_RECEIVE = 5;

    /**
     * 待发货/已确认
     */
    public final static int WAITING_FOR_DELIVERY = 4;

    /**
     * 供应商拒绝退货
     */
    public final static int SUPP_REJECT = 32;

    /**
     * 对接失败的
     */
    public final static int FAILURE = -1;

    /**
     * 待对接管理平台审批
     */
    public final static int WAITING_APPROVAL = 0;

    /**
     * 操作人名字，系统操作
     */
    public final static String SYSTEM_OPERATOR_NAME = "系统";

    /**
     * 操作人名字，系统操作
     */
    public final static int SYSTEM_OPERATOR_ID = -1;

    /**
     * 订单采购人中心日志最大长度
     */
    public final static int ORDER_APPROVAL_LOG_REASON_MAX_LENGTH = 255;

    /**
     * 当请求单据与对接配置不匹配的时候返回的提示（含旧单）
     */
    public final static IBaseTemplateEnum CONFIG_MISMATCH_HINT = ExecptionMessageEnum.DOCUMENT_OLD_OR_FUNCTION_NOT_OPEN;

    /**
     * 中大附一冻结解冻都不要调用接口的单,审批编号：202311101456000227625
     */
    public final static List<Integer> NOT_FREEZE_ORDER_ID_LIST = New.list(1503704,
            1526735,
            1748475,
            2273885,
            2278056,
            2290514,
            2321846,
            2321844,
            2321843,
            2305818,
            2321828,
            2315256,
            2325826,
            2336985,
            2346173,
            2355882,
            2358734,
            2360567,
            2360564,
            2360562,
            2360561,
            2360559,
            2360558,
            2360557,
            2360556,
            2360555,
            2360550,
            2360549,
            2360548,
            2360545,
            2366238,
            2367912,
            2371167,
            2372428,
            2372429,
            2372444,
            2387769,
            2395316,
            2402359,
            2402358,
            2414841,
            2404512,
            2406427,
            2414840,
            2412639,
            2428630,
            2430151,
            2430152,
            2430154,
            2430150,
            2430919,
            2436178,
            2439632,
            2440268,
            2440879,
            2440971,
            2441096,
            2441246,
            2441415,
            2441433,
            2441492,
            2443956,
            2443960,
            2450956,
            2453074,
            2453841,
            2455279,
            2462447,
            2463873,
            2464937,
            2465504,
            2465503,
            2471397,
            2472012,
            2477300,
            2488466,
            2488471,
            2489867,
            2489874,
            2500102,
            2501761,
            2503404,
            2503904,
            2503903,
            2510866,
            2520506,
            2520946,
            2541994,
            2541995,
            2545740,
            2545656,
            2545741,
            2547500,
            2550288,
            2549588,
            2551456,
            2577989,
            2620946,
            653924,
            736332,
            1328381,
            1328382,
            1352942,
            1353074,
            1380216,
            1393557,
            1393559,
            1427031,
            1439804,
            1446674,
            1557354,
            1567580,
            1578427,
            1579361,
            1585806,
            1602877,
            1604282,
            1606606,
            1615587,
            1617152,
            1617387,
            2120872,
            2492184,
            2488039,
            2571355,
            2695450,
            2731268,
            2779178,
            2790766,
            2790770,
            2785354,
            2790803,
            2785372,
            2785353,
            2790804,
            2785369,
            2785352,
            2790415,
            2790762,
            2790414,
            2790437,
            2790413,
            2790802,
            2790412,
            2790764,
            2790411,
            2790410,
            2790435,
            2790409,
            2790759,
            2790408,
            2792878,
            2793776,
            2792876,
            2793775,
            2792875,
            2795773,
            2795739,
            2796004,
            2848875,
            2763698,
            2763697,
            2763696,
            2761729,
            2775662,
            2775621,
            2775615,
            2775614,
            2775761,
            2775762,
            2790771,
            2780788,
            2780789,
            2782934,
            2781936,
            2782135,
            2782136,
            2782137,
            2782329,
            2782330,
            2782332,
            2786301,
            2809402,
            2793541,
            2813063,
            2813056,
            2813058,
            2813061,
            2813055,
            2813053,
            2824352,
            2825079,
            2852168,
            2852169,
            2852170,
            2852171,
            2848874,
            2852172,
            2848873,
            2848921,
            2848872,
            2852173,
            2848871,
            2848870,
            2849071,
            2848869,
            2852154,
            2851623,
            2840385,
            2848868,
            2845942,
            2845943,
            2850035,
            2851197,
            2855126,
            2855125,
            2855124,
            2855123,
            2855121,
            2862241,
            2870102,
            2862550,
            2863638,
            2868848,
            2868847,
            2870992,
            2871976,
            2872584,
            2873801,
            2873799,
            2879405,
            2879458,
            2881792,
            2881793,
            2887496,
            2888628,
            2902453,
            2902735,
            2913392,
            2912210,
            2926213,
            2929618,
            2933653,
            2933661,
            2933666,
            2933732,
            2961232,
            2954502);

    /**
     * 自定义冻结时间的订单
     */
    public final static List<Integer> CUSTOM_FREEZE_TIME_ORDER_LIST = New.list(2255820,
            2255836,
            2256641,
            2262237,
            2263654,
            2266452,
            2281813,
            2283225,
            2283227,
            2301362,
            2303429,
            2304685,
            2313304,
            2314082,
            2315235,
            2321388,
            2321038,
            2324693,
            2358738,
            2359817,
            2386067,
            2402079,
            2404830,
            2426927,
            2428586,
            2428633,
            2434978,
            2440878,
            2441049,
            2444257);
}
