package com.ruijing.store.order.base.core.mapper;
import java.util.Date;

import com.ruijing.store.order.base.core.model.GoodsReturnLogDO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface GoodsReturnLogDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(GoodsReturnLogDO record);

    int insertSelective(GoodsReturnLogDO record);

    GoodsReturnLogDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(GoodsReturnLogDO record);

    int updateByPrimaryKey(GoodsReturnLogDO record);

    /**
     * 通过退货单id查询退货单操作日志
     * @param returnId
     * @return
     */
    List<GoodsReturnLogDO> findByReturnId(@Param("returnId")Integer returnId);

    /**
     * 批量插入退货操作日志
     * @param list
     * @return
     */
    int insertList(@Param("list")List<GoodsReturnLogDO> list);

    /**
     * 通过退货单id 批量 查找退货单操作日志
     * @param returnIdCollection    退货id集合
     * @param operationType         操作类型
     * @return                      退货单操作日志
     */
    List<GoodsReturnLogDO> findByReturnIdInAndOperationType(@Param("returnIdCollection")Collection<Integer> returnIdCollection,@Param("operationType")Integer operationType);

    /**
     * 通过退货操作类型查找退货单
     * @param operationCollection
     * @param startTime
     * @param endTime
     * @return
     */
    List<Integer> findReturnIdByOperationAndUpdateTime(@Param("operationCollection")Collection<Integer> operationCollection, @Param("startTime")Date startTime, @Param("endTime")Date endTime);

    /**
     * 根据退货单id查询退货操作日志
     * @param returnIdCollection
     * @return
     */
    List<GoodsReturnLogDO> findByReturnIdIn(@Param("returnIdCollection")Collection<Integer> returnIdCollection);

    /**
     * 通过操作类型查询退货id
     * @param operationType
     * @return
     */
    List<Integer> findIdByOperationType(@Param("operationType")Integer operationType);

}