package com.ruijing.store.order.base.docking.translator;

import com.ruijing.store.order.api.base.docking.dto.DockingExtraDTO;
import com.ruijing.store.order.base.docking.model.DockingExtra;

/**
 * @description: DockingExtra转换类
 * @author: zhuk
 * @create: 2019-09-25 11:12
 **/
public class DockingExtraTranslator {

    private DockingExtraTranslator(){
        throw new IllegalAccessError("不能使用构造方发创建对象！");
    }

    /**
     * dockingExtra 转为DTO对象
     * @param dockingExtra 入参
     * @return  DockingExtraDTO
     */
    public static DockingExtraDTO dockingExtraToDto(DockingExtra dockingExtra){

        DockingExtraDTO dockingExtraDTO = new DockingExtraDTO();
        dockingExtraDTO.setId(dockingExtra.getId());
        dockingExtraDTO.setCreationTime(dockingExtra.getCreationTime());
        dockingExtraDTO.setUpdateTime(dockingExtra.getUpdateTime());
        dockingExtraDTO.setType(dockingExtra.getType());
        dockingExtraDTO.setInfo(dockingExtra.getInfo());
        dockingExtraDTO.setExtraInfo(dockingExtra.getExtraInfo());
        dockingExtraDTO.setStatusextra(dockingExtra.getStatusextra());
        dockingExtraDTO.setMemo(dockingExtra.getMemo());
        return dockingExtraDTO;
    }

    /**
     * DTO  转为 dockingExtra对象
     * @param dockingExtraDTO 入参
     * @return  DockingExtraDTO
     */
    public static DockingExtra dtoToDockingExtra(DockingExtraDTO dockingExtraDTO){

        DockingExtra dockingExtra = new DockingExtra();
        dockingExtra.setId(dockingExtraDTO.getId());
        dockingExtra.setCreationTime(dockingExtraDTO.getCreationTime());
        dockingExtra.setUpdateTime(dockingExtraDTO.getUpdateTime());
        dockingExtra.setType(dockingExtraDTO.getType());
        dockingExtra.setInfo(dockingExtraDTO.getInfo());
        dockingExtra.setExtraInfo(dockingExtraDTO.getExtraInfo());
        dockingExtra.setStatusextra(dockingExtraDTO.getStatusextra());
        dockingExtra.setMemo(dockingExtraDTO.getMemo());
        return dockingExtra;
    }


}
