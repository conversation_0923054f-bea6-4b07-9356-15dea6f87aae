package com.ruijing.store.order.base.core.mapper;
import com.ruijing.store.order.api.base.enums.GoodsReturnInvalidEnum;
import com.ruijing.store.order.base.core.bo.goodsreturn.GoodsReturnPageRequestBO;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.GoodsReturnCountDO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;


public interface GoodsReturnMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(GoodsReturn record);

    int insertSelective(GoodsReturn record);

    GoodsReturn selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(GoodsReturn record);

    int updateByPrimaryKey(GoodsReturn record);

    /**
     * 获取时间范围内，创建，更新的 退货单
     * @param startTime
     * @param endTime
     * @return
     */
    List<GoodsReturn> findRangeTimeReturnOrder(@Param("startTime")Date startTime, @Param("endTime")Date endTime);

    /**
     * 根据订单详情id 查询退货单
     * @param detailId
     * @return
     */
    List<GoodsReturn> findByDetailId(@Param("detailId")Integer detailId);

    /**
     * 插入对象，批量
     * @param list
     * @return
     */
    int insertList(@Param("list")List<GoodsReturn> list);

    /**
     * 根据detailId 查询
     * @param detailIdCollection
     * @return
     */
    List<GoodsReturn> findByDetailIdIn(@Param("detailIdCollection") Collection<Integer> detailIdCollection);

    /**
     * 根据订单号模糊匹配退货单, 只头对象
     * @param likeReturnNo
     * @return
     */
    GoodsReturn selectOneByReturnNoLike(@Param("likeReturnNo")String likeReturnNo);

    /**
     * 根据detail id 更新退货单信息
     * @param updated
     * @param detailIdCollection
     * @return
     */
    int updateByDetailIdIn(@Param("updated")GoodsReturn updated,@Param("detailIdCollection")Collection<Integer> detailIdCollection);

    /**
     * 批量更新退货商品信息
     * @param list
     * @return
     */
    int loopUpdateByIdIn(@Param("list")List<GoodsReturn> list);

    /**
     * 通过id 批量查询退货单信息
     * @param idCollection
     * @return
     */
    List<GoodsReturn> findByIdIn(@Param("idCollection")Collection<Integer> idCollection, @Param("invalid") Integer isInvalid);

    default List<GoodsReturn> findByIdIn(@Param("idCollection") Collection<Integer> idCollection) {
        return this.findByIdIn(idCollection, GoodsReturnInvalidEnum.NORMAL.getCode());
    }

    /**
     * 退货单分页查询
     * @param param 分页查询条件
     * @return      分页结果
     */
    List<GoodsReturn> findByPageParams(@Param("params") GoodsReturnPageRequestBO param);

    /**
     * 根据退货状态统计退货单数量
     * @param param
     * @return
     */
    List<GoodsReturnCountDO> countGroupByGoodsReturnStatus(@Param("params") GoodsReturnPageRequestBO param, @Param("invalid") Integer isInvalid);

    default List<GoodsReturnCountDO> countGroupByGoodsReturnStatus(@Param("params") GoodsReturnPageRequestBO param) {
        return this.countGroupByGoodsReturnStatus(param, GoodsReturnInvalidEnum.NORMAL.getCode());
    }

    /**
     * 编辑退货单退货说明，原因，保存. 只用于编辑退货单更新
     * @param record
     * @return
     */
    int updateGoodsDetailRemarkById(GoodsReturn record);

    /**
     * 通过orderId查询退货单信息
     * @param orderId
     * @return
     */
    List<GoodsReturn> findByOrderId(@Param("orderId")Integer orderId, @Param("invalid") Integer isInvalid);

    default List<GoodsReturn> findByOrderId(@Param("orderId")Integer orderId) {
        return this.findByOrderId(orderId, GoodsReturnInvalidEnum.NORMAL.getCode());
    }

    /**
     * 通过orderId查询退货单信息
     * @param orderIds
     * @return
     */
    List<GoodsReturn> findByOrderIds(@Param("orderIds") List<Integer> orderIds, @Param("invalid") Integer isInvalid);

    default List<GoodsReturn> findByOrderIds(@Param("orderIds") List<Integer> orderIds) {
        return this.findByOrderIds(orderIds, GoodsReturnInvalidEnum.NORMAL.getCode());
    }

    /**
     * 批量取消退货商品信息
     * @param list
     * @return
     */
    int batchCancelGoodsReturn(@Param("list")List<GoodsReturn> list);

    /**
     * 根据订单id and 退货状态 查询退货单信息
     * @param orderIdCollection             订单id 集合
     * @param goodsReturnStatusCollection   退货状态集合
     * @return
     */
    List<GoodsReturn> findByOrderIdInAndGoodsReturnStatusIn(@Param("orderIdCollection")Collection<Integer> orderIdCollection, @Param("goodsReturnStatusCollection")Collection<Integer> goodsReturnStatusCollection, @Param("invalid") Integer isInvalid);

    default List<GoodsReturn> findByOrderIdInAndGoodsReturnStatusIn(@Param("orderIdCollection")Collection<Integer> orderIdCollection, @Param("goodsReturnStatusCollection")Collection<Integer> goodsReturnStatusCollection) {
        return this.findByOrderIdInAndGoodsReturnStatusIn(orderIdCollection, goodsReturnStatusCollection, GoodsReturnInvalidEnum.NORMAL.getCode());
    }

    /**
     * 根据订单id更新退货单退货状态
     * @param updatedGoodsReturnStatus
     * @param orderIdCollection
     * @return
     */
    int updateGoodsReturnStatusByOrderIdIn(@Param("updatedGoodsReturnStatus")Integer updatedGoodsReturnStatus,
                                           @Param("invalided") Integer invalided,
                                           @Param("orderIdCollection")Collection<Integer> orderIdCollection);

    /**
     * 获取退货单最大id
     * @return
     */
    int selectMaxId();

    /**
     * 根据id范围查找退货记录
     * @param minId 最小id
     * @param maxId 最大id
     * @return      退货记录
     */
    List<GoodsReturn> findByIdBetween(@Param("minId")Integer minId,@Param("maxId")Integer maxId);

    /**
     * 根据退货单号分组，查询包含多个detailId的退货单单号
     * @return 退货单号数组
     */
    List<String> findGroupByReturnNoHavingDetailId();

    /**
     * 根据单号数组批量查询退货单信息
     * @param returnNoCollection    退货单单号数组
     * @return                      退货单信息
     */
    List<GoodsReturn> findByReturnNoIn(@Param("returnNoCollection")Collection<String> returnNoCollection);

    /**
     * 根据detailId分组，查询包含多个ReturnNo的退货单DetailId
     * @return 退货单号数组
     */
    List<Integer> findGroupByDetailIdHavingReturnNo();

    /**
     * 根据订单号查询退货信息
     * @param orderNo   订单号
     * @return          退货单
     */
    List<GoodsReturn> findByOrderNo(@Param("orderNo")String orderNo);

    /**
     * 获取供应商同意退货后的退货单id
     * @param goodsReturnStatusList
     * @param invalid
     * @return
     */
    List<Integer> countIdByGoodsReturnStatus(@Param("goodsReturnStatusList")List<Integer> goodsReturnStatusList, @Param("invalid") Integer invalid);

    /**
     * 根据id列表和orgid列表查询退货单信息
     * @param idCollection
     * @param orgIdCollection
     * @return
     */
    List<GoodsReturn> findByIdInAndOrgIdIn(@Param("idCollection")Collection<Integer> idCollection
            ,@Param("orgIdCollection")Collection<Integer> orgIdCollection);

    /**
     * 通过订单id查询退货单id
     * @param orderIdCollection
     * @return
     */
    List<GoodsReturn> findIdByOrderIdIn(@Param("orderIdCollection")Collection<Integer> orderIdCollection);

    /**
     * 根据订单号查询最新的退货记录
     * @param orderNo
     * @return
     */
    GoodsReturn findByOrderNoLatest(@Param("orderNo") String orderNo);

    /**
     * 根据退货单状态和回复日期查询
     * @param status
     * @param date
     * @return
     */
    List<GoodsReturn> findByStatusAndReplyDate(@Param("statusCollection") List<Integer> status, @Param("date") Date date);

    List<GoodsReturn> findSyncReplyList();
}
