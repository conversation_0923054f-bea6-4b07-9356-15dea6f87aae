package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.store.baseconfig.api.dto.typeConfig.autoSettlement.AutoSettlementCycleConfig;
import com.ruijing.store.baseconfig.api.dto.typeConfig.autoSettlement.enums.AutoSettlementCycleTypeEnum;
import com.ruijing.store.baseconfig.api.dto.typeConfig.convert.TypeConfigConvert;
import com.ruijing.store.baseconfig.api.enums.ConfigCodeEnum;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.baseconfig.api.msg.ConfigQueryDTO;
import com.ruijing.store.baseconfig.api.rpc.SysConfigRpcService;
import com.ruijing.store.order.api.base.enums.OrgReceiptStoreConfigEnum;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.order.exception.CallRpcException;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 系统配置的rpc调用
 * @author: zhongyulei
 * @create: 2019/11/5 11:30
 **/
@ServiceClient
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
public class SysConfigClient {

    @MSharpReference(remoteAppkey = "store-baseconfig-service")
    private SysConfigRpcService sysConfigRpcService;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Resource
    private WmsRuleRpcServiceClient wmsRuleRpcServiceClient;

    /**
     * 通过配置编码获取医院的配置 org_id -> <org_code, BaseConfigDTO>
     * @param codes
     * @return
     */
    @ServiceLog(description = "通过配置编码获取医院的配置",serviceType = ServiceType.COMMON_SERVICE)
    public Map<String, Map<String, BaseConfigDTO>> findByCodes(List<String> codes) throws CallRpcException {
        Preconditions.notEmpty(codes,"配置编码不能为空");
        // 远程服务端限制最大不能超过十条，假如超过十条则分批处理，需要手动分批
        Map<String, Map<String, BaseConfigDTO>> retBaseConfigMap = new HashMap<>();
        List<List<String>> partition = ListUtils.partition(codes, 9);
        // 通过codes.subList()返回的子List没有实现序列化
        List<String> serializedList= New.listWithCapacity(9);
        partition.forEach(batchCodes -> {
            serializedList.addAll(batchCodes);
            RemoteResponse<Map<String, Map<String, BaseConfigDTO>>> remoteResponse = sysConfigRpcService.findByCodes(serializedList);
            Preconditions.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
            Map<String, Map<String, BaseConfigDTO>> responseBaseConfigMap = remoteResponse.getData();
            // key为orgId
            for (String key : responseBaseConfigMap.keySet()) {
                // 要返回的map --> <org_code, BaseConfigDTO>
                Map<String, BaseConfigDTO> retOrgCode2ConfigInfoMap = retBaseConfigMap.get(key);
                // rpc response的map  --> <org_code, BaseConfigDTO>
                Map<String, BaseConfigDTO> responseOrgCode2ConfigInfoMap = responseBaseConfigMap.get(key);
                // 如果之前就存在数据,往上迭代
                if(retOrgCode2ConfigInfoMap != null){
                    retOrgCode2ConfigInfoMap.putAll(responseOrgCode2ConfigInfoMap);
                }else{
                    // 不存在则新增
                    retBaseConfigMap.put(key, responseOrgCode2ConfigInfoMap);
                }
            }
            serializedList.clear();
        });
        return retBaseConfigMap;
    }

    /**
     * 根据配置code 查询配置信息map  key--》configCode  value ---》configValue 对应的值
     * @param orgCode
     * @param configCode
     * @return
     */
    public String getConfigByOrgCodeAndConfigCode(String orgCode, String configCode) throws CallRpcException{
        Map<String, String> configMap = getConfigMapByOrgCodeAndConfigCode(orgCode, New.list(configCode));
        if (configMap.isEmpty()) {
            return StringUtils.EMPTY;
        }
        return configMap.get(configCode);
     }

    /**
     * 根据结构id 和 配置code 查询配置信息map  key--》configCode  value ---》configValue 对应的值
     * @param orgCode
     * @param configCodeList
     * @return
     * @throws CallRpcException
     */
    public Map<String, String> getConfigMapByOrgCodeAndConfigCode(String orgCode, List<String> configCodeList) throws CallRpcException {
        ConfigQueryDTO configQueryDTO = new ConfigQueryDTO();
        configQueryDTO.setOrgCode(orgCode);
        List<BaseConfigDTO> receiptConfigList;
        receiptConfigList = this.getValueByOrgCodeAndConfigCode(orgCode,configCodeList);
        return receiptConfigList.stream().collect(Collectors.toMap(BaseConfigDTO::getConfigCode
                , BaseConfigDTO::getConfigValue, (oldValue, newValue) -> newValue));
    }

    /**
     * 根据org_code 和 config_code 查询系统配置, 查询异常则返回空集
     *
     * @param orgCode
     * @param configCodes
     * @return
     */
    @ServiceLog(description = "根据org_code 和 config_code 查询系统配置", serviceType = ServiceType.COMMON_SERVICE)
    public List<BaseConfigDTO> getValueByOrgCodeAndConfigCode(String orgCode, List<String> configCodes) throws CallRpcException {
        Preconditions.isTrue(StringUtils.isNotBlank(orgCode), "医院编号不能为空");
        Preconditions.isTrue(CollectionUtils.isNotEmpty(configCodes), "配置代码不能为空");

        ConfigQueryDTO param = new ConfigQueryDTO();
        param.setOrgCode(orgCode);
        param.setConfigCodes(configCodes);

        RemoteResponse<List<BaseConfigDTO>> remoteResponse = sysConfigRpcService.getValueByOrgCodeAndConfigCode(param);
        Preconditions.isTrue(remoteResponse.isSuccess(), "根据org_code 和 config_code 查询系统配置异常: " + JsonUtils.toJsonIgnoreNull(remoteResponse));
        List<BaseConfigDTO> responseData = remoteResponse.getData();
        return responseData;
    }


    /**
     * 根据org_id 和 config_code 查询系统配置, 查询异常则返回空集
     *
     * @param orgIdList
     * @param configCodes
     * @return
     */
    @ServiceLog(description = "根据org_id 和 config_code 查询系统配置", serviceType = ServiceType.COMMON_SERVICE)
    public List<BaseConfigDTO> getValueByOrgIdAndConfigCode(List<Integer> orgIdList, List<String> configCodes) throws CallRpcException {
        Preconditions.isTrue(CollectionUtils.isNotEmpty(orgIdList), "医院Id不能为空");
        Preconditions.isTrue(CollectionUtils.isNotEmpty(configCodes), "配置代码不能为空");

        ConfigQueryDTO param = new ConfigQueryDTO();
        param.setOrgIds(orgIdList);
        param.setConfigCodes(configCodes);

        RemoteResponse<List<BaseConfigDTO>> remoteResponse = sysConfigRpcService.getValueByOrgIdsAndConfigCodes(param);
        Preconditions.isTrue(remoteResponse.isSuccess(), "根据org_code 和 config_code 查询系统配置异常: " + JsonUtils.toJsonIgnoreNull(remoteResponse));
        List<BaseConfigDTO> responseData = remoteResponse.getData();
        return responseData;
    }



    public Map<String, String> getConfigMapByOrgIdAndConfigCode(List<Integer> orgIdList, List<String> configCodeList) throws CallRpcException {
        ConfigQueryDTO configQueryDTO = new ConfigQueryDTO();
        configQueryDTO.setOrgIds(orgIdList);
        List<BaseConfigDTO> receiptConfigList;
        receiptConfigList = this.getValueByOrgIdAndConfigCode(orgIdList,configCodeList);
        return receiptConfigList.stream().collect(Collectors.toMap(BaseConfigDTO::getConfigCode
                , BaseConfigDTO::getConfigValue, (oldValue, newValue) -> newValue));
    }
    /**
     * 根据orgCode判断单位是否采用自动发起结算
     * @param orgCode
     * @return
     */
    @ServiceLog(description = "根据orgCode判断单位是否采用自动发起结算", serviceType = ServiceType.RPC_CLIENT)
    public boolean isAutoStatement(String orgCode) {
        Map<String, String> autoStatementConfigMap = this.getConfigMapByOrgCodeAndConfigCode(orgCode, New.list(ConfigCodeEnum.STATEMENT_SUBMITTED_MANNER.name(), ConfigCodeEnum.AUTO_SETTLEMENT_CYCLE_JSON.name()));
        // 查询是否采用新结算，采用新结算要读取新结算的那套配置
        boolean isAutoStatement = ConfigConstant.STATEMENT_SUBMITTED_MANNER_VALUE.toString().equals(autoStatementConfigMap.get(ConfigConstant.STATEMENT_SUBMITTED_MANNER));
        String autoStatementCycleJson = autoStatementConfigMap.get(ConfigCodeEnum.AUTO_SETTLEMENT_CYCLE_JSON.name());
        if(isAutoStatement && StringUtils.isNotBlank(autoStatementCycleJson)){
            AutoSettlementCycleConfig autoSettlementCycleConfig = (AutoSettlementCycleConfig) TypeConfigConvert.convert(ConfigCodeEnum.AUTO_SETTLEMENT_CYCLE_JSON.name(), autoStatementCycleJson);
            if(!AutoSettlementCycleTypeEnum.UN_LIMIT.getValue().equals(autoSettlementCycleConfig.getType())){
                return false;
            }
        }

        return isAutoStatement;
    }

    /**
     * 根据orgCode判断单位是否自动入库
     * @param orgId
     * @param isUsedNewWareHouse    是否使用新库房
     * @param configMap             配置字典
     * @return
     */
    @ServiceLog(description = "根据orgCode判断单位是否自动入库", serviceType = ServiceType.RPC_CLIENT)
    public boolean isAutoStoreWareHouse(Integer orgId, Boolean isUsedNewWareHouse, Map<String, String> configMap) {
        // 查询是否采用新库房，采用新库房要读取新库房的那套配置
        if (isUsedNewWareHouse) {
            return wmsRuleRpcServiceClient.getNewWareHouseConfig(orgId);
        }
        // 旧库房的入库方式配置
        String oldWareHouseVersionValue = configMap.get(ConfigConstant.ORG_RECEIPT_STORE_CONFIG);
        Preconditions.notNull(oldWareHouseVersionValue, "获取库房版本配置失败, 配置ORG_RECEIPT_STORE_CONFIG为NUll");
        return OrgReceiptStoreConfigEnum.AUTO_STORE.getValue().toString().equals(oldWareHouseVersionValue);
    }
}
