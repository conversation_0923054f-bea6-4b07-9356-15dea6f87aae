package com.ruijing.store.order.gateway.print.dto.other;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: liwenyu
 * @createTime: 2023-04-07 10:54
 * @description:
 **/
public class OrderInvoiceDTO implements Serializable {

    private static final long serialVersionUID = 2245324824454260526L;

    /**
     *发票号
     */
    @RpcModelProperty("发票号")
    private String invoiceNo;

    /**
     * 发票code
     */
    @RpcModelProperty("发票code")
    private String invoiceCode;

    /**
     *发票金额
     */
    @RpcModelProperty("发票金额")
    private BigDecimal amount;

    /**
     *发票日期
     */
    @RpcModelProperty("发票日期")
    private Long issueDate;

    /**
     *发票备注
     */
    @RpcModelProperty("发票备注")
    private String remark;

    /**
     *开票人
     */
    @RpcModelProperty("开票人")
    private String drawer;

    /**
     *银行名字
     */
    @RpcModelProperty("银行名字")
    private String bankName;

    /**
     *银行编号
     */
    @RpcModelProperty("银行编号")
    private String bankNo;

    /**
     *发票图片链接
     */
    @RpcModelProperty("发票图片链接")
    private List<String> picturePathList;

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Long getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(Long issueDate) {
        this.issueDate = issueDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDrawer() {
        return drawer;
    }

    public void setDrawer(String drawer) {
        this.drawer = drawer;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public List<String> getPicturePathList() {
        return picturePathList;
    }

    public void setPicturePathList(List<String> picturePathList) {
        this.picturePathList = picturePathList;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderInvoiceDTO.class.getSimpleName() + "[", "]")
                .add("invoiceNo='" + invoiceNo + "'")
                .add("invoiceCode='" + invoiceCode + "'")
                .add("amount=" + amount)
                .add("issueDate=" + issueDate)
                .add("remark='" + remark + "'")
                .add("drawer='" + drawer + "'")
                .add("bankName='" + bankName + "'")
                .add("bankNo='" + bankNo + "'")
                .add("picturePathList=" + picturePathList)
                .toString();
    }
}
