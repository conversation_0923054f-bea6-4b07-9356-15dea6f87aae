package com.ruijing.store.order.rpc.client;

import com.reagent.research.sysu.order.api.dto.PlatformOperatorApprovalInfoDTO;
import com.reagent.research.sysu.order.api.service.PlatformOperatorApprovalRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;

import java.util.List;

/**
 * @author: Liwenyu
 * @create: 2024-12-27 16:24
 * @description:
 */
@ServiceClient
public class PlatformOperatorApprovalRpcClient {

    @MSharpReference(remoteAppkey = "research-sysu-order-service")
    private PlatformOperatorApprovalRpcService platformOperatorApprovalRpcService;

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "获取平台运营商核实数据")
    public List<PlatformOperatorApprovalInfoDTO> listPlatformOperatorApprovalInfoByOrderNos(List<String> orderNos){
        RemoteResponse<List<PlatformOperatorApprovalInfoDTO>> response = platformOperatorApprovalRpcService.listPlatformOperatorApprovalInfoByOrderNos(orderNos);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }
}
