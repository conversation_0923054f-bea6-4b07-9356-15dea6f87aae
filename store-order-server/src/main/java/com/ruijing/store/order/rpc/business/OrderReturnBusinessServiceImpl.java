package com.ruijing.store.order.rpc.business;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.dto.OrderDockingNumberDTO;
import com.reagent.order.dto.config.OrgDockingConfigDTO;
import com.reagent.order.enums.DockingNumberTypeEnum;
import com.reagent.order.enums.config.OmsDockingConfigValueEnum;
import com.reagent.order.enums.config.OrderDockingStrategyEnum;
import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.financial.docking.dto.order.OrderDetailDTO;
import com.reagent.research.financial.docking.dto.order.OrderReturnDTO;
import com.reagent.research.statement.api.enums.StatementStatusEnum;
import com.reagent.tpi.tpiclient.api.order.v2.OrderReturnBusinessService;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.request.GoodsReturnApplyDetailRequestDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnApplyRequestDTO;
import com.ruijing.store.goodsreturn.service.BuyerGoodsReturnService;
import com.ruijing.store.goodsreturn.vo.GoodsReturnApplyResponseVO;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.rpc.client.OrderOtherLogClient;
import com.ruijing.store.order.rpc.client.ResearchStatementClient;
import com.ruijing.store.order.rpc.client.ThirdPartRelatedOrderRPCClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 第三方对接平台退货实现
 * @author: zhongyulei
 * @create: 2021/3/2 16:50
 **/
@MSharpService
public class OrderReturnBusinessServiceImpl implements OrderReturnBusinessService {

    @Resource
    private BuyerGoodsReturnService buyerGoodsReturnService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderOtherLogClient orderOtherLogClient;

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @Resource
    private ResearchStatementClient researchStatementClient;

    @Resource
    private ThirdPartRelatedOrderRPCClient thirdPartRelatedOrderRPCClient;


    @Override
    @ServiceLog(description = "外部平台发起退货", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> orderReturn(OrgRequest<List<OrderReturnDTO>> request) {
        List<OrderReturnDTO> returnData = request.getData();
        if (CollectionUtils.isEmpty(returnData)) {
            return RemoteResponse.<Boolean>custom().setFailure("退货失败，退货入参为空！");
        }
        this.generateReturnRecordStrategy(returnData);
        // 记录财务日志
        for (OrderReturnDTO returnItem : returnData) {
            orderOtherLogClient.createOrderDockingLog(returnItem.getOrderNo(), request.getOrgCode(), null, JsonUtils.toJsonIgnoreNull(returnItem), "发起订单退货", null);
        }
        return RemoteResponse.<Boolean>custom().setSuccess().setData(true);
    }

    /**
     * 生成退货单策略
     * @param returnData    退货数据
     * @return
     */
    private boolean generateReturnRecordStrategy(List<OrderReturnDTO> returnData) {
        List<String> orderNoList = returnData.stream().map(OrderReturnDTO::getOrderNo).collect(Collectors.toList());
        List<OrderMasterDO> orderList = orderMasterMapper.findByFordernoIn(orderNoList);
        BusinessErrUtil.notEmpty(orderList, ExecptionMessageEnum.RETURN_FAILED_INVALID_ORDER_NUMBER, orderNoList);
        OrderMasterDO returnRelateInfo = orderList.get(0);
        OrgDockingConfigDTO config = dockingConfigCommonService.getConfig(returnRelateInfo.getFusercode());
        boolean isEnableAllReturn = false;
        if(dockingConfigCommonService.isNewDockingEnable(config, returnRelateInfo, null)){
            BusinessErrUtil.isTrue(OmsDockingConfigValueEnum.APPLY_RETURN_IN_EXTERNAL.name().equals(config.getOrderDockingConfigDTO().getOrderSyncApplyReturnGoods()), DockingConstant.CONFIG_MISMATCH_HINT);
            isEnableAllReturn = OmsDockingConfigValueEnum.WHOLE_ORDER.name().equals(config.getOrderDockingConfigDTO().getReturnGoodsRange());
        }else {
            BusinessErrUtil.isTrue(dockingConfigCommonService.getIfNeedDocking(returnRelateInfo, New.list(OrderDockingStrategyEnum.RETURN_PART_RETURN_AND_NOTICE, OrderDockingStrategyEnum.RETURN_ALL_RETURN_AND_NOTICE)), DockingConstant.CONFIG_MISMATCH_HINT);
            if(CollectionUtils.isNotEmpty(config.getStrategyEnumList())){
                isEnableAllReturn = config.getStrategyEnumList().contains(OrderDockingStrategyEnum.RETURN_ALL_RETURN_AND_NOTICE);
            }
        }
        Integer orgId = returnRelateInfo.getFuserid();
        if(orgId == OrgEnum.XIANG_GANG_KE_JI_DA_XUE_GUANG_ZHOU.getValue()){
            // 广东药科，结算中(待开票)也可以退货，自动撤销结算。后续考虑整理为配置项
            boolean checkOrderStatus = orderList.stream()
                    .anyMatch(o -> !OrderStatusEnum.WaitingForReceive.getValue().equals(o.getStatus())
                            && !OrderStatusEnum.WaitingForStatement_1.getValue().equals(o.getStatus())
                            && !(OrderStatusEnum.Statementing_1.getValue().equals(o.getStatus()) && StatementStatusEnum.WaitingToDrawBills.getStatus().equals(o.getStatementStatus())));
            BusinessErrUtil.isTrue(!checkOrderStatus, ExecptionMessageEnum.RETURN_FAILED_INVALID_STATUS_FOR_RETURN);
            List<Integer> statementIdList = orderList.stream()
                    .filter(orderMasterDO -> OrderStatusEnum.Statementing_1.getValue().equals(orderMasterDO.getStatus()))
                    .map(OrderMasterDO::getStatementId)
                    .collect(Collectors.toList());
            statementIdList.forEach(statementId-> researchStatementClient.cancelStatement(statementId, "采购人申请退货", null, null));
        }
        else if(orgId == OrgEnum.HUA_NAN_NONG_YE_DA_XUE.getValue()){
            // 华农允许待结算退货
            boolean checkOrderStatus = orderList.stream()
                    .anyMatch(o -> !OrderStatusEnum.WaitingForReceive.getValue().equals(o.getStatus())
                            && !OrderStatusEnum.WaitingForStatement_1.getValue().equals(o.getStatus()));
            BusinessErrUtil.isTrue(!checkOrderStatus, ExecptionMessageEnum.RETURN_FAILED_INVALID_ORDER_STATUS);
        }else{
            boolean checkOrderStatus = orderList.stream().anyMatch(o -> !OrderStatusEnum.WaitingForReceive.getValue().equals(o.getStatus()));
            BusinessErrUtil.isTrue(!checkOrderStatus, ExecptionMessageEnum.RETURN_FAILED_NON_PENDING_STATUS);
        }
        Map<String, OrderMasterDO> orderNoIdentityMap = DictionaryUtils.toMap(orderList, OrderMasterDO::getForderno, Function.identity());
        List<GoodsReturnApplyRequestDTO> applyRequestDTOList = this.parsingGoodsApplyParams(isEnableAllReturn, returnData, orderNoIdentityMap);
        // 封装RJSessionInfo
        RjSessionInfo rjSessionInfo = new RjSessionInfo();
        UserBaseInfoDTO userInfo = userClient.getUserInfo(returnRelateInfo.getFbuyerid(), orgId);
        rjSessionInfo.setGuid(userInfo.getGuid());
        rjSessionInfo.setOrgId(orgId);
        // 订单号-对方退货单映射
        for (GoodsReturnApplyRequestDTO applyRequestDTO : applyRequestDTOList) {
            // 申请退货
            GoodsReturnApplyResponseVO returnRes = buyerGoodsReturnService.applyGoodsReturn(applyRequestDTO, rjSessionInfo);
            applyRequestDTO.setReturnNo(returnRes.getReturnNo());
        }
        if(OrgEnum.XIANG_GANG_KE_JI_DA_XUE_GUANG_ZHOU.getValue() == orgId){
            // 港科大，需要存对方退货单号
            List<OrderDockingNumberDTO> dockingNumberDTOList = applyRequestDTOList.stream()
                    .filter(r->r.getOuterBuyerReturnNo() != null)
                    .map(r-> new OrderDockingNumberDTO()
                    .setDockingNumber(r.getOuterBuyerReturnNo())
                    .setOrderNo(r.getReturnNo())
                    .setNumberType(DockingNumberTypeEnum.OUTER_BUYER_RETURN_NO.getType())
                    .setOrgCode(returnRelateInfo.getFusercode())).collect(Collectors.toList());
            thirdPartRelatedOrderRPCClient.insertDockingNumber(dockingNumberDTOList);
        }
        return true;
    }

    /**
     * 解析第三方退货入参为退货入参
     * @param isEnableAllReturn 是否启用了整单退货
     * @param returnData
     * @return
     */
    private List<GoodsReturnApplyRequestDTO> parsingGoodsApplyParams(boolean isEnableAllReturn, List<OrderReturnDTO> returnData, Map<String, OrderMasterDO> orderNoIdentityMap) {
        List<GoodsReturnApplyRequestDTO> result = new ArrayList<>(returnData.size());
        List<Integer> orderIdList = orderNoIdentityMap.values().stream().map(OrderMasterDO::getId).collect(Collectors.toList());
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findAllByFmasteridIn(orderIdList);
        Map<String, List<OrderDetailDO>> orderNoDetailListMap = DictionaryUtils.groupBy(orderDetailDOList, OrderDetailDO::getFdetailno);

        for (OrderReturnDTO returnItem : returnData) {
            GoodsReturnApplyRequestDTO item = new GoodsReturnApplyRequestDTO();
            OrderMasterDO orderItem = orderNoIdentityMap.get(returnItem.getOrderNo());
            // 对方单号
            item.setOuterBuyerReturnNo(returnItem.getExtraOrderNo());
            item.setReturnStatus(GoodsReturnStatusEnum.WAITING_FOR_CONFIRM.getCode());
            item.setOrderNo(returnItem.getOrderNo());
            item.setReturnNo(returnItem.getReturnNo());
            item.setDepartmentId(orderItem.getFbuydepartmentid());
            item.setDepartmentName(orderItem.getFbuydepartment());
            item.setBuyerName(orderItem.getFbuyername());
            item.setSupplierId(orderItem.getFsuppid());
            item.setSupplierName(orderItem.getFsuppname());

            Map<String, OrderDetailDO> productIdIdentityMap = DictionaryUtils.toMap(orderNoDetailListMap.getOrDefault(returnItem.getOrderNo(), New.emptyList()), it -> it.getProductSn().toString(), Function.identity());
            List<GoodsReturnApplyDetailRequestDTO> returnApplyDetailRequestDTOS = this.parsingGoodsApplyDetailParams(isEnableAllReturn, returnItem, productIdIdentityMap);
            item.setReturnApplyDetailList(returnApplyDetailRequestDTOS);

            result.add(item);
        }
        return result;
    }

    /**
     * 解析第三方退货入参为退货入参
     * @param returnItem            退货单信息
     * @return                      退货详情数组
     */
    private List<GoodsReturnApplyDetailRequestDTO> parsingGoodsApplyDetailParams(boolean isEnableAllReturn, OrderReturnDTO returnItem, Map<String, OrderDetailDO> productIdIdentityMap) {
        String returnReason = returnItem.getReturnReason();
        List<GoodsReturnApplyDetailRequestDTO> result = new ArrayList<>();
        List<OrderDetailDTO> orderDetailDTOs = returnItem.getOrderDetailDTOs();
        if(CollectionUtils.isEmpty(orderDetailDTOs) && isEnableAllReturn){
            result = productIdIdentityMap.values().stream().map(orderDetailDO -> {
                GoodsReturnApplyDetailRequestDTO goodsReturnApplyDetailRequestDTO = this.orderDetailDO2ApplyReturnDTO(orderDetailDO, orderDetailDO.getFquantity());
                goodsReturnApplyDetailRequestDTO.setRemark(returnReason);
                return goodsReturnApplyDetailRequestDTO;
            }).collect(Collectors.toList());
        }else {
            for (OrderDetailDTO detailDTO : orderDetailDTOs) {
                if (detailDTO.getQuantity() == null || detailDTO.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                GoodsReturnApplyDetailRequestDTO it = new GoodsReturnApplyDetailRequestDTO();
                OrderDetailDO orderDetailDO = productIdIdentityMap.get(detailDTO.getGoodsId());
                if (orderDetailDO != null) {
                    GoodsReturnApplyDetailRequestDTO goodsReturnApplyDetailRequestDTO = this.orderDetailDO2ApplyReturnDTO(orderDetailDO, detailDTO.getQuantity());
                    goodsReturnApplyDetailRequestDTO.setRemark(returnReason);
                    result.add(goodsReturnApplyDetailRequestDTO);
                }
            }
        }
        return result;
    }

    private GoodsReturnApplyDetailRequestDTO orderDetailDO2ApplyReturnDTO(OrderDetailDO orderDetailDO, BigDecimal quantity){
        if(orderDetailDO == null){
            return null;
        }
        GoodsReturnApplyDetailRequestDTO it = new GoodsReturnApplyDetailRequestDTO();
        it.setGoodsName(orderDetailDO.getFgoodname());
        it.setGoodsCode(orderDetailDO.getFgoodcode());
        it.setSpecification(orderDetailDO.getFspec());
        it.setBrand(orderDetailDO.getFbrand());
        it.setPrice(orderDetailDO.getFbidprice());
        it.setQuantity(quantity);
        it.setAmount(quantity.multiply(orderDetailDO.getFbidprice()));
        // 目前对方退货原因使用固定文案
        it.setReturnReason("其他");
        it.setGoodsPicturePath(orderDetailDO.getFpicpath());
        it.setDangerousTag(orderDetailDO.getDangerousTypeName());
        it.setDetailId(orderDetailDO.getId());
        it.setProductId(orderDetailDO.getProductSn().toString());
        it.setUnit(orderDetailDO.getFunit());
        return it;
    }
}
