package com.ruijing.store.order.gateway.buyercenter.controller;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.remoting.msharp.annotation.GateWayController;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterTimeOutDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.TimeOutOrderParamsDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutQueryService;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.gateway.buyercenter.request.timeout.TimeOutNoticeRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.timeout.TimeOutRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.vo.OverTimeSettingVO;
import com.ruijing.store.order.gateway.buyercenter.vo.TimeOutTipsVO;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.enums.department.DepartmentTypeEnum;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

import static java.util.stream.Collectors.toList;

/**
 * @author: liwenyu
 * @createTime: 2023-11-30 15:16
 * @description:
 **/
@GateWayController(requestMapping = "/timeout")
@RpcApi("超时相关网关接口")
public class TimeOutController {

    @Resource
    private TimeoutQueryService timeoutQueryService;

    @Resource
    private UserClient userClient;

    @RpcMapping("/checkDeptTimeoutStat")
    @RpcMethod("校验部门超时状况")
    public RemoteResponse<TimeOutTipsVO> checkDeptTimeoutStat(RjSessionInfo rjSessionInfo, TimeOutRequestDTO timeOutRequestDTO){
        BusinessErrUtil.notNull(timeOutRequestDTO.getDeptId(), "需要传入要校验的部门");
        List<TimeOutTipsVO> timeOutTips = timeoutQueryService.checkDeptTimeoutStat(rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), timeOutRequestDTO.getDeptId());
        return CollectionUtils.isNotEmpty(timeOutTips) ? RemoteResponse.success(timeOutTips.get(0)) : RemoteResponse.success(null);
    }

    @RpcMapping("/checkPersonalTimeoutStat")
    @RpcMethod("校验个人超时状况")
    public RemoteResponse<TimeOutTipsVO> checkPersonalTimeoutStat(RjSessionInfo rjSessionInfo){
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(), false, null);
        List<TimeOutTipsVO> timeOutTips = timeoutQueryService.checkPersonalTimeoutStat(rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), loginInfo.getUserId());
        return CollectionUtils.isNotEmpty(timeOutTips) ? RemoteResponse.success(timeOutTips.get(0)) : RemoteResponse.success(null);
    }


    @RpcMapping("/checkAcceptance")
    @RpcMethod("校验验收超时状况")
    public RemoteResponse<List<TimeOutTipsVO>> checkUserHasAcceptanceFreeze(RjSessionInfo rjSessionInfo){
        if(rjSessionInfo.getUserType() != RjUserTypeEnum.STORE_USER){
            return RemoteResponse.success(New.emptyList());
        }
        return RemoteResponse.success(timeoutQueryService.checkOrderAcceptanceStat(rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER).intValue()));
    }

    @RpcMapping("/checkBalance")
    @RpcMethod("校验结算超时状况")
    public RemoteResponse<List<TimeOutTipsVO>> checkUserHasBalanceFreeze(RjSessionInfo rjSessionInfo){
        if(rjSessionInfo.getUserType() != RjUserTypeEnum.STORE_USER){
            return RemoteResponse.success(New.emptyList());
        }
        return RemoteResponse.success(timeoutQueryService.checkOrderBalanceStat(rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER).intValue()));
    }

    @RpcMapping("/findTimeOutOrders")
    @RpcMethod("查询验收超时订单")
    public PageableResponse<List<OrderMasterTimeOutDTO>> findTimeOutOrders(RjSessionInfo rjSessionInfo, TimeOutOrderParamsDTO timeOutOrderParamsDTO){
        BusinessErrUtil.isTrue(rjSessionInfo.getUserType() == RjUserTypeEnum.STORE_USER, "当前用户非采购用户！");

        List<DepartmentDTO> departmentDTOList = userClient.getDepartmentsForUser(rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER).intValue(), rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER));
        if(CollectionUtils.isEmpty(departmentDTOList)){
            return PageableResponse.<List<OrderMasterTimeOutDTO>>custom().setData(New.emptyList()).setPageSize(timeOutOrderParamsDTO.getPageSize()).setPageNo(timeOutOrderParamsDTO.getPageNo()).setTotal(0).setSuccess();
        }
        List<Integer> deptIds;
        if(departmentDTOList.stream().anyMatch(item->item.getDepartmentType() == DepartmentTypeEnum.ADMINISTRATING.getValue())){
            deptIds = null;
        }else {
            deptIds = departmentDTOList.stream().map(DepartmentDTO::getId).collect(toList());
        }
        timeOutOrderParamsDTO.setUserId(rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER));
        timeOutOrderParamsDTO.setDepartmentIds(deptIds);
        timeOutOrderParamsDTO.setEndDate(timeOutOrderParamsDTO.getEndDate() != null ? timeOutOrderParamsDTO.getEndDate() : new Date());
        BasePageResponseDTO<OrderMasterTimeOutDTO> res = timeoutQueryService.findTimeOutOrders(timeOutOrderParamsDTO);
        return PageableResponse.<List<OrderMasterTimeOutDTO>>custom().setData(res.getData()).setPageNo(res.getPageNo()).setTotal(res.getTotal()).setPageSize(res.getPageSize()).setSuccess();
    }

    @RpcMapping("/noticeUserByEmail")
    public RemoteResponse<Boolean> noticeUserByEmail(RjSessionInfo rjSessionInfo, TimeOutNoticeRequestDTO timeOutNoticeRequestDTO){
        BusinessErrUtil.isTrue(rjSessionInfo.getUserType() == RjUserTypeEnum.STORE_USER, "当前用户非采购用户！");
        BusinessErrUtil.notEmpty(timeOutNoticeRequestDTO.getDtos(), "请选择需要提醒的订单");
        BusinessErrUtil.notNull(timeOutNoticeRequestDTO.getBatchType(), "提醒类型不能为空");
        timeOutNoticeRequestDTO.setOrgId(rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER));
        timeoutQueryService.noticeUserByEmail(timeOutNoticeRequestDTO);
        return RemoteResponse.success().setMsg("成功向"+timeOutNoticeRequestDTO.getDtos().size()+"张订单发送提醒");
    }


    /**
     * 获取医院更改配置前的统计配置
     *
     * @param rjSessionInfo 用户信息
     * @return
     */
    @RpcMapping("/getOldSetting")
    public RemoteResponse<OverTimeSettingVO> findOrgOldSetting(RjSessionInfo rjSessionInfo) {
        BusinessErrUtil.isTrue(rjSessionInfo.getUserType() == RjUserTypeEnum.STORE_USER, "当前用户非采购用户！");
        Integer orgId = rjSessionInfo.getOrgId();
        OverTimeSettingVO overTimeSettingVO = timeoutQueryService.getOldOverTimeSetting(orgId);
        return RemoteResponse.success(overTimeSettingVO);
    }
}
