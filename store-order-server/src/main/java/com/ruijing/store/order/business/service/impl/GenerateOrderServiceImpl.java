package com.ruijing.store.order.business.service.impl;

import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.RefOrderFundCardDTO;
import com.reagent.order.dto.OrderDockingNumberDTO;
import com.reagent.order.dto.config.OrgDockingConfigDTO;
import com.reagent.order.dto.extra.OrderExtraBigDataDTO;
import com.reagent.order.enums.DockingDataTypeEnum;
import com.reagent.order.enums.DockingNumberTypeEnum;
import com.reagent.order.enums.config.OmsDockingConfigValueEnum;
import com.reagent.order.enums.config.OrderDockingStrategyEnum;
import com.reagent.research.fundcard.api.budget.dto.BindDTO;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.enums.BusinessTypeEnum;
import com.reagent.research.fundcard.enums.SourceTypeEnum;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.shop.category.api.enums.InboundTypeEnum;
import com.ruijing.shop.category.api.enums.ReimbursementExpenseTypeEnum;
import com.ruijing.shop.goods.api.dto.BaseProductDTO;
import com.ruijing.shop.goods.api.dto.BaseProductExtDTO;
import com.ruijing.store.approval.api.dto.ApprovalTaskDTO;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderDetailExtraEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.other.dto.GenerateOrderDTO;
import com.ruijing.store.order.api.base.other.dto.GenerateOrderDetailDTO;
import com.ruijing.store.order.api.base.other.dto.GenerateOrderExtraDTO;
import com.ruijing.store.order.api.base.other.dto.GenerateOrderResultDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.mapper.RefFundcardOrderMapper;
import com.ruijing.store.order.base.core.mapper.RefOrderDetailTagDOMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.base.core.model.RefOrderDetailTagDO;
import com.ruijing.store.order.base.core.translator.OrderAddressTranslator;
import com.ruijing.store.order.base.core.translator.OrderDetailTranslator;
import com.ruijing.store.order.base.core.translator.OrderMasterTranslator;
import com.ruijing.store.order.base.core.translator.RefFundcardOrderTranslator;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.base.minor.mapper.DangerousTagDOMapper;
import com.ruijing.store.order.base.minor.mapper.OrderConfirmForTheRecordDOMapper;
import com.ruijing.store.order.base.minor.mapper.ProductDescriptionSnapshotMapper;
import com.ruijing.store.order.base.minor.model.DangerousTagDO;
import com.ruijing.store.order.base.minor.model.OrderConfirmForTheRecordDO;
import com.ruijing.store.order.base.minor.model.ProductDescriptionSnapshotDO;
import com.ruijing.store.order.base.minor.translator.OrderRelateTranslator;
import com.ruijing.store.order.business.enums.DangerousTagEnum;
import com.ruijing.store.order.business.enums.TagTypeEnum;
import com.ruijing.store.order.business.enums.myorderlist.OrderSpeciesEnum;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.handler.WeChatMessageHandler;
import com.ruijing.store.order.business.service.GenerateOrderService;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @author: Liwenyu
 * @create: 2025-04-14 11:09
 * @description:
 */
@Service
public class GenerateOrderServiceImpl implements GenerateOrderService {

    private final static String CAT_TYPE = GenerateOrderServiceImpl.class.getSimpleName();

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private OrderManageService orderManageService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderEmailHandler orderEmailHandler;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private OrderConfirmForTheRecordDOMapper orderConfirmForTheRecordDOMapper;

    @Resource
    private RefOrderDetailTagDOMapper refOrderDetailTagDOMapper;

    @Resource
    private DangerousTagDOMapper dangerousTagDOMapper;

    @Resource
    private ProductDescriptionSnapshotMapper productDescriptionSnapshotMapper;

    @Resource
    private ProductClient productClient;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Resource
    private OrderAddressRPCClient orderAddressRPCClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private OrderDetailExtraClient orderDetailExtraClient;

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @Resource
    private WeChatMessageHandler weChatMessageHandler;

    @Resource
    private OrderBankSnapshotClient orderBankSnapshotClient;

    @Resource
    private RefFundCardOrderClient refFundCardOrderClient;

    @Resource
    private OrderDockingNumberRpcClient orderDockingNumberRpcClient;

    @Resource
    private SysuClient sysuClient;

    @Resource
    private FetchOrderDockingDataServiceClient fetchOrderDockingDataServiceClient;

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    @Transactional(rollbackFor = Exception.class)
    public List<GenerateOrderResultDTO> generateNewOrder(List<GenerateOrderDTO> generateOrderDTOList) {
        Preconditions.notEmpty(generateOrderDTOList, "生成订单失败，入参为空！");
        Map<String, List<OrderDetailDO>> orderNoDetailMap = new HashMap<>(generateOrderDTOList.size());
        List<OrderMasterDO> orderMasterDOList = new ArrayList<>(generateOrderDTOList.size());
        List<GenerateOrderResultDTO> generateOrderResultDTOList = new ArrayList<>(generateOrderDTOList.size());
        List<OrderAddressDTO> orderAddressDTOList = new ArrayList<>(generateOrderDTOList.size());

        // flow id 采购
        Map<Integer, Integer> appIdFlowIdMap = this.getApplyFlowIdMap(generateOrderDTOList);

        for (GenerateOrderDTO generateOrderDTO : generateOrderDTOList) {
            GenerateOrderResultDTO generateOrderResultDTO = new GenerateOrderResultDTO();
            //插入订单主表
            Preconditions.notNull(generateOrderDTO.getOrderMasterDTO(), "订单信息不能为空");
            Preconditions.notNull(generateOrderDTO.getGenerateOrderAddressDTO(), "生成订单失败, 地址信息不可空");
            OrderMasterDO orderMasterDO = OrderMasterTranslator.dtoToOrderMasterDO(generateOrderDTO.getOrderMasterDTO());

            // 增加审批流
            Integer flowId = -1;
            if (OrderTypeEnum.PURCHASE_ORDER.getCode().equals(orderMasterDO.getOrderType())
                    || OrderTypeEnum.CLINICAL_PURCHASE_ORDER.getCode().equals(orderMasterDO.getOrderType())) {
                // 采购审批流
                flowId = appIdFlowIdMap.get(orderMasterDO.getFtbuyappid());
            }
            orderMasterDO.setFlowId(flowId);

            if (generateOrderDTO.getGenerateOrderDetailDTOList() == null) {
                // 上游尚未兼容时的临时性方案，转换旧字段做兼容。字段转移完毕后将移除改代码
                generateOrderDTO.setGenerateOrderDetailDTOList(generateOrderDTO.getOrderDetailDTOS().stream().map(OrderDetailTranslator::orderDetailDTO2GenerateDTO).collect(toList()));
            }
            // 插入订单详情表
            Preconditions.notEmpty(generateOrderDTO.getGenerateOrderDetailDTOList(), "订单详情信息不能为空");
            // 建立入参对象与写入数据库对象映射，用于后续通过该映射关系回写订单详情id到入参对象中
            Map<GenerateOrderDetailDTO, OrderDetailDO> orderDetailMap = New.map();
            List<OrderDetailDO> orderDetailDOList = generateOrderDTO.getGenerateOrderDetailDTOList().stream().map(orderDetailDTO -> {
                OrderDetailDO dbItem = OrderDetailTranslator.generateDtoToOrderDetailDO(orderDetailDTO);
                orderDetailMap.put(orderDetailDTO, dbItem);
                return dbItem;
            }).collect(toList());

            this.initStatusStrategy(orderMasterDO, orderDetailDOList);
            orderMasterMapper.insertSelective(orderMasterDO);
            orderMasterDOList.add(orderMasterDO);
            generateOrderDTO.getOrderMasterDTO().setId(orderMasterDO.getId());

            generateOrderResultDTO.setOrderMasterId(orderMasterDO.getId());
            generateOrderResultDTO.setOrderMasterNumber(orderMasterDO.getForderno());
            generateOrderResultDTO.setOrderAddressDTO(generateOrderDTO.getGenerateOrderAddressDTO());
            generateOrderResultDTOList.add(generateOrderResultDTO);

            orderDetailDOList.forEach(orderDetailDO -> orderDetailDO.setFmasterid(orderMasterDO.getId()));
            orderDetailMapper.insertList(orderDetailDOList);
            // 根据映射关系回写入参对象
            for (int i = 0; i < orderDetailDOList.size(); i++) {
                generateOrderDTO.getGenerateOrderDetailDTOList().get(i).setId(orderDetailDOList.get(i).getId());
            }
            orderNoDetailMap.put(orderMasterDO.getForderno(), orderDetailDOList);

            // 保存商品快照策略
            this.saveProductDescriptionStrategy(orderDetailDOList, orderMasterDO);

            //插入订单确认备案信息
            if (generateOrderDTO.getOrderConfirmForTheRecordDTO() != null) {
                OrderConfirmForTheRecordDO orderConfirmForTheRecordDO = OrderRelateTranslator.orderConfirmForTheRecordDTO2DO(generateOrderDTO.getOrderConfirmForTheRecordDTO());
                orderConfirmForTheRecordDO.setId(UUID.randomUUID().toString());
                orderConfirmForTheRecordDO.setOrderId(orderMasterDO.getId());
                orderConfirmForTheRecordDOMapper.insertSelective(orderConfirmForTheRecordDO);
            }

            // 生猪采购单不记到危化品标签表
            if (!OrderTypeEnum.PIG_PURCHASE_ORDER.getCode().equals(generateOrderDTO.getOrderMasterDTO().getOrderType())) {
                List<RefOrderDetailTagDO> refOrderDetailTagDOList = new ArrayList<>(orderDetailDOList.size());
                List<DangerousTagDO> dangerousTagDOList = new ArrayList<>(orderDetailDOList.size());
                for (OrderDetailDO orderDetailDO : orderDetailDOList) {
                    //创建RefOrderDetailTagDO并加入集合
                    addRefOrderDetailTagDOList(refOrderDetailTagDOList, orderDetailDO);
                    //创建DangerousTagDO并加入集合
                    addDangerousTagList(dangerousTagDOList, orderDetailDO);
                }
                //插入订单商品详情关联对危化品标签
                if (CollectionUtils.isNotEmpty(refOrderDetailTagDOList)) {
                    refOrderDetailTagDOMapper.insertList(refOrderDetailTagDOList);
                }

                //插入订单商品详情关联对危化品标签（另一种记录，通过采购单商品详情复制过来的）
                if (CollectionUtils.isNotEmpty(dangerousTagDOList)) {
                    dangerousTagDOMapper.insertList(dangerousTagDOList);
                }
            }

            // 广州医的推送依赖这个地址表先插入成功, 先发版修复, 后续立马优化这点
            try {
                OrderAddressDTO orderAddressDTO = OrderAddressTranslator.generateDTOToDTO(generateOrderResultDTO);
                orderAddressDTOList.add(orderAddressDTO);
                orderAddressRPCClient.insertList(New.list(orderAddressDTO));
            } catch (Exception e) {
                // 发送报警邮件给开发人员
                orderEmailHandler.sendWarningToDeveloper("插入地址表失败", JsonUtils.toJsonIgnoreNull(generateOrderResultDTOList) + "\n" + e);
                throw new BusinessInterceptException(ExecptionMessageEnum.INSERT_ADDRESS_TABLE_FAILED, e);
            }

            // 保存商品详情级别的拓展数据
            this.saveOrderDetailExtraData(generateOrderDTO);
            // 保存订单级别的拓展数据
            this.saveOrderExtraData(generateOrderDTO);
            //订单绑定的经费卡信息
            if (CollectionUtils.isNotEmpty(generateOrderDTO.getRefFundcardOrderDTOS())) {
                List<RefFundcardOrderDO> refFundCardOrderDOList = generateOrderDTO.getRefFundcardOrderDTOS().stream().map(RefFundcardOrderTranslator::dto2DO).collect(toList());
                refFundCardOrderDOList.forEach(refFundCardOrderDO -> {
                    refFundCardOrderDO.setId(UUID.randomUUID().toString());
                    refFundCardOrderDO.setOrderId(orderMasterDO.getId().toString());
                });
                refFundcardOrderMapper.batchInsertSelective(refFundCardOrderDOList);
                this.bindOrderFundCard(orderMasterDO, refFundCardOrderDOList);
                // 插入galaxy的关联经费信息表
                List<RefOrderFundCardDTO> refOrderFundCardDTOList = generateOrderDTO.getRefFundcardOrderDTOS().stream().map(RefFundcardOrderTranslator::fromDTO).collect(toList());
                for (RefOrderFundCardDTO refOrderFundCardDTO : refOrderFundCardDTOList) {
                    refOrderFundCardDTO.setOrderId(orderMasterDO.getId());
                }
                refFundCardOrderClient.batchInsertSelective(refOrderFundCardDTOList);
            }

            if (generateOrderDTO.getOrderBankDataDTO() != null) {
                generateOrderDTO.getOrderBankDataDTO().setOrderNo(orderMasterDO.getForderno()).setOrderId(orderMasterDO.getId()).setOrgId(orderMasterDO.getFuserid());
                orderBankSnapshotClient.insertList(New.list(generateOrderDTO.getOrderBankDataDTO()));
            }

            if (generateOrderDTO.getOrderPayeeUserDTO() != null) {
                sysuClient.saveOrderOfflinePayeeUser(orderMasterDO, generateOrderDTO.getOrderPayeeUserDTO());
            }
            // 写入对接单号
            this.saveDockingNumber(generateOrderDTO);
        }

        // 推送订单到学校管理平台
        for (OrderMasterDO masterDO : orderMasterDOList) {
            // 如果开启了thunder开关, 将服务流量切换到thunder服务, 否则走老推送接口
            if (OrgEnum.GUANG_XI_ZHONG_LIU.getCode().equals(masterDO.getFusercode())) {
                // 只有广西肿瘤未迁移完毕，走这里
                List<OrderDetailDO> detailDOList = orderNoDetailMap.get(masterDO.getForderno());
                if (CollectionUtils.isEmpty(detailDOList)) {
                    continue;
                }
                orderManageService.pushOrderToThirdPlatform(masterDO, detailDOList);
            }
        }

        asyncSendEmail(orderMasterDOList, orderAddressDTOList);

        return generateOrderResultDTOList;
    }

    private void saveDockingNumber(GenerateOrderDTO generateOrderDTO) {
        List<OrderDockingNumberDTO> dockingNumberDTOList = New.list();
        if (StringUtils.isNotEmpty(generateOrderDTO.getDockingNumber())) {
            dockingNumberDTOList.add(new OrderDockingNumberDTO().setOrderNo(generateOrderDTO.getOrderMasterDTO().getForderno()).setNumberType(DockingNumberTypeEnum.ORDER.getType()).setDockingNumber(generateOrderDTO.getDockingNumber()).setOrgCode(generateOrderDTO.getOrderMasterDTO().getFusercode()));
        }
        if (StringUtils.isNotEmpty(generateOrderDTO.getSuppDockingNumber())) {
            dockingNumberDTOList.add(new OrderDockingNumberDTO().setOrderNo(generateOrderDTO.getOrderMasterDTO().getForderno()).setNumberType(DockingNumberTypeEnum.OUTER_SUPP.getType()).setDockingNumber(generateOrderDTO.getSuppDockingNumber()).setOrgCode(generateOrderDTO.getOrderMasterDTO().getFusercode()));
        }
        if (StringUtils.isNotEmpty(generateOrderDTO.getOuterBuyerDeliveryNumber())){
            dockingNumberDTOList.add(new OrderDockingNumberDTO().setOrderNo(generateOrderDTO.getOrderMasterDTO().getForderno()).setNumberType(DockingNumberTypeEnum.OUTER_BUYER_DELIVERY_NUMBER.getType()).setDockingNumber(generateOrderDTO.getOuterBuyerDeliveryNumber()).setOrgCode(generateOrderDTO.getOrderMasterDTO().getFusercode()));
        }
        for (GenerateOrderDetailDTO generateOrderDetailDTO : generateOrderDTO.getGenerateOrderDetailDTOList()) {
            if (generateOrderDetailDTO.getDockingNumber() != null) {
                dockingNumberDTOList.add(new OrderDockingNumberDTO()
                        .setOrderNo(generateOrderDetailDTO.getId().toString())
                        .setNumberType(DockingNumberTypeEnum.DETAIL_FINANCIAL.getType())
                        .setDockingNumber(generateOrderDetailDTO.getDockingNumber())
                        .setOrgCode(generateOrderDTO.getOrderMasterDTO().getFusercode()));
            }
        }
        if (CollectionUtils.isNotEmpty(dockingNumberDTOList)) {
            orderDockingNumberRpcClient.insertList(dockingNumberDTOList);
        }
    }

    /**
     * 写入订单级别的拓展数据
     *
     * @param generateOrderDTO 生成订单信息
     */
    private void saveOrderExtraData(GenerateOrderDTO generateOrderDTO) {
        // 订单额外信息处理，订单的广告额外信息需要去重，避免按广告id分组统计订单信息时，重复统计
        List<GenerateOrderExtraDTO> orderExtraDTOList = generateOrderDTO.getGenerateOrderExtraDTOList();
        Set<String> advertValueSet = New.set();
        if (CollectionUtils.isNotEmpty(orderExtraDTOList)) {
            for (GenerateOrderExtraDTO generateOrderExtraDTO : orderExtraDTOList) {
                if (OrderExtraEnum.ADVERTISEMENT_ID.equals(generateOrderExtraDTO.getField())) {
                    advertValueSet.add(generateOrderExtraDTO.getValue());
                }
            }
            // 移除掉里面的广告id项（拆单后生成子单可能会出现该状况）
            generateOrderDTO.getGenerateOrderExtraDTOList().removeIf(e -> OrderExtraEnum.ADVERTISEMENT_ID.equals(e.getField()));
        }

        for (GenerateOrderDetailDTO orderDetailDTO : generateOrderDTO.getGenerateOrderDetailDTOList()) {
            List<OrderDetailExtraDTO> orderDetailExtraDTOList = orderDetailDTO.getOrderDetailExtraDTOList();
            if (CollectionUtils.isNotEmpty(orderDetailExtraDTOList)) {
                orderDetailExtraDTOList = orderDetailExtraDTOList.stream().filter(orderDetailExtraDTO -> OrderDetailExtraEnum.ADVERTISEMENT_ID.getValue().equals(orderDetailExtraDTO.getExtraKey()) && advertValueSet.add(orderDetailExtraDTO.getExtraValue())).collect(toList());

                for (OrderDetailExtraDTO orderDetailExtraDTO : orderDetailExtraDTOList) {
                    if (OrderDetailExtraEnum.ADVERTISEMENT_ID.getType().equals(orderDetailExtraDTO.getExtraKeyType())) {
                        advertValueSet.add(orderDetailExtraDTO.getExtraValue());
                    }
                }
            }
        }
        // 去重后的广告id再写入
        for (String advertValue : advertValueSet) {
            GenerateOrderExtraDTO advertisementOrderExtraDTO = new GenerateOrderExtraDTO();
            advertisementOrderExtraDTO.setField(OrderExtraEnum.ADVERTISEMENT_ID);
            advertisementOrderExtraDTO.setValue(advertValue);
            generateOrderDTO.getGenerateOrderExtraDTOList().add(advertisementOrderExtraDTO);
        }

        if(generateOrderDTO.getFlowFundType() != null){
            GenerateOrderExtraDTO advertisementOrderExtraDTO = new GenerateOrderExtraDTO();
            advertisementOrderExtraDTO.setField(OrderExtraEnum.FLOW_FUND_TYPE);
            advertisementOrderExtraDTO.setValue(generateOrderDTO.getFlowFundType());
            generateOrderDTO.getGenerateOrderExtraDTOList().add(advertisementOrderExtraDTO);
        }

        // 插入额外字段属性
        if (CollectionUtils.isNotEmpty(generateOrderDTO.getGenerateOrderExtraDTOList())) {
            List<BaseOrderExtraDTO> baseOrderExtraDtoList = New.listWithCapacity(generateOrderDTO.getGenerateOrderExtraDTOList().size());
            for(GenerateOrderExtraDTO generateOrderExtraDTO : generateOrderDTO.getGenerateOrderExtraDTOList()){
                OrderExtraEnum orderExtraEnum = generateOrderExtraDTO.getField();
                BaseOrderExtraDTO output = new BaseOrderExtraDTO();
                output.setOrderId(generateOrderDTO.getOrderMasterDTO().getId());
                output.setOrderNo(generateOrderDTO.getOrderMasterDTO().getForderno());
                output.setOrgId(generateOrderDTO.getOrderMasterDTO().getFuserid());
                output.setExtraKey(orderExtraEnum.getValue());
                output.setExtraKeyDesc(orderExtraEnum.getDesc());
                output.setExtraValue(generateOrderExtraDTO.getValue());
                baseOrderExtraDtoList.add(output);
            }
            orderExtraClient.insertList(baseOrderExtraDtoList);
        }

        // 存放财务对接拓展数据
        if(StringUtils.isNotEmpty(generateOrderDTO.getFinancialDockingExtraData())){
            OrderExtraBigDataDTO financialDockingExtraData = new OrderExtraBigDataDTO();
            financialDockingExtraData.setOrderNo(generateOrderDTO.getOrderMasterDTO().getForderno());
            financialDockingExtraData.setDataType(DockingDataTypeEnum.FINANCIAL_DOCKING_EXTRA_DATA.getType());
            financialDockingExtraData.setDataContent(generateOrderDTO.getFinancialDockingExtraData());
            fetchOrderDockingDataServiceClient.saveOrderExtraBigData(financialDockingExtraData);
        }

        // 存放管理平台发货二维码
        if(StringUtils.isNotEmpty(generateOrderDTO.getOuterBuyerDeliveryQrCode())){
            OrderExtraBigDataDTO financialDockingExtraData = new OrderExtraBigDataDTO();
            financialDockingExtraData.setOrderNo(generateOrderDTO.getOrderMasterDTO().getForderno());
            financialDockingExtraData.setDataType(DockingDataTypeEnum.OUTER_BUYER_DELIVERY_QR_CODE.getType());
            financialDockingExtraData.setDataContent(generateOrderDTO.getOuterBuyerDeliveryQrCode());
            fetchOrderDockingDataServiceClient.saveOrderExtraBigData(financialDockingExtraData);
        }
    }

    /**
     * 写入订单商品级别的拓展信息
     *
     * @param generateOrderDTO 生成订单信息
     */
    private void saveOrderDetailExtraData(GenerateOrderDTO generateOrderDTO) {
        OrderMasterDTO generateOrderData = generateOrderDTO.getOrderMasterDTO();
        // 订单详情额外信息处理
        List<OrderDetailExtraDTO> needSaveDetailExtraList = New.list();
        for (GenerateOrderDetailDTO generateDetailDTO : generateOrderDTO.getGenerateOrderDetailDTOList()) {
            List<OrderDetailExtraDTO> orderDetailExtraDTOList = generateDetailDTO.getOrderDetailExtraDTOList();
            if (CollectionUtils.isNotEmpty(orderDetailExtraDTOList)) {
                for (OrderDetailExtraDTO orderDetailExtraDTO : orderDetailExtraDTOList) {
                    orderDetailExtraDTO.setOrderDetailId(generateDetailDTO.getId());
                    orderDetailExtraDTO.setOrderId(generateOrderData.getId());
                    orderDetailExtraDTO.setOrgId(generateOrderData.getFuserid());
                    OrderDetailExtraEnum orderDetailExtraEnum = OrderDetailExtraEnum.getByValue(orderDetailExtraDTO.getExtraKey());
                    if (orderDetailExtraEnum != null) {
                        orderDetailExtraDTO.setExtraKeyType(orderDetailExtraEnum.getType());
                    }
                    needSaveDetailExtraList.add(orderDetailExtraDTO);
                }
            }
        }
        orderDetailExtraClient.batchInsertOrderDetailExtra(needSaveDetailExtraList);

        //订单详情处理数据长度较长的列
        for (GenerateOrderDetailDTO orderDetailDTO : generateOrderDTO.getGenerateOrderDetailDTOList()) {
            String attribute = orderDetailDTO.getAttribute();
            Integer detailId = orderDetailDTO.getId();
            if (StringUtils.isEmpty(attribute)) {
                continue;
            }
            OrderExtraBigDataDTO orderExtraBigDataDTO = new OrderExtraBigDataDTO();
            orderExtraBigDataDTO.setOrderNo(String.valueOf(detailId));
            orderExtraBigDataDTO.setDataContent(attribute);
            orderExtraBigDataDTO.setDataType(DockingDataTypeEnum.MULTIPLE_SPECIFICATIONS_PRODUCTS.getType());
            fetchOrderDockingDataServiceClient.saveOrderExtraBigData(orderExtraBigDataDTO);
        }
    }

    /**
     * 获取采购单id-审批流id 对应map
     *
     * @param generateOrderDTOS
     * @return
     */
    private Map<Integer, Integer> getApplyFlowIdMap(List<GenerateOrderDTO> generateOrderDTOS) {
        List<Long> applyIdList = generateOrderDTOS.stream()
                .filter(s -> s.getOrderMasterDTO() != null)
                .filter(s -> s.getOrderMasterDTO().getFtbuyappid() != null)
                .map(s -> s.getOrderMasterDTO().getFtbuyappid().longValue()).collect(toList());
        Map<Integer, Integer> appIdFlowIdMap = new HashMap<>();
        if (CollectionUtils.isEmpty(applyIdList)) {
            return appIdFlowIdMap;
        }
        List<ApprovalTaskDTO> appInfoWithFlowId = applicationBaseClient.getAppInfoWithFlowId(applyIdList);
        for (ApprovalTaskDTO approvalTaskDTO : appInfoWithFlowId) {
            Integer appId = approvalTaskDTO.getDockId() == null ? null : approvalTaskDTO.getDockId().intValue();
            Integer flowId = approvalTaskDTO.getFlowId() == null ? null : approvalTaskDTO.getFlowId().intValue();
            appIdFlowIdMap.put(appId, flowId);
        }
        return appIdFlowIdMap;
    }

    private void asyncSendEmail(List<OrderMasterDO> orderMasterDOList, List<OrderAddressDTO> orderAddressDTOList) {
        //发送生成订单邮件邮件给供应商
        AsyncExecutor.listenableRunAsync(() -> {
            Map<Integer, OrderAddressDTO> idAddressMap = DictionaryUtils.toMap(orderAddressDTOList, OrderAddressDTO::getId, Function.identity());
            for (OrderMasterDO masterDO : orderMasterDOList) {
                //待供应商确认状态，发送生成订单邮件、短信、微信消息给供应商
                if (OrderStatusEnum.WaitingForConfirm.getValue().equals(masterDO.getStatus()) || OrgConst.GUANG_DONG_YI_KE_DA_XUE.equals(masterDO.getFusercode())) {
                    OrderAddressDTO orderAddressDTO = idAddressMap.get(masterDO.getId());
                    orderEmailHandler.sendOrderGenerateEmailToSupp(masterDO, orderAddressDTO);
                    weChatMessageHandler.waitingConfirmToSupp(masterDO, orderAddressDTO);
                }
            }
        }).addFailureCallback(throwable -> {
            logger.error("发送生成订单邮件邮件给供应商失败：" + throwable);
            Cat.logError(CAT_TYPE, "orderEmailHandler", "发送生成订单邮件邮件给供应商失败：", throwable);
            throw new IllegalStateException(throwable);
        });
    }

    /**
     * 保存商品快照策略
     *
     * @param orderDetailDOList 订单明细
     * @param orderMasterDO     订单信息
     */
    private void saveProductDescriptionStrategy(List<OrderDetailDO> orderDetailDOList, OrderMasterDO orderMasterDO) {
        Integer orderType = orderMasterDO.getOrderType();
        //如果是竞价生成的订单则保存商品描述快照信息
        if (OrderTypeEnum.BID_ORDER.getCode().equals(orderType)) {
            //竞价业务类型
            final Integer bidBusinessType = 2;
            Integer suppId = orderMasterDO.getFsuppid();
            List<Long> productIdList = orderDetailDOList.stream().map(OrderDetailDO::getProductSn).collect(Collectors.toList());
            List<BaseProductDTO> productList = productClient.findByIdListWithDeleted(productIdList, suppId);
            BusinessErrUtil.notEmpty(productList, ExecptionMessageEnum.UNABLE_TO_FIND_PRODUCT_INFO);
            Map<Long, List<BaseProductDTO>> productMapById = productList.stream().collect(Collectors.groupingBy(BaseProductDTO::getId));

            //商品描述快照信息
            List<ProductDescriptionSnapshotDO> descriptionSnapshotDOList = New.list();
            for (OrderDetailDO orderDetailDO : orderDetailDOList) {
                Long productSn = orderDetailDO.getProductSn();
                BaseProductDTO baseProductDTO = productMapById.get(productSn).get(0);
                BaseProductExtDTO productExt = baseProductDTO.getProductExt();
                String description = "";
                if (productExt != null) {
                    description = productExt.getDescription();
                }
                ProductDescriptionSnapshotDO productDescriptionSnapshotDO = new ProductDescriptionSnapshotDO();
                productDescriptionSnapshotDO.setBusinessId(orderDetailDO.getId().toString());
                productDescriptionSnapshotDO.setBusinessType(bidBusinessType);
                productDescriptionSnapshotDO.setProductId(orderDetailDO.getProductSn());
                productDescriptionSnapshotDO.setDescription(description);
                descriptionSnapshotDOList.add(productDescriptionSnapshotDO);
            }
            if (CollectionUtils.isNotEmpty(descriptionSnapshotDOList)) {
                productDescriptionSnapshotMapper.insertList(descriptionSnapshotDOList);
            }
        }
    }

    /**
     * 生成订单的初始策略
     *
     * @param orderMasterDO
     * @return
     */
    private void initStatusStrategy(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList) {
        String orgCode = orderMasterDO.getFusercode();
        if (OrgEnum.HAI_JUN_TE_SE_YI_XUE_ZHONG_XIN.getCode().equals(orgCode) && OrderSpeciesEnum.OFFLINE.getValue() == orderMasterDO.getSpecies().intValue()) {
            orderMasterDO.setStatus(OrderStatusEnum.Finish.getValue());
            return;
        }

        if (OrderSpeciesEnum.OFFLINE.getValue() == orderMasterDO.getSpecies().intValue()) {
            return;
        }
        // 广西肿瘤迁移后,删除此处代码
        if (OrgEnum.GUANG_XI_ZHONG_LIU.getCode().equals(orgCode)) {
            orderMasterDO.setStatus(OrderStatusEnum.WaitingForDockingConfirm.getValue());
            return;
        }

        OrgDockingConfigDTO config = dockingConfigCommonService.getConfig(orgCode);
        boolean pushOrder = false;
        if (dockingConfigCommonService.isNewDockingEnable(config, orderMasterDO, orderDetailDOList)) {
            boolean bid = OrderTypeEnum.BID_ORDER.getCode().equals(orderMasterDO.getOrderType()) && OmsDockingConfigValueEnum.PUSH_ORDER.name().equals(config.getBidDockingConfigDTO().getBidPushOrderData());
            boolean app = OrderTypeEnum.PURCHASE_ORDER.getCode().equals(orderMasterDO.getOrderType()) && OmsDockingConfigValueEnum.PUSH_ORDER.name().equals(config.getAppDockingConfigDTO().getAppPushOrderData());
            pushOrder = bid || app;
        } else {
            pushOrder = dockingConfigCommonService.getIfNeedDocking(orderMasterDO, New.list(OrderDockingStrategyEnum.PUSH_ORDER_AND_HANDLE_REQUEST, OrderDockingStrategyEnum.APP_PUSH_ORDER, OrderDockingStrategyEnum.BID_PUSH_ORDER));
        }
        if (pushOrder) {
            orderMasterDO.setStatus(OrderStatusEnum.WaitingForDockingConfirm.getValue());
        }
    }

    /**
     * 订单绑定预算系统
     *
     * @param orderMasterDO
     * @param refFundCardOrderDOList
     */
    public void bindOrderFundCard(OrderMasterDO orderMasterDO, List<RefFundcardOrderDO> refFundCardOrderDOList) {
        List<String> fundCardsIdList = refFundCardOrderDOList.stream().map(RefFundcardOrderDO::getCardId).collect(toList());
        if (OrgEnum.SHANG_HAI_JIU_YUAN.getCode().equals(orderMasterDO.getFusercode())) {
            fundCardsIdList = refFundCardOrderDOList.stream().map(RefFundcardOrderDO::getSubjectId).collect(toList());
        }
        UserBaseInfoDTO userInfo = userClient.getUserInfo(orderMasterDO.getFbuyerid(), orderMasterDO.getFuserid());
        // 推送绑卡信息到 预算系统
        BindDTO bindItem = new BindDTO();
        bindItem.setSerialNumber(orderMasterDO.getForderno());
        bindItem.setAppKey(Environment.getAppKey());
        bindItem.setSourceType(SourceTypeEnum.ORDER.getValue());
        bindItem.setBusinessType(BusinessTypeEnum.BUY.getValue());
        bindItem.setUserId(userInfo.getId());
        bindItem.setOperatorJobNumber(userInfo.getJobnumber());
        bindItem.setOperatorName(userInfo.getName());
        bindItem.setOperateDate(new Date());

        List<FundCardDTO> fundCardDTOS = fundCardsIdList.stream()
                .map(cardId -> {
                    FundCardDTO fundCardDTO = new FundCardDTO();
                    fundCardDTO.setId(cardId);
                    return fundCardDTO;
                }).collect(Collectors.toList());

        bindItem.setFundCardDTOs(fundCardDTOS);
        List<BindDTO> bindDTOS = New.list(bindItem);
        // 绑卡记录推送到预算系统
        researchFundCardServiceClient.fundCardBindBatch(bindDTOS, orderMasterDO.getFusercode());
    }

    /**
     * 创建DangerousTagDO并插入集合
     *
     * @param dangerousTagDOList
     * @param orderDetailDO
     */
    private void addDangerousTagList(List<DangerousTagDO> dangerousTagDOList, OrderDetailDO orderDetailDO) {
        String casNo = orderDetailDO.getCasno();
        Integer dangerousTypeId = orderDetailDO.getDangerousTypeId();
        //无效值过滤
        int invalidValue = 0;
        if (dangerousTypeId != null && dangerousTypeId != invalidValue) {
            DangerousTagDO dangerousTagDO = new DangerousTagDO();
            dangerousTagDO.setRegulatoryType(orderDetailDO.getRegulatoryTypeId());
            dangerousTagDO.setDangerousType(orderDetailDO.getDangerousTypeId());
            dangerousTagDO.setCasNo(casNo == null ? "" : casNo);
            dangerousTagDO.setBusinessId(orderDetailDO.getId().toString());
            dangerousTagDO.setBusinessType(DangerousTagEnum.ORDER_DETAIL.getValue());
            dangerousTagDO.setCreationTime(new Date());
            dangerousTagDOList.add(dangerousTagDO);
        }
    }

    /**
     * 创建RefOrderDetailTagDO并插入集合
     *
     * @param refOrderDetailTagDOList
     * @param orderDetailDO
     */
    private void addRefOrderDetailTagDOList(List<RefOrderDetailTagDO> refOrderDetailTagDOList, OrderDetailDO orderDetailDO) {
        if (orderDetailDO.getFeeTypeTag() != null) {
            RefOrderDetailTagDO feeTypeTagDO = new RefOrderDetailTagDO();
            feeTypeTagDO.setRefId(orderDetailDO.getId().toString());
            feeTypeTagDO.setTagName(orderDetailDO.getFeeTypeTag());
            feeTypeTagDO.setTagType(TagTypeEnum.FEE_TYPE.getValue());
            String feeTypeTag = orderDetailDO.getFeeTypeTag();
            if (StringUtils.isNotBlank(feeTypeTag)) {
                ReimbursementExpenseTypeEnum expenseTypeEnum = ReimbursementExpenseTypeEnum.descOf(feeTypeTag);
                if (expenseTypeEnum != null) {
                    feeTypeTagDO.setTagValue(expenseTypeEnum.getValue());
                }
                refOrderDetailTagDOList.add(feeTypeTagDO);
            }
        }
        if (orderDetailDO.getCategoryTag() != null) {
            RefOrderDetailTagDO categoryTagDO = new RefOrderDetailTagDO();
            categoryTagDO.setRefId(orderDetailDO.getId().toString());
            categoryTagDO.setTagName(orderDetailDO.getCategoryTag());
            categoryTagDO.setTagType(TagTypeEnum.FIRST_TIER_CATEGORY.getValue());
            String categoryTag = orderDetailDO.getCategoryTag();
            if (StringUtils.isNotBlank(categoryTag)) {
                InboundTypeEnum inboundTypeEnum = InboundTypeEnum.descOf(categoryTag);
                if (inboundTypeEnum != null) {
                    categoryTagDO.setTagValue(inboundTypeEnum.getValue());
                }
            }
            refOrderDetailTagDOList.add(categoryTagDO);
        }
    }
}
