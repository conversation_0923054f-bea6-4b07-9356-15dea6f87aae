package com.ruijing.store.order.gateway.buyercenter.controller;

import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeStatisticsDTO;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.service.BarCodeGoodsReturnService;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.DetailBatchesDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailReq;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnDTO;
import com.ruijing.store.order.gateway.buyercenter.request.OrderBarCodeQryReqDTO;
import com.ruijing.store.order.gateway.buyercenter.request.OrderUniqueBarCodeRequest;
import com.ruijing.store.order.gateway.buyercenter.request.goodsreturn.BarCodeGoodsReturnRequest;
import com.ruijing.store.order.gateway.buyercenter.request.goodsreturn.GoodsReturnBarCodeRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.BarCodeReturnResultVO;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.OrderBatchesVO;
import com.ruijing.store.order.other.dto.UniqueBarCodeDTO;
import com.ruijing.store.order.other.translator.OrderUniqueBarCodeTranslator;
import com.ruijing.store.order.rpc.client.OrderUniqueBarCodeRPCClient;
import com.ruijing.store.order.service.OrderBatchService;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 中山三院(临床)定制的路由
 */
@MSharpService(isGateway = "true")
@RpcMapping("/orderUniqueBarCodeManage")
@RpcApi(value = "订单-一物一码网关")
public class LinChuangBuyerOrderGWController {

    @Resource
    private OrderUniqueBarCodeRPCClient orderUniqueBarCodeRPCClient;

    @Resource
    private BarCodeGoodsReturnService barCodeGoodsReturnService;

    @Resource
    private OrderBatchService orderBatchService;

    @RpcMethod("输出订单明细商品的批次, 返回当前商品的所有条形码信息，orderDetailIdList必填")
    @RpcMapping("/outputDetailBatches")
    public RemoteResponse<List<UniqueBarCodeDTO>> outputOrderDetailBatches(RjSessionInfo rjSessionInfo, OrderDetailReq request) {
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.QUERY_BATCH_FAILED_NO_PERMISSION);
        Preconditions.notNull(request, "入参为空!");
        List<OrderUniqueBarCodeDTO> queryResult = orderUniqueBarCodeRPCClient.outPutOrderBatchesInfo(request.getOrderDetailIdList());
        List<UniqueBarCodeDTO> res = queryResult.stream().map(OrderUniqueBarCodeTranslator::galaxyDto2orderDto).collect(Collectors.toList());
        return RemoteResponse.<List<UniqueBarCodeDTO>>custom().setSuccess().setData(res);
    }

    @RpcMethod("输出订单单个明细商品的批次(只返回一条批次信息, 列表气泡用)，orderDetailId")
    @RpcMapping("/outputSingleDetailBatches")
    public RemoteResponse<DetailBatchesDTO> outputSingleDetailBatches(RjSessionInfo rjSessionInfo, OrderBasicParamDTO request) {
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.QUERY_BATCH_FAILED_NO_PERMISSION);
        Preconditions.notNull(request.getOrderDetailId(), "入参为空!");
        DetailBatchesDTO queryResult = orderUniqueBarCodeRPCClient.outPutSingleBatchesInfo(request.getOrderDetailId());
        return RemoteResponse.<DetailBatchesDTO>custom().setSuccess().setData(queryResult);
    }

    @RpcMethod("录入条形码批次信息, orderUniqueBarCodeList必填")
    @RpcMapping("/inputOrderDetailBatches")
    public RemoteResponse<Boolean> inputOrderDetailBatches(RjSessionInfo rjSessionInfo, OrderUniqueBarCodeRequest request) {
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.QUERY_BATCH_FAILED_NO_PERMISSION);
        boolean isSuccess = orderUniqueBarCodeRPCClient.inputOrderBarCodeBatches(request.getOrderUniqueBarCodeList());
        return RemoteResponse.<Boolean>custom().setSuccess().setData(isSuccess);
    }

    @RpcMethod("修改条形码批次信息, orderUniqueBarCodeList必填")
    @RpcMapping("/modifyOrderDetailBatches")
    public RemoteResponse<Boolean> modifyOrderDetailBatches(RjSessionInfo rjSessionInfo, OrderUniqueBarCodeRequest request) {
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.QUERY_BATCH_FAILED_NO_PERMISSION);
        boolean success = orderUniqueBarCodeRPCClient.modifyBatchesForEachProductEachCode(request.getOrderUniqueBarCodeList());
        return RemoteResponse.<Boolean>custom().setSuccess().setData(success);
    }

    @RpcMethod("修改批次信息(不需要二维码的模式), orderUniqueBarCodeList必填")
    @RpcMapping("/saveOrUpdateCommonBatches")
    public RemoteResponse<Boolean> saveOrUpdateCommonBatches(OrderUniqueBarCodeRequest request) {
        return RemoteResponse.success(orderUniqueBarCodeRPCClient.modifyCommonBatches(request.getOrderUniqueBarCodeList()));
    }

    @RpcMethod("获取订单条形码批次总计信息, orderNo必填")
    @RpcMapping("/getOrderBatchesStatistics")
    public RemoteResponse<List<OrderUniqueBarCodeStatisticsDTO> > getOrderBatchesStatistics(RjSessionInfo rjSessionInfo, OrderBarCodeQryReqDTO request) {
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.BARCODE_BATCH_INFO_FAILED);
        Preconditions.notNull(request.getOrderNo(), "获取订单条形码批次总计信息失败，订单号不可为空！");
        List<OrderUniqueBarCodeStatisticsDTO> statistics = orderBatchService.getBarCodeBatchesByOrderNo(request.getOrderNo(), request.getTypeList());
        return RemoteResponse.<List<OrderUniqueBarCodeStatisticsDTO>>custom().setSuccess().setData(statistics);
    }

    @RpcMethod("获取单个商品条形码批次总计信息, orderDetailId必填")
    @RpcMapping("/getOrderDetailBatchesStatistics")
    public RemoteResponse<OrderUniqueBarCodeStatisticsDTO> getOrderDetailBatchesStatistics(RjSessionInfo rjSessionInfo, OrderBasicParamDTO request) {
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.BARCODE_BATCH_INFO_FAILED);
        Preconditions.notNull(request.getOrderDetailId(), "获取订单条形码批次总计信息失败, orderDetailId不可为空!");
        OrderUniqueBarCodeStatisticsDTO statistics = orderUniqueBarCodeRPCClient.getBarCodeBatchesByDetailId(request.getOrderDetailId());
        return RemoteResponse.<OrderUniqueBarCodeStatisticsDTO>custom().setSuccess().setData(statistics);
    }

    @RpcMethod("获取订单全部条形码批次信息, orderNo必填")
    @RpcMapping("/outputAllOrderBatches")
    public RemoteResponse<OrderBatchesVO> outputAllOrderBatches(RjSessionInfo rjSessionInfo, OrderBarCodeQryReqDTO request) {
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.QUERY_BATCH_FAILED_NO_PERMISSION);
        Preconditions.notNull(request, "入参为空!");
        OrderBatchesVO result = orderUniqueBarCodeRPCClient.getOrderBatches(request.getOrderNo(), request.getTypeList());
        return RemoteResponse.<OrderBatchesVO>custom().setSuccess().setData(result);
    }

    @RpcMethod("生成订单条形码信息, orderNo必填")
    @RpcMapping("/generateOrderBarCode")
    public RemoteResponse<Boolean> generateOrderBarCode(OrderBarCodeQryReqDTO request) {
        return RemoteResponse.success(orderUniqueBarCodeRPCClient.generateOrderBarCode(request.getOrderNo(), request.getTypeList()));
    }

    @RpcMethod("通过码获取单个条形码批次信息, barCode必填,只有扫码退货用到")
    @RpcMapping("/getBatchesByBarCodeForReturn")
    public RemoteResponse<UniqueBarCodeDTO> getBatchesByBarCodeForReturn(RjSessionInfo rjSessionInfo, OrderDetailReq request) {
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.QUERY_BATCH_FAILED_NO_PERMISSION);
        Preconditions.notNull(request.getBarCode(), "条形码为空!");
        return RemoteResponse.<UniqueBarCodeDTO>custom().setSuccess().setData(orderUniqueBarCodeRPCClient.findCanReturnBatchesByBarcode(request.getBarCode()));
    }

    @RpcMethod("订单手动退货, orderNo, orderUniqueBarCodeList必填")
    @RpcMapping("/manualOrderReturn")
    public RemoteResponse<Boolean> manualOrderReturn(RjSessionInfo rjSessionInfo, GoodsReturnBarCodeRequest request) {
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.QUERY_BATCH_FAILED_NO_PERMISSION);
        Preconditions.notNull(request, "入参为空!");
        boolean isSuccess = barCodeGoodsReturnService.applyOrderReturnForBarCode(rjSessionInfo, request);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(isSuccess);
    }

    @RpcMethod("订单扫码退货, goodsReturnOrderList必填")
    @RpcMapping("/applyOrderReturnByBarCodeAssemble")
    public RemoteResponse<BarCodeReturnResultVO> applyOrderReturnByBarCodeAssemble(RjSessionInfo rjSessionInfo, BarCodeGoodsReturnRequest request) {
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.QUERY_BATCH_FAILED_NO_PERMISSION);
        Preconditions.notNull(request.getGoodsReturnOrderList(), "入参为空!");
        
        // 调用服务处理退货业务逻辑，返回包含成功和失败信息的结果对象
        BarCodeReturnResultVO resultVO = barCodeGoodsReturnService.applyOrderReturnByBarCodeAssemble(rjSessionInfo, request);
        
        return RemoteResponse.<BarCodeReturnResultVO>custom().setSuccess().setData(resultVO);
    }

    @RpcMethod("退货单详情, returnNo, detailId必填")
    @RpcMapping("/getReturnDetailBatches")
    public RemoteResponse<OrderBatchesVO> getReturnDetailBatches(RjSessionInfo rjSessionInfo, GoodsReturnDTO request) {
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.QUERY_BATCH_FAILED_NO_PERMISSION);
        Preconditions.notNull(request.getReturnNo(), "查询订单明细商品的批次失败, returnNo为空!");
        Preconditions.notNull(request.getDetailId(), "查询订单明细商品的批次失败, detailId为空!");
        OrderBatchesVO vo = orderUniqueBarCodeRPCClient.findByReturnNoAndDetailId(request.getReturnNo(), request.getDetailId());
        return RemoteResponse.<OrderBatchesVO>custom().setSuccess().setData(vo);
    }

    @RpcMethod("退货单详情, returnNo必填")
    @RpcMapping("/getReturnAllDetailBatches")
    public RemoteResponse<List<UniqueBarCodeDTO>> getReturnAllDetailBatches(RjSessionInfo rjSessionInfo, GoodsReturnDTO request) {
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.QUERY_BATCH_FAILED_NO_PERMISSION);
        Preconditions.notNull(request.getReturnNo(), "查询订单明细商品的批次失败, returnNo为空!");
        return RemoteResponse.<List<UniqueBarCodeDTO>>custom().setSuccess().setData(orderUniqueBarCodeRPCClient.getReturnAllDetailBatches(request.getReturnNo()));
    }
}
