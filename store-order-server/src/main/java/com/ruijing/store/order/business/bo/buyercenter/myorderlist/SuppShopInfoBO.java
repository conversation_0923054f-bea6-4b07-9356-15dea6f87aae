package com.ruijing.store.order.business.bo.buyercenter.myorderlist;

import com.reagent.supp.api.brand.dto.BrandDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2021/9/7 11:13
 */
public class SuppShopInfoBO {

    /**
     * 位运算符号
     */
    private Integer suppFlag;

    /**
     * 供应商 ID
     */
    private Long id;

    /**
     * 供应商手机
     */
    private String mobile;

    /**
     * 供应商电话
     */
    private String telephone;

    /**
     * 供应商编码
     */
    private String suppCode;

    /**
     * 供应商名称
     */
    private String suppName;

    /**
     * 供应商 GUID
     */
    private String guid;

    /**
     * Email
     */
    private String email;

    /**
     * Banner 地址
     */
    private String BannerUrl;

    /**
     * 联系人
     */
    private String fcontactman;

    /**
     * QQ
     */
    private String qq;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 县区
     */
    private String country;

    /**
     * 地址
     */
    private String address;

    /**
     * 交易数
     */
    private Integer transactions;

    /**
     * 品牌
     */
    private List<BrandDTO> brands;

    private String logoUrl;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 创建时间
     */
    private LocalDateTime createAt;

    /**
     * Is activated or not
     */
    private Boolean isActivated;

    /**
     * store description
     */
    private String description;

    public Integer getSuppFlag() {
        return suppFlag;
    }

    public void setSuppFlag(Integer suppFlag) {
        this.suppFlag = suppFlag;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getSuppCode() {
        return suppCode;
    }

    public void setSuppCode(String suppCode) {
        this.suppCode = suppCode;
    }

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getBannerUrl() {
        return BannerUrl;
    }

    public void setBannerUrl(String bannerUrl) {
        BannerUrl = bannerUrl;
    }

    public String getFcontactman() {
        return fcontactman;
    }

    public void setFcontactman(String fcontactman) {
        this.fcontactman = fcontactman;
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getTransactions() {
        return transactions;
    }

    public void setTransactions(Integer transactions) {
        this.transactions = transactions;
    }

    public List<BrandDTO> getBrands() {
        return brands;
    }

    public void setBrands(List<BrandDTO> brands) {
        this.brands = brands;
    }

    public String getLogoUrl() {
        return logoUrl;
    }

    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public LocalDateTime getCreateAt() {
        return createAt;
    }

    public void setCreateAt(LocalDateTime createAt) {
        this.createAt = createAt;
    }

    public Boolean getActivated() {
        return isActivated;
    }

    public void setActivated(Boolean activated) {
        isActivated = activated;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("SuppShopInfoBO{");
        sb.append("suppFlag=").append(suppFlag);
        sb.append(", id=").append(id);
        sb.append(", mobile='").append(mobile).append('\'');
        sb.append(", telephone='").append(telephone).append('\'');
        sb.append(", suppCode='").append(suppCode).append('\'');
        sb.append(", suppName='").append(suppName).append('\'');
        sb.append(", guid='").append(guid).append('\'');
        sb.append(", email='").append(email).append('\'');
        sb.append(", BannerUrl='").append(BannerUrl).append('\'');
        sb.append(", fcontactman='").append(fcontactman).append('\'');
        sb.append(", qq='").append(qq).append('\'');
        sb.append(", province='").append(province).append('\'');
        sb.append(", city='").append(city).append('\'');
        sb.append(", country='").append(country).append('\'');
        sb.append(", address='").append(address).append('\'');
        sb.append(", transactions=").append(transactions);
        sb.append(", brands=").append(brands);
        sb.append(", logoUrl='").append(logoUrl).append('\'');
        sb.append(", storeName='").append(storeName).append('\'');
        sb.append(", createAt=").append(createAt);
        sb.append(", isActivated=").append(isActivated);
        sb.append(", description='").append(description).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
