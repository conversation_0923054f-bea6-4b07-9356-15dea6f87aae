package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.reagent.order.base.order.dto.OrderAcceptCommentDTO;
import com.reagent.order.base.order.dto.OrderAcceptQueryDTO;
import com.reagent.order.base.order.dto.OrderDetailAcceptanceFileDTO;
import com.reagent.order.base.order.dto.OrderDetailAcceptancePicDTO;
import com.reagent.order.base.order.dto.request.OrderDetailAcceptanceFileRequestDTO;
import com.reagent.order.base.order.dto.request.OrderDetailAcceptancePicRequestDTO;
import com.reagent.order.base.order.service.OrderAcceptCommentRpcService;
import com.reagent.order.base.order.service.OrderDetailAcceptanceFileRpcService;
import com.reagent.order.base.order.service.OrderDetailAcceptancePicRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderAcceptCommentVO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2021/2/26 14:14
 * @Description
 **/
@ServiceClient
public class OrderAcceptCommentClient {

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    private OrderAcceptCommentRpcService orderAcceptCommentRpcService;

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    private OrderDetailAcceptancePicRpcService orderDetailAcceptancePicRpcService;

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    private OrderDetailAcceptanceFileRpcService orderDetailAcceptanceFileRpcService;

    public Map<Integer, String> getOrgRelatedCommentMap(Integer orgId) {
        return OrderAcceptCommentRpcService.ORG_COMMENT.get(orgId);
    }

    /**
     * @description: 保存订单验收评价信息
     * @date: 2021/2/26 14:46
     * @author: zengyanru
     * @param orgId
     * @param orderId
     * @param commentTagList
     * @return void
     */
    @ServiceLog(description = "保存订单验收评价信息", serviceType = ServiceType.COMMON_SERVICE)
    public void saveOrderComment(Integer orgId, Integer orderId, List<Integer> commentTagList) {
        OrderAcceptCommentDTO orderAcceptCommentDTO = new OrderAcceptCommentDTO();
        orderAcceptCommentDTO.setOrderId(orderId).setOrgId(orgId).setAcceptCommentTagList(commentTagList);
        RemoteResponse<Boolean> response = orderAcceptCommentRpcService.saveOrderComment(orderAcceptCommentDTO);
        Preconditions.notNull(response, "调用orderAcceptCommentRpcService.saveOrderComment出错，入参：\norgId=" + orgId + ", orderId=" + orderId + ", commentTagList=" + commentTagList);
        Preconditions.isTrue(response.isSuccess() && response.getData(), "调用orderAcceptCommentRpcService.saveOrderComment出错。" + response.getMsg() + ", 入参：\norgId=" + orgId + ", orderId=" + orderId + ", commentTagList=" + commentTagList);
    }

    /**
     * @description: 获取订单验收评价信息
     * @date: 2021/2/26 14:46
     * @author: zengyanru
     * @param orgId
     * @param orderIdList
     * @return com.reagent.order.base.order.dto.OrderAcceptCommentDTO
     */
    @ServiceLog(description = "获取订单验收评价信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderAcceptCommentVO> getOrderComment(Integer orgId, List<Integer> orderIdList) {
        OrderAcceptQueryDTO orderAcceptQueryDTO = new OrderAcceptQueryDTO();
        orderAcceptQueryDTO.setOrderIdList(orderIdList).setOrgId(orgId);
        RemoteResponse<List<OrderAcceptCommentDTO>> response = orderAcceptCommentRpcService.getOrderComment(orderAcceptQueryDTO);

        Preconditions.notNull(response, "调用orderAcceptCommentRpcService.getOrderComment，入参：\norgId=" + orgId + ", orderIdList=" + orderIdList);
        Preconditions.isTrue(response.isSuccess(), "调用orderAcceptCommentRpcService.getOrderComment。" + response.getMsg() + "，入参：\norgId=" + orgId + ", orderIdList=" + orderIdList);
        List<OrderAcceptCommentDTO> orderAcceptCommentList = response.getData();
        Map<Integer, String> orgCommentMap = OrderAcceptCommentRpcService.ORG_COMMENT.get(orgId);

        // 循环设置订单验收评价返回体
        List<OrderAcceptCommentVO> orderAcceptCommentVOList = New.list();
        for (OrderAcceptCommentDTO orderAcceptCommentDTO : orderAcceptCommentList) {
            List<Integer> acceptCommentTagList = orderAcceptCommentDTO.getAcceptCommentTagList();
            List<String> acceptCommentStringList = New.list();
            // TODO: 沦为冗余信息，若2021-04-01仍未使用，可删除此for循环的验收评价文字构建信息
            for (Integer commentTag : acceptCommentTagList) {
                acceptCommentStringList.add(orgCommentMap.get(commentTag));
            }
            OrderAcceptCommentVO acceptCommentVO = new OrderAcceptCommentVO();
            acceptCommentVO.setOrgId(orgId).setOrderId(orderAcceptCommentDTO.getOrderId()).setAcceptCommentList(acceptCommentStringList).setAcceptCommentTagList(acceptCommentTagList);
            orderAcceptCommentVOList.add(acceptCommentVO);
        }
        return orderAcceptCommentVOList;
    }

    /**
     * @description: 订单验收评价，DTO到VO转换静态方法
     * @date: 2021/2/26 14:47
     * @author: zengyanru
     * @param input
     * @return com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderAcceptCommentVO
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "订单验收评价，DTO到VO转换静态方法")
    public static OrderAcceptCommentVO OrderAcceptCommentDTOToVO(OrderAcceptCommentDTO input) {
        OrderAcceptCommentVO output = new OrderAcceptCommentVO();
        return output.setAcceptCommentList(input.getAcceptCommentList())
                .setAcceptCommentTagList(input.getAcceptCommentTagList())
                .setOrderId(input.getOrderId())
                .setOrgId(input.getOrgId());
    }

    /**
     * @description: 订单验收评价，VO到DTO转换静态方法
     * @date: 2021/2/26 14:48
     * @author: zengyanru
     * @param input
     * @return com.reagent.order.base.order.dto.OrderAcceptCommentDTO
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "订单验收评价，VO到DTO转换静态方法")
    public static OrderAcceptCommentDTO OrderAcceptCommentVOToDTO(OrderAcceptCommentVO input) {
        OrderAcceptCommentDTO output = new OrderAcceptCommentDTO();
        return output.setAcceptCommentList(input.getAcceptCommentList())
                .setAcceptCommentTagList(input.getAcceptCommentTagList())
                .setOrderId(input.getOrderId())
                .setOrgId(input.getOrgId());
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "根据订单查询 详情关联验收图片列表")
    public List<OrderDetailAcceptancePicDTO> listDetailAcceptancePic(Integer orderId) {
        RemoteResponse<List<OrderDetailAcceptancePicDTO>> response = orderDetailAcceptancePicRpcService.listByOrderId(orderId);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "根据订单ID删除 详情关联验收图片列表")
    public Boolean deleteDetailAcceptancePic(Integer orderId) {
        RemoteResponse<Boolean> response = orderDetailAcceptancePicRpcService.deleteByOrderId(orderId);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "批量保存 详情关联验收图片列表")
    public Boolean batchSaveDetailAcceptancePic(List<OrderDetailAcceptancePicRequestDTO> list) {
        RemoteResponse<Boolean> response = orderDetailAcceptancePicRpcService.batchSave(list);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "根据订单查询 详情关联验收文件列表")
    public List<OrderDetailAcceptanceFileDTO> listDetailAcceptanceFile(Integer orderId) {
        RemoteResponse<List<OrderDetailAcceptanceFileDTO>> response = orderDetailAcceptanceFileRpcService.listByOrderId(orderId);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "根据订单ID删除 详情关联验收文件列表")
    public Boolean deleteDetailAcceptanceFile(Integer orderId) {
        RemoteResponse<Boolean> response = orderDetailAcceptanceFileRpcService.deleteByOrderId(orderId);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "批量保存 详情关联验收文件列表")
    public Boolean batchSaveDetailAcceptanceFile(List<OrderDetailAcceptanceFileRequestDTO> list) {
        RemoteResponse<Boolean> response = orderDetailAcceptanceFileRpcService.batchSave(list);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "根据订单批量查询 详情关联验收图片列表")
    public List<OrderDetailAcceptancePicDTO> listDetailAcceptancePicByOrderIds(List<Integer> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return New.emptyList();
        }

        List<OrderDetailAcceptancePicDTO> result = New.list();
        List<List<Integer>> batchOrderIds = Lists.partition(orderIds, 200);
        for (List<Integer> batch : batchOrderIds) {
            RemoteResponse<List<OrderDetailAcceptancePicDTO>> response = orderDetailAcceptancePicRpcService.listByOrderIds(New.list(batch));
            Preconditions.isTrue(response.isSuccess(), response.getMsg());
            if (Objects.nonNull(response.getData())) {
                result.addAll(response.getData());
            }
        }
        return result;
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "根据订单批量查询 详情关联验收文件列表")
    public List<OrderDetailAcceptanceFileDTO> listDetailAcceptanceFileByOrderIds(List<Integer> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return New.emptyList();
        }

        List<OrderDetailAcceptanceFileDTO> result = New.list();
        List<List<Integer>> batchOrderIds = Lists.partition(orderIds, 200);
        for (List<Integer> batch : batchOrderIds) {
            RemoteResponse<List<OrderDetailAcceptanceFileDTO>> response = orderDetailAcceptanceFileRpcService.listByOrderIds(New.list(batch));
            Preconditions.isTrue(response.isSuccess(), response.getMsg());
            if (Objects.nonNull(response.getData())) {
                result.addAll(response.getData());
            }
        }
        return result;
    }


}
