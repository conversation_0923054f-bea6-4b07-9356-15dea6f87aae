package com.ruijing.store.order.business.service;

import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.*;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;

import java.text.ParseException;
import java.util.List;
import java.util.Set;

/**
 * @description 订单详情相关业务
 * <AUTHOR>
 */
public interface OrderDetailRelatedService {

    /**
     * 通过fmasterid 和 退货状态查找订单详情对应的主订单id
     * @param returnStatus
     * @return
     */
    Set<Integer> findFmasterIdByReturnStatus(List<Integer> masterIdList, List<Integer> returnStatus);

    /**
     * @description: 获取图片上传配置
     * @date: 2020/12/21 15:44
     * @author: zengyanru
     * @param
     * @return BaseConfigDTO
     */
    BaseConfigDTO getPicConfig(RjSessionInfo rjSessionInfo);

    /**
     * @description: 通过订单ID获取发票详细信息
     * @date: 2020/12/21 16:29
     * @author: zengyanru
     * @param orderId
     * @param orderNo
     * @return java.util.List<com.ruijing.store.order.base.core.vo.orderdetail.OrderInvoiceInfoVO>
     */
    List<OrderInvoiceInfoVO> listInvoiceByOrder(Integer orderId, String orderNo, Integer orgId);


    /**
     *  检索订单发票信息
     *
     * @param orderId
     * @param orderNo
     * @param orgId
     * @param userId
     * @param accessCode
     * @return
     */
    OrderInvoiceInfoRespVO retrieveInvoicesForOrder(Integer orderId, String orderNo, Integer orgId, Long userId, String accessCode);


    /**
     * @description: 获取订单相关配置-验收、经费卡、合同相关
     * @date: 2020/12/22 14:19
     * @author: zengyanru
     * @param orgCode
     * @param configCodeList
     * @return com.ruijing.store.order.base.core.vo.orderdetail.OrderRelatedConfigVO
     */
    OrderRelatedConfigVO getOrderRelatedConfig(String orgCode, List<String> configCodeList);

    /**
     * @description: 通过订单id获取订单详情信息
     * @date: 2020/12/22 14:58
     * @author: zengyanru
     * @param request
     * @return com.ruijing.store.order.base.core.vo.orderlist.OrderInfoVO
     */
    OrderInfoVO getOrderDetail(LoginUserInfoBO loginInfo, OrderBasicParamDTO request, Boolean isHms) throws ParseException;

    /**
     * 获取订单详情数据
     * @param loginInfo 登录信息
     * @param isHms 是否hms
     * @param orderMasterDO 订单主表数据
     * @return 订单详情数据
     * @throws ParseException
     */
    OrderInfoVO getOrderDetail(LoginUserInfoBO loginInfo, Boolean isHms, OrderMasterDO orderMasterDO) throws ParseException;
    
    /**
     * @description: 通过订单id获取采购单或者竞价单快照信息
     * @date: 2021/1/4 15:01
     * @author: zengyanru
     * @param orderId
     * @return com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderSnapshotVO
     */
    OrderSnapshotVO getOrderSnapshot(Integer orderId);

    /**
     * @description: 通过订单主表信息获取线下单信息
     * @date: 2021/1/19 18:32
     * @author: zengyanru
     * @param orderMasterInfo
     * @return com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderOfflineInfoVO
     */
    OrderOfflineInfoVO getOfflineInfo(OrderMasterDO orderMasterInfo);

    /**
     * 通过订单主表信息列表获取线下单信息列表
     * @param masterSearchList
     * @return
     */
    List<OrderOfflineInfoVO> getOfflineInfoList(List<OrderMasterSearchDTO> masterSearchList);

    /**
     * 获取买家留言
     * @param buyAppId 采购单id
     * @param suppId 供应商id
     * @return
     */
    String getOrderRemark(Integer buyAppId, Integer suppId);
}
