package com.ruijing.store.order.gateway.supplier.controller;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.GateWayController;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.store.order.gateway.supplier.request.SuppSearchCommonRequestDTO;
import com.ruijing.store.order.gateway.supplier.service.SuppRelatedCommonService;
import com.ruijing.store.order.gateway.supplier.vo.SuppInfoVO;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/1 15:58
 * @description 供应商相关通用controller
 */
@GateWayController(requestMapping = "/suppRelatedCommon")
public class SuppRelatedCommonGWController {
    
    @Resource
    private SuppRelatedCommonService suppRelatedCommonService;
    
    @RpcMapping("/searchSupp")
    public RemoteResponse<List<SuppInfoVO>> searchSupp(SuppSearchCommonRequestDTO requestDTO){
        Preconditions.notNull(requestDTO, "请求不能为空");
        return RemoteResponse.<List<SuppInfoVO>>custom().setData(suppRelatedCommonService.fuzzySearchSuppInfoByName(requestDTO.getSuppName())).setSuccess();
    }
}
