package com.ruijing.store.order.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.Hashtable;

/**
 * <AUTHOR>
 * @Date 2020/5/11 17:34
 * @Version 1.0
 * @Description 二维码工具类
 */
public class QrCodeUtil {

    /**
     * 功能描述: 生成二维码 BufferedImage.
     *
     * @param content
     * @param qrWidth
     * @param qrHeight
     * @return java.awt.image.BufferedImage
     * <AUTHOR>
     * @date 2019/5/31 9:13
     */
    public static BufferedImage getBufferImage(String content, int qrWidth, int qrHeight) throws Exception {
        Hashtable<EncodeHintType, Object> hints = new Hashtable<EncodeHintType, Object>();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
        hints.put(EncodeHintType.MARGIN, 1);
        BitMatrix bitMatrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, qrWidth, qrHeight, hints);
        int width = bitMatrix.getWidth();
        int height = bitMatrix.getHeight();
        BufferedImage image = new BufferedImage(qrWidth, qrHeight, BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, bitMatrix.get(x, y) ? 0xFF000000 : 0xFFFFFFFF);
            }
        }
        return image;
    }

    /**
     * 功能描述:  生成base64格式二维码.
     *
     * @param content  content
     * @param qrWidth  qrWidth
     * @param qrHeight qrHeight
     * @return string
     * <AUTHOR>
     * @date 2019/5/31 9:18
     */
    public static String getBase64(String content, int qrWidth, int qrHeight) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            BufferedImage image = getBufferImage(content, qrWidth, qrHeight);
            //转换成png格式的IO流
            ImageIO.write(image, "png", byteArrayOutputStream);
        } catch (Exception e) {
            e.printStackTrace();
        }
        byte[] bytes = byteArrayOutputStream.toByteArray();
        BASE64Encoder encoder = new BASE64Encoder();
        String base64 = encoder.encodeBuffer(bytes).trim();
        return base64;
    }
}
