package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.store.apply.dto.ApplyRefWarehouseDTO;
import com.ruijing.store.apply.dto.CommonRequestDTO;
import com.ruijing.store.apply.service.ApplyRefWarehouseService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/11/19 10:21
 */
@ServiceClient
public class ApplyRefWarehouseServiceClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApplyRefWarehouseServiceClient.class);

    private static final String CAT_TYPE = "applyRefWarehouseServiceClient";

    @MSharpReference(remoteAppkey="store-apply-service")
    private ApplyRefWarehouseService applyRefWarehouseService;

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public ApplyRefWarehouseDTO getWarehouseIdByApplicationId(Integer purchaseApplicationId) {
        if (purchaseApplicationId == null) {
            return null;
        }
        CommonRequestDTO applicationRequest = new CommonRequestDTO();
        applicationRequest.setApplyIds(Collections.singleton(purchaseApplicationId));
        RemoteResponse<List<ApplyRefWarehouseDTO>> response = applyRefWarehouseService.listApplyRefWarehouse(applicationRequest);
        if (!response.isSuccess()) {
            Cat.logError(CAT_TYPE, "getWarehouseIdByApplicationId", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + purchaseApplicationId + "\n", new RuntimeException("RPC调用返回失败结果"));
            throw new RuntimeException(response.getMsg());
        }
        List<ApplyRefWarehouseDTO> applyRefWarehouseDTOList = response.getData();
        //过滤掉库房id为0的情况（保存申请单时，实现方把不用选择库房的申请单设置库房id为0，所以查出来库房id为0的情况是没选择库房的，不应该返回）
        if (CollectionUtils.isNotEmpty(applyRefWarehouseDTOList)) {
            applyRefWarehouseDTOList = applyRefWarehouseDTOList.stream().filter(o -> o.getWarehouseId() > 0).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(applyRefWarehouseDTOList)) {
            LOGGER.info("找不到采购申请单{}对应的库房列表", purchaseApplicationId);
            return null;
        }
        return applyRefWarehouseDTOList.get(0);
    }
}
