package com.ruijing.store.order.rpc.business;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.research.api.dto.ExtraDTO;
import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.financial.docking.dto.order.OrderDTO;
import com.reagent.research.financial.docking.dto.order.OrderDetailDTO;
import com.reagent.tpi.tpiclient.api.order.v2.OrderBusinessService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.store.order.rpc.client.ApplicationBaseClient;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.util.TranslateUtils;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * @description:
 * @author: zhongyulei
 * @create: 2021/3/2 14:26
 **/
@MSharpService
public class OrderBusinessServiceImpl implements OrderBusinessService {

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Override
    public CompletableFuture<RemoteResponse<List<OrderDTO>>> getOrders(OrgRequest<List<String>> request) {
        List<String> orderNoList = request.getData();
        return AsyncExecutor.callAsync(() -> this.getOrders(orderNoList));
    }

    @ServiceLog(description = "第三方平台查询订单")
    RemoteResponse<List<OrderDTO>> getOrders(List<String> request) {
        if (CollectionUtils.isEmpty(request)) {
            return RemoteResponse.<List<OrderDTO>>custom().setFailure("查询失败，订单号为空");
        }
        Preconditions.isTrue(request.size() < 301, "单次批量查询不超过300个单");
        OrderSearchParamDTO params = new OrderSearchParamDTO();
        params.setOrderNoList(request);
        List<OrderDTO> result = TranslateUtils.toModelList(
                () -> orderSearchBoostService.commonSearch(params).getRecordList(),
                this::toTPIOrderDTOStrategy
        );
        return RemoteResponse.<List<OrderDTO>>custom().setSuccess().setData(result);
    }

    /**
     * 订单dto转TPI订单dto
     * @param searchDTO 订单dto
     * @return          TPI订单对象
     */
    private OrderDTO toTPIOrderDTOStrategy(OrderMasterSearchDTO searchDTO) {
        OrderDTO result = new OrderDTO();
        result.setAppKey(Environment.getAppKey());
        result.setOrderNo(searchDTO.getForderno());
        result.setStatus(searchDTO.getStatus());
        result.setOrderDate(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, searchDTO.getForderdate()));

        if (OrgEnum.HUA_NAN_SHI_FAN_DA_XUE.getValue() == searchDTO.getFuserid()) {
            String permitCard = applicationBaseClient.findPermitCardByApplicationId(searchDTO.getFtbuyappid());
            result.setBuyerJobNumber(permitCard);
            result.setManagerJobNumber(permitCard);
        }

        result.setBuyerName(searchDTO.getFbuyername());
        result.setBuyerTelephone(searchDTO.getFbuyertelephone());
        result.setOrderAmountTotal(searchDTO.getForderamounttotal() != null ? BigDecimal.valueOf(searchDTO.getForderamounttotal()) : BigDecimal.ZERO);
        result.setDeliveryPlace(searchDTO.getFbiderdeliveryplace());
        List<OrderDetailDTO> orderDetailDTOS = TranslateUtils.toModelList(
                () -> searchDTO.getOrderDetail(),
                this::toTPIOrderDetailDTO
        );
        result.setOrderDetailDTOs(orderDetailDTOS);
        result.setExtraDTOs(this.toTPIExtraDTOStrategy(searchDTO));
        result.setOrderDateString(searchDTO.getForderdate());

        return result;
    }

    private OrderDetailDTO toTPIOrderDetailDTO(OrderDetailSearchDTO searchDTO) {
        OrderDetailDTO result = new OrderDetailDTO();
        result.setGoodsName(searchDTO.getFgoodname());
        result.setQuantity(BigDecimal.valueOf(searchDTO.getFquantity().doubleValue()));
        result.setPrice(BigDecimal.valueOf(searchDTO.getFbidprice()));
        result.setGoodsCode(searchDTO.getFgoodcode());
        result.setBrand(searchDTO.getFbrand());
        result.setSpecification(searchDTO.getFspec());
        result.setExtraDTOs(detailToTPIExtraDTO(searchDTO));
        result.setGoodsId(searchDTO.getProductId().toString());

        return result;
    }

    private List<ExtraDTO> toTPIExtraDTOStrategy(OrderMasterSearchDTO searchDTO) {
        List<ExtraDTO> list = new ArrayList<>();
        if (OrgEnum.HUA_NAN_SHI_FAN_DA_XUE.getValue() == searchDTO.getFuserid()) {
            ExtraDTO e1 = new ExtraDTO();
            e1.setField("payAmount");
            e1.setValue(searchDTO.getActualAmount() == null ? "0.00" : searchDTO.getActualAmount().toString());

            ExtraDTO e2 = new ExtraDTO();
            e2.setField("freightAmount");
            e2.setValue(searchDTO.getCarryFee() == null ? "0.00" : searchDTO.getCarryFee().toString());
            list.add(e1);
            list.add(e2);
        }
        return list;
    }

    private List<ExtraDTO> detailToTPIExtraDTO(OrderDetailSearchDTO searchDTO) {
        List<ExtraDTO> list = new ArrayList<>();
        ExtraDTO e1 = new ExtraDTO();
        e1.setField("returnCount");
        e1.setValue(searchDTO.getFcancelquantity() == null ? "0.00" : searchDTO.getFcancelquantity().toString());

        ExtraDTO e2 = new ExtraDTO();
        e2.setField("returnAmount");
        e2.setValue(searchDTO.getReturnAmount() == null ? "0.00" : searchDTO.getReturnAmount().toString());
        list.add(e1);
        list.add(e2);
        return list;
    }

}
