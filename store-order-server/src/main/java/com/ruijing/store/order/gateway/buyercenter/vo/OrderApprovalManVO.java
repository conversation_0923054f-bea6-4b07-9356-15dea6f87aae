package com.ruijing.store.order.gateway.buyercenter.vo;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON>g <PERSON>
 * @Description:
 * @DateTime: 2022/2/16 16:35
 */
public class OrderApprovalManVO implements Serializable {

    private static final long serialVersionUID = 579354632177018147L;

    private List<String> approveManList;

    public List<String> getApproveManList() {
        return approveManList;
    }

    public void setApproveManList(List<String> approveManList) {
        this.approveManList = approveManList;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderApprovalManVO{");
        sb.append("approveManList=").append(approveManList);
        sb.append('}');
        return sb.toString();
    }
}
