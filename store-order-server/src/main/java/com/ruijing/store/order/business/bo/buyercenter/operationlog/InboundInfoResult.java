package com.ruijing.store.order.business.bo.buyercenter.operationlog;

import java.util.Date;

/**
 * @Author: <PERSON>g <PERSON>
 * @Date: 2021/1/12 22:01
 */
public class InboundInfoResult {

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 操作人
     */
    private String opeartor;

    /**
     * 审批时间
     */
    private  Integer approveTimes;

    /**
     * 订单状态
     */
    private Integer status;

    public Date getOperationTime() {
        return operationTime;
    }

    public InboundInfoResult setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
        return this;
    }

    public Integer getApproveTimes() {
        return approveTimes;
    }

    public InboundInfoResult setApproveTimes(Integer approveTimes) {
        this.approveTimes = approveTimes;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public InboundInfoResult setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public String getOpeartor() {
        return opeartor;
    }

    public InboundInfoResult setOpeartor(String opeartor) {
        this.opeartor = opeartor;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("InboundInfoResult{");
        sb.append("operationTime=").append(operationTime);
        sb.append(", opeartor='").append(opeartor).append('\'');
        sb.append(", approveTimes=").append(approveTimes);
        sb.append(", status=").append(status);
        sb.append('}');
        return sb.toString();
    }
}
