package com.ruijing.store.order.business.service.impl;

import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.service.constant.OrderInboundSucceedConstant;
import org.springframework.stereotype.Service;

/**
 * @description: 孙逸仙入库回调的策略
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-01-14 15:58
 */
@Service(OrderInboundSucceedConstant.ZHONG_SHAN_DA_XUE_SUN_YI_XIAN_JI_NIAN_YI_YUAN_INBOUND_CALLBACK)
public class SunYiXianInboundSucceedCallbackService extends InboundSucceedCallbackService {

    @Override
    protected void statementHandler(Integer inventoryStatus, OrderMasterDO orderInfo, boolean usedStatement) {
        // 如果是孙逸仙，旧单用的是经费状态作为判断条件，旧单全部改为自结算。而一般的单位自结算单是直接到结束的，孙逸仙由于旧单处理方式原因，自结算的单也推到结算。
        getOrderStatementService().orderStatementCore(orderInfo, orderInfo.getFbuyerid(), orderInfo.getFbuyername(), usedStatement, orderInfo.getInventoryStatus().intValue());
    }
}
