package com.ruijing.store.order.business.service.impl;

import com.google.common.collect.Lists;
import com.mchange.lang.IntegerUtils;
import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.credit.api.request.SendOrderReceivedData;
import com.reagent.credit.api.request.SendOrderRecivedDataReq;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.dto.request.OrderDetailAcceptanceFileRequestDTO;
import com.reagent.order.base.order.dto.request.OrderDetailAcceptancePicRequestDTO;
import com.reagent.order.base.order.enums.product.OrderProductBatchesStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductInventoryStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductTransactionStatusEnum;
import com.reagent.order.dto.request.ThirdPartOrderMasterDTO;
import com.reagent.order.enums.OrderEventTypeEnum;
import com.reagent.order.enums.OuterBuyerDockingTypeEnum;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.dto.v2.FundCardExtraDTO;
import com.reagent.research.statement.api.invoice.dto.InvoiceDTO;
import com.reagent.tpi.tpiclient.message.req.order.HNOrderBySelfBuyingReq;
import com.reagent.tpi.tpiclient.message.resp.BaseResp;
import com.reagent.tpi.tpiclient.message.resp.order.OrderResp;
import com.ruijing.base.gateway.api.dto.GatewayConstant;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.base.store.auth.api.constant.BuyerCenterAccessConstant;
import com.ruijing.cooperation.cbsd.api.msg.RecordOrderGoodsInfoDTO;
import com.ruijing.cooperation.cbsd.api.msg.RecordOrderInfoDTO;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.util.Pair;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.collections.SetBuilder;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.exception.CallRpcException;
import com.ruijing.order.saturn.api.accept.approve.request.SubmitAcceptApproveRequestDTO;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.utils.LocaleUtils;
import com.ruijing.order.whitehole.database.dto.file.OrderUploadFileDTO;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.shop.category.api.dto.CategoryDTO;
import com.ruijing.shop.category.api.enums.InboundTypeEnum;
import com.ruijing.shop.category.api.enums.RegulatoryTypeEnum;
import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.enums.OfflineAccountTypeEnum;
import com.ruijing.store.baseconfig.api.dto.typeConfig.orderContract.OrderContractConfig;
import com.ruijing.store.baseconfig.api.dto.typeConfig.orderContract.enums.OrderContractUploadConditionEnum;
import com.ruijing.store.baseconfig.api.dto.typeConfig.orderContract.enums.OrderUploadContractEnum;
import com.ruijing.store.baseconfig.api.enums.ConfigCodeEnum;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.component.BeanContainer;
import com.ruijing.store.enums.DangerousTagEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.docking.enums.DockingTypeEnum;
import com.ruijing.store.order.api.base.docking.enums.StatusExtraEnum;
import com.ruijing.store.order.api.base.enums.*;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.*;
import com.ruijing.store.order.api.base.other.dto.OrderBankDataDTO;
import com.ruijing.store.order.api.enums.FileBusinessTypeEnum;
import com.ruijing.store.order.api.file.request.OrderUploadFileDataDTO;
import com.ruijing.store.order.api.file.request.OrderUploadFileDataRequestDTO;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.core.translator.OrderDetailTranslator;
import com.ruijing.store.order.base.core.translator.OrderExtraTranslator;
import com.ruijing.store.order.base.core.translator.OrderMasterTranslator;
import com.ruijing.store.order.base.docking.mapper.DockingExtraMapper;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.base.minor.mapper.*;
import com.ruijing.store.order.base.minor.model.*;
import com.ruijing.store.order.base.minor.service.DangerousTagDOService;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutStatisticsService;
import com.ruijing.store.order.base.util.FundCardUtils;
import com.ruijing.store.order.base.util.OrderCommonUtils;
import com.ruijing.store.order.business.bo.ThirdPartyPlatformOrderBO;
import com.ruijing.store.order.business.enums.InWarehouseModeEnum;
import com.ruijing.store.order.business.enums.myorderlist.OrderAcceptPhotoConfigEnum;
import com.ruijing.store.order.business.enums.myorderlist.OrderSpeciesEnum;
import com.ruijing.store.order.business.enums.myorderlist.OrderUploadStatusEnum;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.handler.OrderMessageHandler;
import com.ruijing.store.order.business.service.OrderAcceptService;
import com.ruijing.store.order.business.service.OrderContractService;
import com.ruijing.store.order.business.service.OrderMasterForTPIService;
import com.ruijing.store.order.business.service.OrderStatementService;
import com.ruijing.store.order.business.service.constant.CustomAcceptorConstant;
import com.ruijing.store.order.business.service.constant.OrderAcceptConstant;
import com.ruijing.store.order.business.service.file.OrderUploadFileService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.*;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderAcceptanceConfigVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderFundcardVO;
import com.ruijing.store.order.gateway.file.request.OrderFileInfoRequestDTO;
import com.ruijing.store.order.gateway.file.vo.UploadFileInfoVO;
import com.ruijing.store.order.gateway.fundcard.service.OrderFundCardService;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.search.service.impl.OrderSearchBoostServiceImpl;
import com.ruijing.store.order.service.BizBaseService;
import com.ruijing.store.order.service.ResearchBaseService;
import com.ruijing.store.order.util.CommonValueUtils;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.user.api.dto.department.DepartmentThirdPartyDTO;
import com.ruijing.store.warehouse.callback.impl.WarehouseCallbackServiceImpl;
import com.ruijing.store.warehouse.service.WarehouseStockOccupyService;
import com.ruijing.store.warehouse.utils.OrderDetailsUtil;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDetailDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseExitDTO;
import com.ruijing.store.wms.api.enums.ApprovalTaskStatusEnum;
import com.ruijing.store.wms.api.enums.InboundStatus;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruijing.order.whitehole.eventbase.enums.file.FileBusinessTypeEnum.ACCEPTANCE_ATTACHMENT;
import static com.ruijing.order.whitehole.eventbase.enums.file.FileBusinessTypeEnum.ACCEPTANCE_VIDEO;
import static java.util.stream.Collectors.toList;

/**
 * @description: 订单验收实现, 还是逻辑太复杂了, 后续要策略模式等重构
 * @author: zhongyulei
 * @create: 2021/5/11 16:44
 **/
@Service
@Primary
public class OrderAcceptServiceImpl implements OrderAcceptService {

    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    private static final String CAT_TYPE = "OrderReceiptServiceImpl";



    /**
     * 系统用户id
     */
    private static final int systemOperatorId = -1;

    /**
     * 系统用户
     */
    private static final String systemOperatorName = "--";

    // 不需要自动验收订单的单位
    private final Set<String> NOT_AUTO_RECEIPT_ORG_SET = SetBuilder.<String>custom()
            .add("ZHONG_SHAN_DA_XUE")
            .add("ZHONG_SHAN_DA_XUE_SHEN_ZHEN")
            .add("ZHONG_SHAN_DA_XUE_FU_SHU_ZHONG_LIU_YI_YUAN")
            .build();

    // 验收 变 完成状态的单位
    private final Set<String> ACCEPT_FINISH_ORG_SET = SetBuilder.<String>custom()
            .build();

    // 验收审批 变 完成状态的单位
    private final Set<String> ACCEPT_APPROVAL_FINISH_ORG_SET = SetBuilder.<String>custom()
            .add("GUANG_DONG_YAO_KE_DA_XUE")
            .build();

    /**
     * 在该日期之前的单，都是南方医未对接财务的旧单
     */
    @PearlValue(key = "old.order.date.for.nan.fang.yi.ke", defaultValue = "2020-01-25 23:59:59")
    private String oldOrderDateForNanFangYiKe;

    /**
     * 需要对接中爆台账的组织id
     */
    @PearlValue(key = "order.standingBook.orgId", defaultValue = "[]")
    public List<Integer> standingBookOrgIdList;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private DangerousTagDOService dangerousTagDOServiceImpl;

    @Resource
    private OrderAcceptCommentClient orderAcceptCommentClient;

    @Resource
    private OrderContractMapper orderContractMapper;

    @Resource
    private OrderPicMapper orderPicMapper;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderEmailHandler orderEmailHandler;

    @Resource
    private TimeoutStatisticsService timeoutStatisticsService;

    @Resource
    private CreditServiceClient creditServiceClient;

    @Resource
    private WalletOrderRpcClient walletOrderRpcClient;

    @Resource
    private CooperationClient cooperationClient;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private ResearchBaseService researchBaseService;

    @Resource
    private BizBaseService bizBaseService;

    @Resource
    private BizWareHouseClient bizWareHouseClient;

    @Resource
    private OrderMasterForTPIService orderMasterForTPIService;

    @Resource
    private WmsRuleRpcServiceClient wmsRuleRpcServiceClient;

    @Resource
    private OrderStatementService orderStatementService;

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private DepartmentRpcClient departmentRpcClient;

    @Resource
    private OrderRemarkMapper orderRemarkMapper;

    @Resource
    private ProductDescriptionSnapshotMapper descSnapshotMapper;

    @Autowired
    private CategoryServiceClient categoryServiceClient;

    @Resource
    private DangerousTagDOMapper dangerousTagDOMapper;

    @Resource
    private HuaNongServiceClient huaNongServiceClient;

    @Resource
    private DockingExtraMapper dockingExtraMapper;

    @Resource
    private OrderUniqueBarCodeRPCClient orderUniqueBarCodeRPCClient;

    @Resource
    private DockingExtraService dockingExtraService;

    @Resource
    private CacheClient cacheClient;

    @Resource
    private ThirdPartOrderRPCClient thirdPartOrderRPCClient;

    @Resource
    private AcceptApprovalClient acceptApprovalClient;

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @Resource
    private ApplicationBaseClient applicationBaseClient;
    
    @Resource
    private InvoiceClient invoiceClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private OrderFundCardService orderFundCardService;

    @Resource
    private OrderAddressRPCClient orderAddressRPCClient;

    @Resource
    private OrderUploadFileService orderUploadFileService;

    @Resource
    private BizExitServiceClient bizExitServiceClient;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private WarehouseStockOccupyService warehouseStockOccupyService;

    @Resource
    private OrderUploadFileRpcClient orderUploadFileRpcClient;

    @Resource
    private OrderBankSnapshotClient orderBankSnapshotClient;

    @Resource
    private OrderContractService orderContractService;

    @Resource
    private OrderMessageHandler orderMessageHandler;

    @Override
    public OrderAcceptanceConfigVO getOrderAcceptanceConfig(Integer orderId) {
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMasterDO, "没有查询到该订单");
        Map<String, String> configMap = sysConfigClient.getConfigMapByOrgCodeAndConfigCode(orderMasterDO.getFusercode(), New.list(
                ConfigConstant.ORG_RECEIPT_PIC_CONFIG, ConfigCodeEnum.ORG_RECEIPT_PIC_AMOUNT_LIMIT.name()
        ));
        OrderAcceptPhotoConfigEnum orderAcceptPhotoConfigEnum = this.getIfNeedUploadAcceptancePhoto(configMap, orderMasterDO, orderDetailMapper.findByFmasterid(orderId));
        OrderAcceptanceConfigVO result =  new OrderAcceptanceConfigVO().setOrderId(orderId).setPhotoAcceptance(orderAcceptPhotoConfigEnum.getValue()).setUploadPrintFormStatus(OrderUploadStatusEnum.NO_NEED_TO_UPLOAD.getValue());

        if(OrgEnum.AN_HUI_SHENG_LI_YI_YUAN.getValue() == orderMasterDO.getFuserid()){
            List<RefFundcardOrderDO> bindCards = refFundcardOrderService.findRefundcardOrderByOrderId(orderId.toString());
            List<String> bindCardIds = bindCards.stream().map(RefFundcardOrderDO::getCardId).collect(toList());
            List<FundCardDTO> allLevelCards = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(orderMasterDO.getFusercode(), bindCardIds);
            Map<String, FundCardDTO> cardIdCardMap = FundCardUtils.getAllLevelCardIdCardMap(allLevelCards);
            List<OrderFundcardVO> fundcardVOList = New.listWithCapacity(bindCardIds.size());
            for(String bindCardId : bindCardIds){
                FundCardDTO card = cardIdCardMap.get(bindCardId);
                OrderFundcardVO fundcardVO = new OrderFundcardVO();
                fundcardVO.setOrderId(orderId).setCardId(card.getId()).setCardNo(card.getCode()).setCardName(card.getName());
                while (card.getLevel() > 1){
                    // 循环直到获取到一级经费卡
                    FundCardDTO parentCard = cardIdCardMap.get(card.getParentId());
                    if(parentCard == null || parentCard.getLevel().equals(card.getLevel())){
                        // 防止异常数据造成死循环
                        break;
                    }
                    card = parentCard;
                }
                if(CollectionUtils.isNotEmpty(card.getFundCardExtraDTOs())){
                    Map<String, String> cardExtraKeyValMap = DictionaryUtils.toMap(card.getFundCardExtraDTOs(), FundCardExtraDTO::getField, FundCardExtraDTO::getValue);
                    fundcardVO.setCampusName(cardExtraKeyValMap.get("compName"));
                    fundcardVO.setDeptCode(cardExtraKeyValMap.get("deptCode"));
                }
                fundcardVOList.add(fundcardVO);
            }
            result.setOrderFundcardVOList(fundcardVOList);
        }

        if(OrgEnum.NING_BO_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getValue() == orderMasterDO.getFuserid()){
            // 宁波一院，校验是否上传了单据
            OrderFileInfoRequestDTO request = new OrderFileInfoRequestDTO();
            request.setOrderIdList(New.list(orderId));
            request.setFileBusinessTypeList(New.list(FileBusinessTypeEnum.PURCHASE_FILE.getCode(), FileBusinessTypeEnum.DELIVERY_NOTE.getCode()));
            List<UploadFileInfoVO> uploadFileInfoList = orderUploadFileService.getUploadFileInfoList(request);
            // 校验是否已经上传了两种类型的文件，若没有则需要上传
            Set<Integer> uploadTypeSet = uploadFileInfoList.stream().map(UploadFileInfoVO::getFileBusinessType).collect(Collectors.toSet());
            result.setUploadPrintFormStatus(uploadTypeSet.size() == 2 ? OrderUploadStatusEnum.TRUE.getValue() : OrderUploadStatusEnum.FALSE.getValue());
        }
        return result;
    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public ReceiptOrderResponseDO userAcceptOrder(OrderReceiptParamDTO params) {
        Integer orderId = params.getOrderId();
        final String lockKey = OrderOperationConstant.ACCEPT_REDIS_CACHE_KEY + orderId;
        boolean getLock = false;
        try{
            // 1.获取当前订单验收的执行锁
            getLock = cacheClient.tryLock(lockKey, 10);
            BusinessErrUtil.isTrue(getLock, "当前订单已经提交验收！请稍后再试！");

            // 2.获取医院相关的 验收拍照配置、验收审批、验收入库配置、验收方式配置、合同配置、合同金额配置,线上单和线下单是否使用结算系统
            Map<String, String> receiptConfigMap = this.getReceiptConfigMap(params);

            // 3.检验验收条件并返回订单信息
            Pair<OrderMasterDO, List<OrderDetailDO>> orderInfo = this.checkedConditionAndReturnOrderInfo(params, receiptConfigMap, params.isManual());

            // 4.保存电子签名信息
            bizBaseService.saveAcceptElectronicSign(params, orderInfo.getKey());

            // 5.保存验收材料 图片,附件
            this.saveAcceptanceMaterials(params, orderInfo.getKey());

            // 6.透传入参，开始执行验收订单逻辑，开始链路处理
            ReceiptOrderResponseDO response = this.acceptCore(params, receiptConfigMap, orderInfo.getKey(), orderInfo.getValue());

            // 7.保存验收评价
            this.saveOrderCommentStrategy(params, orderInfo.getKey(), orderInfo.getValue());

            // 8.发送验收邮件
            this.sendReceiptEmailStrategy(orderInfo.getKey(), orderInfo.getValue());

            // 9.推送订单验收信息到对接平台
            this.pushReceiptInfoStrategy(params, orderInfo.getKey(), orderInfo.getValue(), receiptConfigMap);
            return response;
        }catch (Exception e){
            LOGGER.error("订单id为{}的订单验收失败", orderId, e);
            throw e;
        } finally {
            if (getLock) {
                // 如果获取到执行锁成功，则释放
                cacheClient.unlock(lockKey);
            }
        }
    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public ReceiptOrderResponseDO autoAcceptOrder(OrderReceiptParamDTO params, Map<String, String> configMap) {
        // 逻辑从目前的验收逻辑整合，缺失了几个环节。后面如果说自动验收有些步骤没有就是之前就没考虑到
        if(configMap == null){
            configMap = this.getReceiptConfigMap(params);
        }
        Pair<OrderMasterDO, List<OrderDetailDO>> orderInfo =  this.checkedConditionAndReturnOrderInfo(params, configMap, false);;
        ReceiptOrderResponseDO response = this.acceptCore(params, configMap, orderInfo.getKey(), orderInfo.getValue());
        this.saveOrderCommentStrategy(params, orderInfo.getKey(), orderInfo.getValue());
        return response;
    }

    @Override
    @ServiceLog(description = "自动验收", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void batchAutoAcceptAllOrder() {
        //获取订单配置
        Map<String, Map<String, BaseConfigDTO>> autoAcceptanceConfigMap = sysConfigClient.findByCodes(New.list(
                ConfigConstant.AUTO_ACCEPTANCE_CHCEK, ConfigConstant.AUTO_ACCEPTANCE_DAYS, ConfigConstant.ORG_RECEIPT_PIC_CONFIG,
                ConfigConstant.ORG_RECEIPT_STORE_CONFIG, ConfigConstant.CONFIG_CODE_PROCUREMENT_ACCEPTANCE, ConfigCodeEnum.ORDER_CONTRACT_UPLOAD_JSON.name(),
                ConfigConstant.ORG_ACCEPTANCE_APPROVAL_CONFIG, ConfigConstant.AUTO_SUBMIT_STATMENT,
                ConfigConstant.STATEMENT_SUBMITTED_MANNER, ConfigConstant.USE_WAREHOUSE_SYSTEM, ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM, ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM,
                ConfigConstant.WAREHOUSE_SYSTEM_VERSION_CODE
        ));
        //key--》orgCode value-->验收天数
        Map<String, Integer> autoMap = New.map();
        //key-->orgCode value-->{key-->configCode,v-->configValue}
        Map<String, Map<String, String>> orgReceiptConfigMap = New.map();
        // 全局默认配置，如果获取值为空，则默认获取此配置
        BaseConfigDTO defaultConfigDTO = new BaseConfigDTO();

        autoAcceptanceConfigMap.forEach((orgCode, configMap) -> {
            BaseConfigDTO checkConfigDTO = configMap.getOrDefault(ConfigConstant.AUTO_ACCEPTANCE_CHCEK, defaultConfigDTO);
            BaseConfigDTO daysConfigDTO = configMap.getOrDefault(ConfigConstant.AUTO_ACCEPTANCE_DAYS, defaultConfigDTO);
            BaseConfigDTO storeConfigDTO = configMap.getOrDefault(ConfigConstant.ORG_RECEIPT_STORE_CONFIG, defaultConfigDTO);
            BaseConfigDTO picConfigDTO = configMap.getOrDefault(ConfigConstant.ORG_RECEIPT_PIC_CONFIG, defaultConfigDTO);
            BaseConfigDTO procurementConfigDTO = configMap.getOrDefault(ConfigConstant.CONFIG_CODE_PROCUREMENT_ACCEPTANCE, defaultConfigDTO);
            BaseConfigDTO contractConfigDTO = configMap.getOrDefault(ConfigCodeEnum.ORDER_CONTRACT_UPLOAD_JSON.name(), defaultConfigDTO);
            BaseConfigDTO statementConfigDTO = configMap.getOrDefault(ConfigConstant.AUTO_SUBMIT_STATMENT, defaultConfigDTO);
            BaseConfigDTO acceptanceApprovalConfigDTO = configMap.getOrDefault(ConfigConstant.ORG_ACCEPTANCE_APPROVAL_CONFIG, defaultConfigDTO);
            BaseConfigDTO newStatementConfigDTO = configMap.getOrDefault(ConfigConstant.STATEMENT_SUBMITTED_MANNER, defaultConfigDTO);
            BaseConfigDTO useWarehouseConfigDTO = configMap.getOrDefault(ConfigConstant.USE_WAREHOUSE_SYSTEM, defaultConfigDTO);
            BaseConfigDTO onlineOrderConfigDTO = configMap.getOrDefault(ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM, defaultConfigDTO);
            BaseConfigDTO offlineOrderConfigDTO = configMap.getOrDefault(ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM, defaultConfigDTO);
            BaseConfigDTO wareHouseVersionConfigDTO = configMap.getOrDefault(ConfigConstant.WAREHOUSE_SYSTEM_VERSION_CODE, defaultConfigDTO);

            // 考虑到ConfigConstant中常量值Integer类型字段可能被依赖于其他服务，不考虑将Integer修改为String类型，而是采取类型转换
            String checkValue = StringUtils.defaultIfEmpty(checkConfigDTO.getConfigValue(), "0");
            int checkValueInt = IntegerUtils.parseInt(checkValue, 0);
            String daysValue = StringUtils.defaultIfEmpty(daysConfigDTO.getConfigValue(), "14");
            int daysValueInt = IntegerUtils.parseInt(daysValue, 14);
            String storeValue = StringUtils.defaultIfEmpty(storeConfigDTO.getConfigValue(), "0");
            String picValue = StringUtils.defaultIfEmpty(picConfigDTO.getConfigValue(), "0");
            String procurementValue = StringUtils.defaultIfEmpty(procurementConfigDTO.getConfigValue(), "0");
            String contractConfigJson = StringUtils.defaultIfEmpty(contractConfigDTO.getConfigValue(), StringUtils.EMPTY);
            String statementValue = StringUtils.defaultIfEmpty(statementConfigDTO.getConfigValue(), "0");
            String acceptanceApprovalValue = StringUtils.defaultIfEmpty(acceptanceApprovalConfigDTO.getConfigValue(), "0");
            String newStatementValue = StringUtils.defaultIfEmpty(newStatementConfigDTO.getConfigValue(), "1");
            String userWarehouseValue = StringUtils.defaultIfEmpty(useWarehouseConfigDTO.getConfigValue(), "0");
            String onlineOrderConfigValue = StringUtils.defaultIfEmpty(onlineOrderConfigDTO.getConfigValue(), "1");
            String offlineOrderConfigValue = StringUtils.defaultIfEmpty(offlineOrderConfigDTO.getConfigValue(), "1");
            String wareHouseVersionConfigValue= StringUtils.defaultIfEmpty(wareHouseVersionConfigDTO.getConfigValue(), "1");

            if (checkValueInt == ConfigConstant.AUTO_RECEIPT && daysValueInt > 0 && (ConfigConstant.NOT_NEED_PIC_VALUE.equals(picValue) || ConfigConstant.NOT_FORCE_PIC_VALUE.equals(picValue))) {
                //默认华农，中大，中肿不自动验收
                if (!NOT_AUTO_RECEIPT_ORG_SET.contains(orgCode)) {
                    autoMap.put(orgCode, daysValueInt);
                    Map<String, String> receiptConfigMap = New.mapWithCapacity(6);
                    receiptConfigMap.put(ConfigConstant.ORG_RECEIPT_PIC_CONFIG, picValue);
                    receiptConfigMap.put(ConfigConstant.ORG_RECEIPT_STORE_CONFIG, storeValue);
                    receiptConfigMap.put(ConfigConstant.CONFIG_CODE_PROCUREMENT_ACCEPTANCE, procurementValue);
                    receiptConfigMap.put(ConfigCodeEnum.ORDER_CONTRACT_UPLOAD_JSON.name(), contractConfigJson);
                    receiptConfigMap.put(ConfigConstant.AUTO_SUBMIT_STATMENT, statementValue);
                    receiptConfigMap.put(ConfigConstant.ORG_ACCEPTANCE_APPROVAL_CONFIG, acceptanceApprovalValue);
                    receiptConfigMap.put(ConfigConstant.STATEMENT_SUBMITTED_MANNER, newStatementValue);
                    receiptConfigMap.put(ConfigConstant.USE_WAREHOUSE_SYSTEM, userWarehouseValue);
                    receiptConfigMap.put(ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM, onlineOrderConfigValue);
                    receiptConfigMap.put(ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM, offlineOrderConfigValue);
                    receiptConfigMap.put(ConfigConstant.WAREHOUSE_SYSTEM_VERSION_CODE, wareHouseVersionConfigValue);
                    orgReceiptConfigMap.put(orgCode, receiptConfigMap);
                }

            }
        });
        LOGGER.info("自动验收部门的配置：{}", JsonUtils.toJson(autoMap));
        List<OrganizationDTO> allOrg = userClient.getAllOrg();
        Map<String, Integer> orgMap = allOrg.stream().collect(Collectors.toMap(OrganizationDTO::getCode, OrganizationDTO::getId, (oldValue, newValue) -> newValue));

        //遍历验收订单
        for (Map.Entry<String, Integer> entry : autoMap.entrySet()) {
            String orgCode = entry.getKey();
            Integer limitDay = entry.getValue();
            Integer orgId = orgMap.get(orgCode);
            Map<String, String> configMap = orgReceiptConfigMap.get(orgCode);
            List<OrderMasterDO> receiptTimeOutOrderList = orderMasterMapper.findReceiptTimeOut(orgId, OrderStatusEnum.WaitingForReceive.value, limitDay);
            // 需自动验收的订单
            List<OrderMasterDO> orderListToAutoReceive = this.customFilterAutoAccept(orgCode, receiptTimeOutOrderList);
            List<Integer> orderIdList = orderListToAutoReceive.stream().map(OrderMasterDO::getId).collect(toList());

            LOGGER.info("{}组织，自动验收数量{}", orgCode, JsonUtils.toJson(orderIdList));
            for (OrderMasterDO orderMasterDO : orderListToAutoReceive) {
                Integer orderId = orderMasterDO.getId();
                LOGGER.info("{}组织，订单id{} 自动验收start", orgCode, orderId);
                try {
                    OrderReceiptParamDTO acceptParams = this.getAutoAcceptParam(orderId, orderMasterDO, orgId, limitDay);
                    this.autoAcceptOrder(acceptParams, configMap);
                } catch (Exception e) {
                    Cat.logError(CAT_TYPE, "autoReceiptOrder", "自动验收", e);
                    LOGGER.error("自动验收异常", e);
                }
                LOGGER.info("{}组织，自动验收订单id{} 自动验收end", orgCode, orderId);
            }
        }
    }

    private List<OrderMasterDO> customFilterAutoAccept(String orgCode, List<OrderMasterDO> receiptTimeOutOrderList){
        List<OrderMasterDO> orderListToAutoReceive = receiptTimeOutOrderList;
        // 华农，只对旧单自动收货
        if(OrgEnum.HUA_NAN_NONG_YE_DA_XUE.getCode().equals(orgCode)){
            Date oldDate = DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, OrderDateConstant.HUA_NAN_NONG_YE_DA_XUE_OLD_DOCKING_TIME);
            // 先通过订单生成时间过滤,订单生成时间早于旧单日期的一定是旧单
            orderListToAutoReceive = receiptTimeOutOrderList.stream().filter(orderMasterDO -> orderMasterDO.getForderdate().before(oldDate)).collect(toList());
            // 可能是旧单的订单需要查采购单生成时间
            List<OrderMasterDO> perhapsOldOrderList = receiptTimeOutOrderList.stream().filter(orderMasterDO -> orderMasterDO.getForderdate().after(oldDate)).collect(toList());
            List<Integer> perhapsOldAppIdList = perhapsOldOrderList.stream().map(OrderMasterDO::getFtbuyappid).collect(toList());
            if(CollectionUtils.isNotEmpty(perhapsOldAppIdList)){
                // 再去查采购单生成时间
                List<List<Integer>> appIdListPartitions = Lists.partition(perhapsOldAppIdList, 200);
                List<Long> oldAppIdList = New.list();
                for(List<Integer> appIdList : appIdListPartitions){
                    List<ApplicationMasterDTO> applicationMasterDTOList = applicationBaseClient.findByMasterId(appIdList);
                    oldAppIdList.addAll(applicationMasterDTOList.stream().filter(applicationMasterDTO -> applicationMasterDTO.getCreateTime().before(oldDate)).map(ApplicationMasterDTO::getId).collect(toList()));
                }
                // 可能是旧单的订单过滤掉采购单时间不早于旧单日期的
                List<OrderMasterDO> oldOrderList = perhapsOldOrderList.stream().filter(orderMasterDO -> oldAppIdList.contains(orderMasterDO.getFtbuyappid().longValue())).collect(toList());
                orderListToAutoReceive.addAll(oldOrderList);
            }
        } else if(OrgEnum.GUANG_ZHOU_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode().equals(orgCode)
                || OrgEnum.GUANG_ZHOU_HU_XI_JIAN_KANG_YAN_JIU_YUAN.getCode().equals(orgCode)){
            // 广州医科大学附属第一医院/呼吸研究院，仅对线上单自动验收
            orderListToAutoReceive = orderListToAutoReceive.stream().filter(orderMasterDO -> ProcessSpeciesEnum.NORMAL.getValue().equals(orderMasterDO.getSpecies().intValue())).collect(toList());
        }
        // 过滤掉一物一码单，不触发自动验收
        List<Integer> orderIdList = receiptTimeOutOrderList.stream().map(OrderMasterDO::getId).collect(toList());
        List<BaseOrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(orderIdList, OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue());
        if(CollectionUtils.isNotEmpty(orderExtraDTOList)){
            Set<Integer> oneProductOneCodeOrderIdSet = orderExtraDTOList.stream().filter(extra->CommonValueUtils.parseNumberStrToBoolean(extra.getExtraValue())).map(BaseOrderExtraDTO::getOrderId).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(oneProductOneCodeOrderIdSet)){
                orderListToAutoReceive = orderListToAutoReceive.stream().filter(orderMasterDO -> !oneProductOneCodeOrderIdSet.contains(orderMasterDO.getId())).collect(toList());
            }
        }
        return orderListToAutoReceive;
    }

    /**
     * 获取订单验收相关OMS配置
     * @param params
     * @return
     */
    private Map<String, String> getReceiptConfigMap(OrderReceiptParamDTO params) {
        List<String> configCodeList = New.list(
                ConfigConstant.ORG_RECEIPT_PIC_CONFIG, ConfigConstant.ORG_ACCEPTANCE_APPROVAL_CONFIG, ConfigConstant.ORG_RECEIPT_STORE_CONFIG,
                ConfigConstant.CONFIG_CODE_PROCUREMENT_ACCEPTANCE, ConfigCodeEnum.ORDER_CONTRACT_UPLOAD_JSON.name(), ConfigConstant.AUTO_SUBMIT_STATMENT,
                ConfigConstant.STATEMENT_SUBMITTED_MANNER, ConfigConstant.USE_WAREHOUSE_SYSTEM,
                ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM, ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM, ConfigConstant.WAREHOUSE_SYSTEM_VERSION_CODE,
                ConfigCodeEnum.OFFLINE_ORDER_REQUIRE_FILL_IN_INVOICE.name(),
                ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM, ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM, ConfigConstant.WAREHOUSE_SYSTEM_VERSION_CODE,
                ConfigCodeEnum.OTHER_ORG_RECEIPT_ATTACHMENT.name(), ConfigCodeEnum.ORG_RECEIPT_PIC_AMOUNT_LIMIT.name(), ConfigConstant.ORDER_VIDEO_ATTACHMENT,
                ConfigCodeEnum.ORDER_ACCEPT_PHOTO_OPEN_PRODUCT_ASSOCIATION.name(), ConfigCodeEnum.ORDER_ACCEPT_ATTACHMENT_OPEN_PRODUCT_ASSOCIATION.name()
                
        );
        return sysConfigClient.getConfigMapByOrgCodeAndConfigCode(params.getOrgCode(), configCodeList);
    }

    /**
     * 校验 验收条件
     * @param orderId  订单id
     * @param orderMasterDO 订单信息
     * @param orgId
     * @return orderDetailList 返回订单详情列表
     */
    private OrderReceiptParamDTO getAutoAcceptParam(Integer orderId, OrderMasterDO orderMasterDO, Integer orgId, Integer limitDay) {
        OrderReceiptParamDTO params = new OrderReceiptParamDTO();
        params.setOrderId(orderId);
        params.setOrderSnapshot(OrderMasterTranslator.orderMasterDO2OrderMasterDTO(orderMasterDO));
        params.setOrgId(orgId);
        params.setUserId(systemOperatorId);
        params.setUserName(systemOperatorName);
        params.setAcceptReason("超过" + limitDay + "天未验收，系统自动验收");
        return params;
    }

    /**
     * 检查订单验收条件
     *
     * @param receiptConfigMap 验收配置
     * @param isManual         是否手动验收
     * @return 订单信息
     */
    @Override
    public Pair<OrderMasterDO, List<OrderDetailDO>> checkedConditionAndReturnOrderInfo(OrderReceiptParamDTO params, Map<String, String> receiptConfigMap, Boolean isManual) {
        final Integer orgId = params.getOrgId();
        final Integer userId = params.getUserId();
        final List<String> picUrlList = params.getPictureUrlList();
        final List<AttachmentDTO> attachmentList = params.getAttachmentList();
        final List<AttachmentDTO> orderVideoAttachmentList = params.getOrderVideoAttachmentList();
        final OrderMasterDTO orderMasterDTO = params.getOrderSnapshot();
        final List<AcceptAttachmentDTO> detailAttachmentList = params.getDetailAttachmentDTOList();
        final List<AcceptPictureDTO> detailPictureList = params.getDetailPictureDTOList();
        final List<AttachmentDTO> paymentRecordList = params.getPaymentRecordList();
        OrderMasterDO orderSnapshot = orderMasterDTO == null ? orderMasterMapper.selectByPrimaryKey(params.getOrderId()) : OrderMasterTranslator.dtoToOrderMasterDO(orderMasterDTO);
        BusinessErrUtil.notNull(orderSnapshot, ExecptionMessageEnum.ACCEPTANCE_FAILED_NO_ORDER);

        // 校验订单状态
        Integer orderStatus = orderSnapshot.getStatus();
        BusinessErrUtil.isTrue(OrderStatusEnum.WaitingForReceive.getValue().equals(orderStatus), ExecptionMessageEnum.STATUS_CHANGED_REFRESH_RETRY, orderSnapshot.getForderno());

        // 判断是否有退货商品
        List<OrderDetailDO> orderDetailList = orderDetailMapper.findByFmasterid(params.getOrderId());
        String goodNames = orderDetailList.stream().filter(OrderCommonUtils::havingReturn).map(OrderDetailDO::getFgoodname).distinct().collect(Collectors.joining("；"));
        BusinessErrUtil.isTrue(StringUtils.isEmpty(goodNames), ExecptionMessageEnum.RETURN_PENDING_GOODS, goodNames);

        //检查订单合同
        String orderContractConfig = receiptConfigMap.get(ConfigCodeEnum.ORDER_CONTRACT_UPLOAD_JSON.name());
        this.checkOrderContract(orderSnapshot, orderDetailList, orderContractConfig);

        this.customValidationDockingStatus(orderSnapshot);

        // 检查发票
        String offLineInvoiceConfig = receiptConfigMap.get(ConfigCodeEnum.OFFLINE_ORDER_REQUIRE_FILL_IN_INVOICE.name());
        this.customValidationInvoice(orderSnapshot, orderDetailList, offLineInvoiceConfig);

        this.customValidationAcceptanceDate(orgId);

        //  检查出库单是否审批通过
        customValidateWarehouseExitApprovalStatus(orderSnapshot);

        if (isManual) {
            // 校验用户权限
            this.validateUserAcceptAccess(params, orderSnapshot, orderDetailList, receiptConfigMap);
            // 温州 仁济，验收人工号不为空才可验收
            if (Objects.equals(OrgEnum.WEN_ZHOU_YI_KE_DA_XUE.getValue(), orgId) || Objects.equals(OrgEnum.WEN_YI_DA_REN_JI_XUE_YUAN.getValue(), orgId)) {
                UserBaseInfoDTO userInfo = userClient.getUserInfo(userId, orgId);
                BusinessErrUtil.notNull(userInfo.getJobnumber(), ExecptionMessageEnum.JOB_NUMBER_REQUIRED_FOR_ACCEPTANCE);
            }
            // 单位特殊定制校验验收人
            customValidationAcceptor(params, orderSnapshot, orderDetailList);

            //特殊验收图片校验
            if (customValidationPicture(picUrlList, orderSnapshot, orderDetailList)) {
                return new Pair<>(orderSnapshot, orderDetailList);
            }

            //拍照验收校验
            this.validateAcceptPicUpload(receiptConfigMap, orderSnapshot, orderDetailList, picUrlList);

            // 校验 订单详情图片关联配置
            validateDetailPicUpload(receiptConfigMap, orderDetailList, detailPictureList);

            //附件上传规则
            this.validationAttachment(receiptConfigMap, attachmentList, orderVideoAttachmentList, orderDetailList);

            // 校验 订单详情附件关联配置
            validateDetailAttachmentUpload(receiptConfigMap, orderDetailList, detailAttachmentList);

            // 校验 付款记录
            this.validatePaymentRecord(paymentRecordList, orderSnapshot);

            if(ProcessSpeciesEnum.OFFLINE.getValue() == orderSnapshot.getSpecies().intValue()){
                // 线下单，如果开启了批次填写，需要先补充批次信息才能收货
                List<BaseOrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(New.list(orderSnapshot.getId()), OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue());
                if(CollectionUtils.isNotEmpty(orderExtraDTOList) && CommonValueUtils.parseNumberStrToBoolean(orderExtraDTOList.get(0).getExtraValue())){
                    List<OrderUniqueBarCodeDTO> orderUniqueBarCodeDTOList = orderUniqueBarCodeRPCClient.findByOrderNo(orderSnapshot.getForderno(), null);
                    BusinessErrUtil.isTrue(orderUniqueBarCodeDTOList.stream().allMatch(item -> OrderProductBatchesStatusEnum.INPUTTED.getCode() == item.getBatchesStatus()), ExecptionMessageEnum.ENTER_BATCH_INFO_BEFORE_RECEIVING);
                }
            }
        }

        // todo 华农的验收代码，华农二期正式上线后删除
        // 由于旧订单的存在，需保留代码，旧订单走旧流程
        if (OrgConst.HUA_NAN_NONG_YE_DA_XUE.equals(orderSnapshot.getFusercode())) {
            // 华南农业大学竞价订单推送订单与合同信息
            if (OrderTypeEnum.BID_ORDER.getCode().equals(orderSnapshot.getOrderType())) {
                this.sendOrderInfo(orderSnapshot);
            }
        }

        return new Pair<>(orderSnapshot, orderDetailList);
    }

    /**
     * 校验用户验收权限
     */
    private void validateUserAcceptAccess(OrderReceiptParamDTO params, OrderMasterDO orderSnapshot, List<OrderDetailDO> orderDetailList, Map<String, String> receiptConfigMap) {
        Integer orgId = params.getOrgId();
        Integer userId = params.getUserId();
        if (OrgEnum.GUANG_ZHOU_FU_DA_ZHONG_LIU_YI_YUAN.getValue() == orderSnapshot.getFuserid()
                && OrderTypeEnum.BID_ORDER.getCode().equals(orderSnapshot.getOrderType())) {
            // 广州复大医疗有限公司复大肿瘤医院，不是采购单，则不读取验收方式和验收权限，采购人仅可验收自己的订单
            BusinessErrUtil.isTrue(orderSnapshot.getFbuyerid().equals(userId), ExecptionMessageEnum.NO_ACCEPTANCE_AUTHORITY);
            return;
        }

        // 是否有服务/非服务类商品
        boolean withService = false;
        boolean withNonService = false;
        for (OrderDetailDO detail : orderDetailList) {
            if (Objects.equals(CategoryConstant.SCIENCE_SERVICE_ID, detail.getFirstCategoryId())) {
                withService = true;
            } else {
                withNonService = true;
            }
        }

        // 中大五院定制， 科研服务类订单，无视权限，谁都能验收
        if (Objects.equals(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN.getValue(), orgId) && withService) {
            return;
        }

        // 判断订单是否包含 管制/非管制 危化品
        boolean withRegulatoryDangerous = false;
        boolean withNonRegulatoryDangerous = false;
        for (OrderDetailDO detail : orderDetailList) {
            if (Objects.equals(CategoryConstant.DANGEROUS_ID, detail.getFirstCategoryId())) {
                if (Objects.equals(RegulatoryTypeEnum.REGULATORY.getRegulatoryType(), detail.getRegulatoryTypeId())) {
                    withRegulatoryDangerous = true;
                } else if (Objects.equals(RegulatoryTypeEnum.UNREGULATORY.getRegulatoryType(), detail.getRegulatoryTypeId())) {
                    withNonRegulatoryDangerous = true;
                }
            }
        }

        // 权限获取
        Map<String, List<DepartmentDTO>> codeAuthDeptIdMap = userClient.findUserHasAccessDepartmentBatch(userId, orgId, New.list(ConfigConstant.ORDER_ACCEPTANCE,
                BuyerCenterAccessConstant.SERVICE_ORDER_ACCEPTANCE,
                BuyerCenterAccessConstant.NON_SERVICE_ORDER_ACCEPTANCE,
                ConfigConstant.REGULATORY_ORDER_ACCEPTANCE,
                ConfigConstant.NON_REGULATORY_ORDER_ACCEPTANCE));

        boolean withAccess = false;

        // 订单验收权限 -可以验收所有类型的订单
        if (CollectionUtils.isNotEmpty(codeAuthDeptIdMap.get(ConfigConstant.ORDER_ACCEPTANCE))) {
            withAccess = this.checkIfWithAccessDept(codeAuthDeptIdMap.get(ConfigConstant.ORDER_ACCEPTANCE), orderSnapshot.getFbuydepartmentid());
        }
        // 服务类订单验收权限
        if (withService && CollectionUtils.isNotEmpty(codeAuthDeptIdMap.get(BuyerCenterAccessConstant.SERVICE_ORDER_ACCEPTANCE))) {
            withAccess = withAccess || this.checkIfWithAccessDept(codeAuthDeptIdMap.get(BuyerCenterAccessConstant.SERVICE_ORDER_ACCEPTANCE), orderSnapshot.getFbuydepartmentid());
        }
        // 非服务类订单验收权限
        if (withNonService && CollectionUtils.isNotEmpty(codeAuthDeptIdMap.get(BuyerCenterAccessConstant.NON_SERVICE_ORDER_ACCEPTANCE))) {
            withAccess = withAccess || this.checkIfWithAccessDept(codeAuthDeptIdMap.get(BuyerCenterAccessConstant.NON_SERVICE_ORDER_ACCEPTANCE), orderSnapshot.getFbuydepartmentid());
        }
        // 管制类危化品订单验收权限
        if (withRegulatoryDangerous && CollectionUtils.isNotEmpty(codeAuthDeptIdMap.get(ConfigConstant.REGULATORY_ORDER_ACCEPTANCE))) {
            withAccess = withAccess || this.checkIfWithAccessDept(codeAuthDeptIdMap.get(ConfigConstant.REGULATORY_ORDER_ACCEPTANCE), orderSnapshot.getFbuydepartmentid());
        }
        // 非管制类危化品订单验收权限
        if (withNonRegulatoryDangerous && CollectionUtils.isNotEmpty(codeAuthDeptIdMap.get(ConfigConstant.NON_REGULATORY_ORDER_ACCEPTANCE))) {
            withAccess = withAccess || this.checkIfWithAccessDept(codeAuthDeptIdMap.get(ConfigConstant.NON_REGULATORY_ORDER_ACCEPTANCE), orderSnapshot.getFbuydepartmentid());
        }
        BusinessErrUtil.isTrue(withAccess, ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);

        //验收方式校验
        String acceptanceConfig = receiptConfigMap.get(ConfigConstant.CONFIG_CODE_PROCUREMENT_ACCEPTANCE);
        BusinessErrUtil.isTrue(!ConfigConstant.CROSS_ACCEPTANCE_VALUE.toString().equals(acceptanceConfig)
                || !orderSnapshot.getFbuyerid().equals(userId), ExecptionMessageEnum.ORDER_NEEDS_TEAM_ACCEPTANCE, orderSnapshot.getForderno());
    }

    private boolean checkIfWithAccessDept(List<DepartmentDTO> deptList, Integer orderDeptId){
        if (CollectionUtils.isNotEmpty(deptList)) {
            for (DepartmentDTO dept : deptList) {
                if(dept.getId().equals(orderDeptId)){
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 校验付款记录
     *
     * @param paymentRecordList 付款记录列表
     * @param orderSnapshot     订单快照
     */
    private void validatePaymentRecord(List<AttachmentDTO> paymentRecordList, OrderMasterDO orderSnapshot) {
        // 西南医科大定制 ，线下单使用个人账户 需上传付款记录
        if (Objects.equals(OrgEnum.XI_NAN_YI_KE_DA_XUE.getValue(), orderSnapshot.getFuserid())
                && Objects.equals(ProcessSpeciesEnum.OFFLINE.getValue(), orderSnapshot.getSpecies().intValue())) {
            List<OrderBankDataDTO> orderBankDataDTOS = orderBankSnapshotClient.listByOrderId(New.list(orderSnapshot.getId()));
            if (CollectionUtils.isEmpty(orderBankDataDTOS)) {
                return;
            }
            Integer accountType = orderBankDataDTOS.get(0).getAccountType();
            if (Objects.equals(OfflineAccountTypeEnum.INDIVIDUAL_ACCOUNT.getType(), accountType)) {
                // 校验是否上传了付款记录
                BusinessErrUtil.notEmpty(paymentRecordList, ExecptionMessageEnum.PLEASE_UPLOAD_PAYMENT_RECORDS_FIRST);
                // 校验付款记录数量限制
                BusinessErrUtil.isTrue(CollectionUtils.size(paymentRecordList) <= OrderAcceptConstant.UPLOAD_RECEIVE_PAYMENT_RECORD,
                        ExecptionMessageEnum.MAX_PAYMENT_RECORDS_UPLOAD,
                        OrderAcceptConstant.UPLOAD_RECEIVE_PAYMENT_RECORD);
            }
        }
    }

    /**
     * 判断是否验收图片必传
     * @param receiptConfigMap 配置
     * @param orderSnapshot 订单快照
     * @param orderDetailList 订单详情快照
     * @return 是否必传图片
     */
    private OrderAcceptPhotoConfigEnum getIfNeedUploadAcceptancePhoto(Map<String, String> receiptConfigMap, OrderMasterDO orderSnapshot, List<OrderDetailDO> orderDetailList){
        // 1.根据配置，判断是否必传图片，必传则计算比对金额
        BigDecimal validateAmount;
        switch (receiptConfigMap.get(ConfigConstant.ORG_RECEIPT_PIC_CONFIG)){
            case ConfigConstant.NOT_NEED_PIC_VALUE:
                // 无需上传
                return OrderAcceptPhotoConfigEnum.NO_NEED;
            case ConfigConstant.NEED_PIC_VALUE:
                // 强制上传，计算校验金额
                validateAmount = orderSnapshot.getForderamounttotal().subtract(BigDecimal.valueOf(orderSnapshot.getReturnAmount()));
                break;
            case ConfigConstant.UNLESS_SERVICE_NEED_PIC_VALUE:
                // 除服务类商品外需要强制拍照验收（含未退完货的商品的情况下）
                List<OrderDetailDO> notServiceCategoryProducts = orderDetailList.stream()
                        .filter(d -> d.getFquantity().compareTo(d.getFcancelquantity()) != 0)
                        .filter(d -> !InboundTypeEnum.descOf(d.getCategoryTag()).getValue().equals(InboundTypeEnum.SERVICE.getValue()))
                        .collect(toList());
                if(CollectionUtils.isEmpty(notServiceCategoryProducts)){
                    return OrderAcceptPhotoConfigEnum.SELECTIVE;
                }
                validateAmount = notServiceCategoryProducts.stream().map(d->d.getFbidamount().subtract(BigDecimal.valueOf(d.getReturnAmount()))).reduce(BigDecimal.ZERO, BigDecimal::add);
                break;
            case ConfigConstant.NOT_FORCE_PIC_VALUE:
                // 非强制上传
                return OrderAcceptPhotoConfigEnum.SELECTIVE;
            default:
                // 其他的 无强制要求
                return OrderAcceptPhotoConfigEnum.NO_NEED;
        }
        // 2.金额比对，如果超过阈值，则必传，否则选传
        BigDecimal needPictureAmountThreshold;
        try{
            needPictureAmountThreshold = new BigDecimal(receiptConfigMap.getOrDefault(ConfigCodeEnum.ORG_RECEIPT_PIC_AMOUNT_LIMIT.name(), "0"));
        } catch (NumberFormatException e){
            needPictureAmountThreshold = BigDecimal.ZERO;
        }
        if(validateAmount.compareTo(needPictureAmountThreshold) < 0){
            return OrderAcceptPhotoConfigEnum.SELECTIVE;
        }
        return OrderAcceptPhotoConfigEnum.MUST;
    }

    /**
     * 校验验收图片上传
     * @param receiptConfigMap 验收配置
     * @param orderSnapshot 订单快照
     * @param orderDetailList 订单商品快照
     * @param picUrlList 订单上传图片
     */
    @Override
    public void validateAcceptPicUpload(Map<String, String> receiptConfigMap, OrderMasterDO orderSnapshot, List<OrderDetailDO> orderDetailList, List<String> picUrlList) {
        // 1.校验上传图片数量限制
        Set<Integer> specialOrgSet = New.set(OrgEnum.HANG_ZHOU_YI_XUE_YUAN.getValue(), OrgEnum.CHNEG_DU_YI_XUE_YUAN.getValue());
        int uploadThreshold = specialOrgSet.contains(orderSnapshot.getFuserid()) ? OrderAcceptConstant.UPLOAD_RECEIVE_PIC_THRESHOLD : OrderAcceptConstant.UPLOAD_RECEIVE_PIC_THRESHOLD_WHEN_RECEIVING;
        String errMsg = LocaleUtils.translate(ExecptionMessageEnum.UPLOAD_IMAGES_BEFORE_SUBMIT, uploadThreshold);
        if (CollectionUtils.isNotEmpty(picUrlList)) {
            BusinessErrUtil.isTrue(picUrlList.size() <= uploadThreshold, errMsg);
        }
        // 2.根据是否必须上传验收图片，做图片必传校验
        OrderAcceptPhotoConfigEnum orderAcceptPhotoConfigEnum = this.getIfNeedUploadAcceptancePhoto(receiptConfigMap, orderSnapshot, orderDetailList);
        if(orderAcceptPhotoConfigEnum != OrderAcceptPhotoConfigEnum.MUST){
            return;
        }
        if(ConfigConstant.UNLESS_SERVICE_NEED_PIC_VALUE.equals(receiptConfigMap.get(ConfigConstant.ORG_RECEIPT_PIC_CONFIG))){
            errMsg = LocaleUtils.translate(ExecptionMessageEnum.NON_SERVICE_GOODS_PHOTO_REQUIRED);
        }
        BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(picUrlList), errMsg);

    }

    /**
     * 校验订单详情图片关联配置
     *
     * @param receiptConfigMap  配置
     * @param detailPictureList 订单详情图片关联信息
     */
    @Override
    public void validateDetailPicUpload(Map<String, String> receiptConfigMap, List<OrderDetailDO> orderDetailList, List<AcceptPictureDTO> detailPictureList) {

        // 验收图片配置为 无需上传，则不校验商品关联
        String receiptPicConfig = receiptConfigMap.get(ConfigConstant.ORG_RECEIPT_PIC_CONFIG);
        if (ConfigConstant.NOT_NEED_PIC_VALUE.equals(receiptPicConfig)) {
            return;
        }

        String detailAcceptPicConfig = receiptConfigMap.get(ConfigConstant.ORDER_ACCEPT_PHOTO_OPEN_PRODUCT_ASSOCIATION);
        switch (detailAcceptPicConfig) {
            // 无需选择
            case ConfigConstant.ORDER_ACCEPT_PHOTO_OPEN_PRODUCT_ASSOCIATION_NONE:
                return;
            // 不必跟全部商品关联
            case ConfigConstant.ORDER_ACCEPT_PHOTO_OPEN_PRODUCT_ASSOCIATION_PART:
                return;
            // 必须跟全部商品关联
            case ConfigConstant.ORDER_ACCEPT_PHOTO_OPEN_PRODUCT_ASSOCIATION_ALL:

                // 图片列表为空，则所有详情都未关联
                if (CollectionUtils.isEmpty(detailPictureList)) {
                    String unassociatedProductName = orderDetailList.stream()
                            .map(detailDO -> "【" + detailDO.getFgoodname() + "】")
                            .collect(Collectors.joining("，"));
                    BusinessErrUtil.isTrue(false, ExecptionMessageEnum.GOODS_NO_ACCEPTANCE_PHOTO, unassociatedProductName);
                    return;
                }

                // 获取已被关联的订单详情ID
                Set<Integer> associatedOrderDetailIds = detailPictureList.stream()
                        .filter(pic -> CollectionUtils.isNotEmpty(pic.getOrderDetailIdList()))
                        .flatMap(pic -> pic.getOrderDetailIdList().stream())
                        .collect(Collectors.toSet());

                // 未被关联的订单详情
                List<OrderDetailDO> unassociatedOrderDetailList = orderDetailList.stream()
                        .filter(detailDO -> !associatedOrderDetailIds.contains(detailDO.getId()))
                        .collect(Collectors.toList());

                // 打印没有关联的订单详情商品
                if (CollectionUtils.isNotEmpty(unassociatedOrderDetailList)) {
                    String unassociatedProductName = unassociatedOrderDetailList.stream()
                            .map(detailDO -> "【" + detailDO.getFgoodname() + "】")
                            .collect(Collectors.joining("，"));
                    BusinessErrUtil.isTrue(false, ExecptionMessageEnum.GOODS_NO_ACCEPTANCE_PHOTO, unassociatedProductName);
                }
                return;
            default:
                // 默认无需选择
                return;

        }
    }

    /**
     * 校验订单详情附件关联配置
     *
     * @param receiptConfigMap     配置
     * @param detailAttachmentList 订单详情附件关联信息
     */
    @Override
    public void validateDetailAttachmentUpload(Map<String, String> receiptConfigMap, List<OrderDetailDO> orderDetailList, List<AcceptAttachmentDTO> detailAttachmentList) {

        // 验收附件配置为 无需上传，则不校验商品关联
        String receiptPicConfig = receiptConfigMap.get(ConfigConstant.OTHER_ORG_RECEIPT_ATTACHMENT);
        if (ConfigConstant.NOT_NEED_ACCEPT_ATTACHMENT_VALUE.equals(receiptPicConfig)) {
            return;
        }

        String detailAcceptPicConfig = receiptConfigMap.get(ConfigConstant.ORDER_ACCEPT_ATTACHMENT_OPEN_PRODUCT_ASSOCIATION);
        switch (detailAcceptPicConfig) {
            // 无需选择
            case ConfigConstant.ORDER_ACCEPT_ATTACHMENT_OPEN_PRODUCT_ASSOCIATION_NONE:
                return;
            // 不必跟全部商品关联
            case ConfigConstant.ORDER_ACCEPT_ATTACHMENT_OPEN_PRODUCT_ASSOCIATION_PART:
                return;
            // 必须跟全部商品关联
            case ConfigConstant.ORDER_ACCEPT_ATTACHMENT_OPEN_PRODUCT_ASSOCIATION_ALL:

                // 附件列表为空，则所有详情都未关联
                if (CollectionUtils.isEmpty(detailAttachmentList)) {
                    String unassociatedProductName = orderDetailList.stream()
                            .map(detailDO -> "【" + detailDO.getFgoodname() + "】")
                            .collect(Collectors.joining("，"));
                    BusinessErrUtil.isTrue(false, ExecptionMessageEnum.GOODS_NO_ACCEPTANCE_ATTACHMENT, unassociatedProductName);
                    return;
                }

                // 获取已被关联的订单详情ID集合
                Set<Integer> associatedOrderDetailIds = detailAttachmentList.stream()
                        .filter(attachment -> CollectionUtils.isNotEmpty(attachment.getOrderDetailIdList()))
                        .flatMap(attachment -> attachment.getOrderDetailIdList().stream())
                        .collect(Collectors.toSet());

                // 未被关联的订单详情集合
                List<OrderDetailDO> unassociatedOrderDetailList = orderDetailList.stream()
                        .filter(detailDO -> !associatedOrderDetailIds.contains(detailDO.getId()))
                        .collect(Collectors.toList());

                // 打印没有关联的订单详情商品
                if (CollectionUtils.isNotEmpty(unassociatedOrderDetailList)) {
                    String unassociatedProductName = unassociatedOrderDetailList.stream()
                            .map(detailDO -> "【" + detailDO.getFgoodname() + "】")
                            .collect(Collectors.joining("，"));
                    BusinessErrUtil.isTrue(false, ExecptionMessageEnum.GOODS_NO_ACCEPTANCE_ATTACHMENT, unassociatedProductName);
                }
                return;
            default:
                // 默认无需选择
                return;
        }
    }

    /**
     * 校验接收人
     *
     * @param params          验收参数
     * @param orderSnapshot   订单数据
     * @param orderDetailList 订单商品详情数据
     */
    private void customValidationAcceptor(OrderReceiptParamDTO params, OrderMasterDO orderSnapshot, List<OrderDetailDO> orderDetailList) {
        String orgCode = orderSnapshot.getFusercode();
        if (OrgEnum.ZHONG_SHAN_SHI_REN_MIN_YI_YUAN.getCode().equals(orgCode)) {
            final String secondReceiverName = params.getSecondReceiverName();
            BusinessErrUtil.notNull(secondReceiverName, ExecptionMessageEnum.FILL_INSPECTOR_NAME);
            BusinessErrUtil.isTrue(!secondReceiverName.equals(orderSnapshot.getFbuyername()), ExecptionMessageEnum.FILL_INSPECTOR_NAME);
        }
        this.customValidationAcceptorAccount(params, orderSnapshot, orderDetailList);
    }

    /**
     * 校验接收人账号是否符合单位要求
     *
     * @param params          验收参数
     * @param orderSnapshot   订单数据
     * @param orderDetailList 订单商品详情数据
     */
    private void customValidationAcceptorAccount(OrderReceiptParamDTO params, OrderMasterDO orderSnapshot, List<OrderDetailDO> orderDetailList) {
        // 校验是否接收账户，不是则抛出错误提示
        RjSessionInfo sessionInfo = RpcContext.getProviderContext().getCallAttachment(GatewayConstant.SESSION_INFO);
        String curUserGuid;
        // 非网关进来的会话信息
        if (sessionInfo == null) {
            UserBaseInfoDTO userInfo = userClient.getUserInfo(params.getUserId(), params.getOrgId());
            curUserGuid = userInfo.getGuid();
        } else {
            curUserGuid = sessionInfo.getGuid();
        }
        
        String orgCode = orderSnapshot.getFusercode();
        List<String> guidListCanAccept;

        // 是否含管制品
        boolean containsRegulatoryProduct = orderDetailList.stream().anyMatch(detail -> RegulatoryTypeEnum.REGULATORY.getRegulatoryType().equals(detail.getRegulatoryTypeId()));
        if (containsRegulatoryProduct) {
            guidListCanAccept = CustomAcceptorConstant.getRegulatoryAcceptorUserGuid(orgCode);
            // 为null则没配置，不用校验。如果不为null则需要校验
            BusinessErrUtil.isTrue(guidListCanAccept == null || guidListCanAccept.contains(curUserGuid), ExecptionMessageEnum.UNAUTHORIZED_PIPE_PRODUCT_ACCEPTANCE);
        }
        // 江西肿瘤定制，院内地址标签且不为动物类/服务类的，需要校验验收人
        boolean containsExpAnimalOrService = orderDetailList.stream().anyMatch(detail -> CategoryConstant.EXPERIMENT_ANIMAL_ID == detail.getFirstCategoryId()
                || CategoryConstant.SCIENCE_SERVICE_ID.equals(detail.getFirstCategoryId()));
        if(!containsExpAnimalOrService && OrgEnum.JIANG_XI_ZHONG_LIU.getCode().equals(orgCode)){
            OrderAddressDTO orderAddressDTO = orderAddressRPCClient.findByOrderId(orderSnapshot.getId());
            if("院内实验用".equals(orderAddressDTO.getLabel())){
                BusinessErrUtil.isTrue(CustomAcceptorConstant.JIANG_XI_ZHONG_LIU_EXP_ANIMAL_OR_SERVICE_CUSTOM_ACCEPT_MAN_ID.contains(sessionInfo.getUserId(RjUserTypeEnum.STORE_USER).intValue()), ExecptionMessageEnum.INSPECTION_REQUIREMENTS_UNIT);
            }
        }
    }

    /**
     * 管理平台状态同步校验
     * @param orderMasterDO
     */
    private void customValidationDockingStatus(OrderMasterDO orderMasterDO) {
        if (OrgEnum.GUANG_XI_ZHONG_LIU.getValue() == orderMasterDO.getFuserid() && !OrderDateConstant.isOldOrderForNormal(OrgEnum.GUANG_XI_ZHONG_LIU.getCode(), orderMasterDO.getForderdate())) {
            dockingExtraService.customValidationDockingStatus(orderMasterDO.getForderno());
        }
    }

    /**
     * 校验发票
     * @param orderMasterDO 订单信息
     * @param orderDetailDOList 订单商品信息
     */
    private void customValidationInvoice(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList, String offLineInvoiceConfig) {
        boolean needInvoice = false;
        String noInvoiceHint = StringUtils.EMPTY;

        // 根据OMS配置检查线下单是否必填发票, 1不填，2.非必填，3必填
        String checkInvoiceConfig = "3";
        if (ProcessSpeciesEnum.OFFLINE.getValue() == orderMasterDO.getSpecies().intValue() && checkInvoiceConfig.equals(offLineInvoiceConfig)) {
            needInvoice = true;
            noInvoiceHint = LocaleUtils.translate(ExecptionMessageEnum.INVOICE_INFO_NOT_FILLED);
        }
        if(OrgEnum.GUANG_ZHOU_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode().equals(orderMasterDO.getFusercode())
            || OrgEnum.GUANG_ZHOU_HU_XI_JIAN_KANG_YAN_JIU_YUAN.getCode().equals(orderMasterDO.getFusercode())){
            BigDecimal instrumentPriceLimit = BigDecimal.valueOf(1500);
            needInvoice = orderDetailDOList.stream().allMatch(orderDetailDO -> CategoryConstant.INSTRUMENT_ID == orderDetailDO.getFirstCategoryId() && orderDetailDO.getFbidprice().compareTo(instrumentPriceLimit) > 0);
            needInvoice = needInvoice && ProcessSpeciesEnum.OFFLINE.getValue() == orderMasterDO.getSpecies().intValue();
            noInvoiceHint = LocaleUtils.translate(ExecptionMessageEnum.INVOICE_REQUIRED_FOR_INSTRUMENTS);
        }
        if(needInvoice){
            List<InvoiceDTO> invoiceDTOList = invoiceClient.findInvoiceList(New.list(orderMasterDO.getId()), orderMasterDO.getFuserid());
            BusinessErrUtil.notEmpty(invoiceDTOList, noInvoiceHint);
        }
    }

    /**
     * 校验验收日期
     * @param orgId 单位
     */
    private void customValidationAcceptanceDate(Integer orgId){
        if(OrgEnum.DONG_GUAN_SHI_REN_MIN_YI_YUAN.getValue() == orgId){
            // 东莞人医，每个月最后一天不能验收
            LocalDateTime nowDate = LocalDateTime.now();
            LocalDateTime deadline = LocalDateTime.of(nowDate.getYear(), nowDate.getMonth(), 1, 0 , 0)
                    .plus(1, ChronoUnit.MONTHS)
                    .minus(1, ChronoUnit.DAYS);
            BusinessErrUtil.isTrue(deadline.isAfter(nowDate), ExecptionMessageEnum.END_OF_MONTH_CONSUMABLES_LIMIT);
        }
    }

    /**
     * 拍照验收定制化处理,返回true时跳过后续校验
     * @param picUrlList        验收图片
     * @param orderMasterDO     订单master
     * @param orderDetailList   订单detail
     * @return
     */
    @Override
    public boolean customValidationPicture(List<String> picUrlList, OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList) {
        Integer orgId = orderMasterDO.getFuserid();
        //中肿 拍照验收比较特殊
        if (OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_ZHONG_LIU_YI_YUAN.getValue() == orgId) {
            //超过等于 1万元 （服务类订单除外），强制上传收货照片
            boolean overspend = orderMasterDO.getForderamounttotal().compareTo(new BigDecimal(10000)) >= 0;
            //一级分类：实验服务，全部不需拍照验收
            //二级分类：动物实验服务，除模式动物定制之外 不需要拍照
            boolean isServiceType = orderDetailList.stream().allMatch(this::isNoPhotoAcceptance);
            boolean photoNotEmpty = CollectionUtils.isNotEmpty(picUrlList);

            if (!isServiceType && overspend) {
                BusinessErrUtil.isTrue(photoNotEmpty, ExecptionMessageEnum.ORDER_AMOUNT_UPLOAD_PICTURE);
            }
            return true;
        }
        if(OrgEnum.SHEN_ZHEN_WAN_SHI_YAN_SHI.getValue() == orgId){
            // 深圳湾，2000元以上订单要上传验收图片
            BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(picUrlList)
                    || orderMasterDO.getForderamounttotal().intValue() < 2000, ExecptionMessageEnum.ORDER_AMOUNT_UPLOAD_RECEIPT_PHOTO);

            // 深圳湾,订单中商品存在剧毒化学品(id:506)、易制毒化学品(id:488)、易制爆化学品(id:497)、实验气体(id:476)任意一项时，验收时必须强制拍照验收
            List<Integer> categoryIdList = New.list(506,488,497,476);
            List<OrderDetailDO> list = orderDetailList
                    .stream()
                    .filter(orderDetailDO -> categoryIdList.contains(orderDetailDO.getFirstCategoryId()) ||
                            categoryIdList.contains(orderDetailDO.getSecondCategoryId()))
                    .collect(toList());
            BusinessErrUtil.isTrue(CollectionUtils.isEmpty(list) || CollectionUtils.isNotEmpty(picUrlList), ExecptionMessageEnum.HAZARDOUS_MATERIALS_PHOTO_REQUIRED);
            return true;
        }

        if(OrgEnum.JIANG_SHU_SHENG_ZHONG_XI_YI_JIE_HE_YI_YUAN.getValue() == orgId){
            // 江苏省中西医结合医院:当订单存在商品一级分类为危险化学品（id：460）时，验收时无需限制是否已上传图片
            return orderDetailList.stream().anyMatch(detail->CategoryConstant.REGULATORY_DANGEROUS_SECOND_LEVEL_ID.contains(detail.getSecondCategoryId()));
        }
        
        return false;
    }

    /**
     * 校验出库单是否审批通过
     *
     * @param orderSnapshot 订单快照
     */
    private void customValidateWarehouseExitApprovalStatus(OrderMasterDO orderSnapshot) {
        // 现货仓订单需校验出库单是否审批通过
        Map<Integer, String> orderExtraMap = getOrderExtraMap(orderSnapshot.getId());
        String spotWareHouseFlag = orderExtraMap.getOrDefault(OrderExtraEnum.STOCK_WAREHOUSE_TYPE.getValue(), "0");
        String spotWareHouseValue = "1";
        if (spotWareHouseValue.equals(spotWareHouseFlag)) {
            // 出库单已审批通过状态
            Integer warehouseExitApprovalStatus = 1;
            List<BizWarehouseExitDTO> warehouseExitDTOS = bizExitServiceClient.queryExitByOrderNo(orderSnapshot.getForderno());
            boolean checkWarehouseExit = warehouseExitDTOS.stream().allMatch(warehouseExitDTO -> warehouseExitApprovalStatus.equals(warehouseExitDTO.getStatus()));
            BusinessErrUtil.isTrue(checkWarehouseExit, ExecptionMessageEnum.SPOT_WAREHOUSE_DELIVERY_NOT_APPROVED);
        }
    }


    /**
     * 收货发送评价信息
     * @param params            入参
     * @param orderMasterDO     订单
     * @param orderDetailList   订单详情
     */
    private void saveOrderCommentStrategy(OrderReceiptParamDTO params, OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList) {
        final Integer orgId = params.getOrgId();
        final List<Integer> acceptCommentTagList = params.getAcceptCommentTagList();
        // 当前应用单位仅 南医深圳：107
        if (OrgEnum.NAN_FANG_YI_KE_DA_XUE_SHEN_ZHEN_YI_YUAN.getCode().equals(orgId)) {
            orderAcceptCommentClient.saveOrderComment(orgId, orderMasterDO.getId(), acceptCommentTagList);
        }
        boolean isNormal = OrderSpeciesEnum.NORMAL.getValue().equals(orderMasterDO.getSpecies().intValue());
        if (isNormal) {
            //发送订单到评价系统
            AsyncExecutor.listenableRunAsync(() -> {
                this.sendOrderReceivedCredit(orderMasterDO, orderDetailList);
            }).addFailureCallback(throwable -> {
                LOGGER.error("发送订单到评价系统异常:" + throwable);
                Cat.logError(CAT_TYPE, "receiptOrderCore", "发送订单到评价系统异常: ", throwable);
            });

        }
    }

    /**
     * 收货发送邮件通知
     * @param orderMasterDO     订单主表
     * @param orderDetailDOList 订单详情
     */
    private void sendReceiptEmailStrategy(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList) {
        if (OrderSpeciesEnum.NORMAL.getValue().equals(orderMasterDO.getSpecies().intValue())) {
            //发送验收邮件给供应商
            orderEmailHandler.sendPurchaserReceiveOrderEmailToSupplier(orderMasterDO);
        }

        //江西肿瘤发送信息给采购人
        if (OrgConst.JIANG_XI_ZHONG_LIU.equals(orderMasterDO.getFusercode())) {
            sendReceiptMessageForJiangZhong(orderMasterDO, orderDetailDOList);
        }

        if (OrgEnum.ZHONG_QING_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getValue() == orderMasterDO.getFuserid()){
            orderMessageHandler.sendNoticeClaimAfterAcceptForChongQingYiKe(orderMasterDO, orderDetailDOList);
        }
    }


    /**
     * 保存验收材料
     *
     * @param params        入参
     * @param orderMasterDO 订单信息
     */
    private void saveAcceptanceMaterials(OrderReceiptParamDTO params, OrderMasterDO orderMasterDO) {
        // 保存验收图片、验收附件、付款记录
        AsyncExecutor.runAsync(() -> {
            saveOrderPic(params, orderMasterDO);
            saveOrderAttachment(params, orderMasterDO);
            savePaymentRecord(params, orderMasterDO);
        });
    }

    /**
     * 保存付款记录
     *
     * @param params        入参
     * @param orderMasterDO 订单信息
     */
    private void savePaymentRecord(OrderReceiptParamDTO params, OrderMasterDO orderMasterDO) {
        List<AttachmentDTO> paymentRecordList = params.getPaymentRecordList();
        if (CollectionUtils.isEmpty(paymentRecordList)) {
            return;
        }

        List<OrderUploadFileDTO> orderUploadFileDTOList = New.list();
        for (AttachmentDTO attachmentDTO : paymentRecordList) {
            OrderUploadFileDTO orderUploadFileDTO = new OrderUploadFileDTO();
            orderUploadFileDTO.setOrderId(orderMasterDO.getId());
            orderUploadFileDTO.setOrderNo(orderMasterDO.getForderno());
            orderUploadFileDTO.setFileBusinessType(FileBusinessTypeEnum.PAYMENT_RECORD.getCode());
            orderUploadFileDTO.setUrl(attachmentDTO.getUrl());
            orderUploadFileDTO.setFileName(attachmentDTO.getFileName());
            orderUploadFileDTOList.add(orderUploadFileDTO);
        }

        if (CollectionUtils.isNotEmpty(orderUploadFileDTOList)) {
            orderUploadFileRpcClient.overWriteList(orderUploadFileDTOList);
        }
    }

    /**
     * 保存订单验收图片到 orderPic表
     *
     * @param params        入参
     * @param orderMasterDO 订单信息
     */
    private void saveOrderPic(OrderReceiptParamDTO params, OrderMasterDO orderMasterDO) {
        String orderNo = orderMasterDO.getForderno();
        final List<String> picUrlList = CollectionUtils.isEmpty(params.getPictureUrlList()) ? New.emptyList() : params.getPictureUrlList();
        List<OrderPic> orderPicList = picUrlList.stream().map(url -> {
                    OrderPic orderPic = new OrderPic();
                    orderPic.setOrderNo(orderNo);
                    orderPic.setPic(url);
                    return orderPic;
                }
        ).collect(toList());
        LOGGER.info("保存验收图片,订单号:{},urls:{}", orderNo, JsonUtils.toJson(orderPicList));
        if (CollectionUtils.isNotEmpty(orderPicList)) {
            orderPicMapper.deleteByOrderNo(orderNo);
            orderPicMapper.batchInsert(orderPicList);
        }
        // 保存详情和图片关联信息
        overrideSaveDetailPic(params.getDetailPictureDTOList(), orderMasterDO);
    }


    /**
     * 保存上传验收附件
     *
     * @param params        orderNo, attachmentUrlList
     * @param orderMasterDO 订单信息
     */
    private void saveOrderAttachment(OrderReceiptParamDTO params, OrderMasterDO orderMasterDO) {
        List<AttachmentDTO> attachmentList = CollectionUtils.isEmpty(params.getAttachmentList()) ? New.list() : params.getAttachmentList();
        List<AttachmentDTO> orderVideoAttachmentList = CollectionUtils.isEmpty(params.getOrderVideoAttachmentList()) ? New.list() : params.getOrderVideoAttachmentList();
        String orderNo = orderMasterDO.getForderno();
        //没有附件信息
        if(CollectionUtils.isEmpty(attachmentList) && CollectionUtils.isEmpty(orderVideoAttachmentList)){
            return;
        }
        //验收附件
        OrderUploadFileDataRequestDTO orderFileUploadRequestDTO = new OrderUploadFileDataRequestDTO();
        List<OrderUploadFileDataDTO> orderUploadFileDataDTOList = attachmentList.stream()
                .map(attachmentDTO -> {
                    OrderUploadFileDataDTO orderUploadFileDataDTO = new OrderUploadFileDataDTO();
                    orderUploadFileDataDTO.setOrderId(orderMasterDO.getId());
                    orderUploadFileDataDTO.setFileBusinessType(ACCEPTANCE_ATTACHMENT.getCode());
                    orderUploadFileDataDTO.setUrl(attachmentDTO.getUrl());
                    orderUploadFileDataDTO.setOrderNo(orderNo);
                    orderUploadFileDataDTO.setFileName(attachmentDTO.getFileName());
                    return orderUploadFileDataDTO;
                }).collect(toList());
        //验收视频
        List<OrderUploadFileDataDTO> orderUploadVideoFileDataDTO = orderVideoAttachmentList.stream()
                .map(attachmentDTO -> {
                    OrderUploadFileDataDTO orderUploadFileDataDTO = new OrderUploadFileDataDTO();
                    orderUploadFileDataDTO.setOrderId(orderMasterDO.getId());
                    orderUploadFileDataDTO.setFileBusinessType(ACCEPTANCE_VIDEO.getCode());
                    orderUploadFileDataDTO.setUrl(attachmentDTO.getUrl());
                    orderUploadFileDataDTO.setOrderNo(orderNo);
                    orderUploadFileDataDTO.setFileName(attachmentDTO.getFileName());
                    return orderUploadFileDataDTO;
                }).collect(toList());
        orderUploadFileDataDTOList.addAll(orderUploadVideoFileDataDTO);
        orderFileUploadRequestDTO.setOrderUploadFileDataDTOList(orderUploadFileDataDTOList);
        //覆盖上传
        orderUploadFileService.saveUploadFileInfo(orderFileUploadRequestDTO);
        // 保存订单详情附件关联信息
        overrideSaveDetailAttachment(params.getDetailAttachmentDTOList(), orderMasterDO);
    }

    /**
     * 覆盖保存订单详情附件关联信息
     *
     * @param acceptAttachmentDTOList 验收附件信息
     * @param orderMasterDO           订单信息
     */
    private void overrideSaveDetailAttachment(List<AcceptAttachmentDTO> acceptAttachmentDTOList, OrderMasterDO orderMasterDO) {
        if (CollectionUtils.isEmpty(acceptAttachmentDTOList)) {
            return;
        }
        // 构建入库参数
        List<OrderDetailAcceptanceFileRequestDTO> saveList = New.list();
        for (AcceptAttachmentDTO acceptAttachmentDTO : acceptAttachmentDTOList) {
            List<Integer> orderDetailIdList = acceptAttachmentDTO.getOrderDetailIdList();
            // 只校验附件是否为空
            if (Objects.isNull(acceptAttachmentDTO.getAttachment())) {
                continue;
            }

            // 如果orderDetailIdList为空,则只关联订单
            if (CollectionUtils.isEmpty(orderDetailIdList)) {
                OrderDetailAcceptanceFileRequestDTO orderDetailAcceptanceFileDTO = new OrderDetailAcceptanceFileRequestDTO();
                orderDetailAcceptanceFileDTO.setOrderId(orderMasterDO.getId());
                orderDetailAcceptanceFileDTO.setDetailId(null); // 不关联详情ID
                orderDetailAcceptanceFileDTO.setUrl(acceptAttachmentDTO.getAttachment().getUrl());
                orderDetailAcceptanceFileDTO.setFileName(StringUtils.defaultString(acceptAttachmentDTO.getAttachment().getFileName()));
                saveList.add(orderDetailAcceptanceFileDTO);
                continue;
            }

            // 关联详情ID的逻辑
            for (Integer orderDetailId : orderDetailIdList) {
                OrderDetailAcceptanceFileRequestDTO orderDetailAcceptanceFileDTO = new OrderDetailAcceptanceFileRequestDTO();
                orderDetailAcceptanceFileDTO.setOrderId(orderMasterDO.getId());
                orderDetailAcceptanceFileDTO.setDetailId(orderDetailId);
                orderDetailAcceptanceFileDTO.setUrl(acceptAttachmentDTO.getAttachment().getUrl());
                orderDetailAcceptanceFileDTO.setFileName(StringUtils.defaultString(acceptAttachmentDTO.getAttachment().getFileName()));
                saveList.add(orderDetailAcceptanceFileDTO);
            }
        }
        // 删除旧的
        orderAcceptCommentClient.deleteDetailAcceptanceFile(orderMasterDO.getId());
        // 插入新的
        if (CollectionUtils.isNotEmpty(saveList)) {
            orderAcceptCommentClient.batchSaveDetailAcceptanceFile(saveList);
        }
    }


    /**
     * 覆盖保存订单详情和图片关联信息
     *
     * @param acceptPictureDTOList 验收图片信息
     * @param orderMasterDO        订单信息
     */
    private void overrideSaveDetailPic(List<AcceptPictureDTO> acceptPictureDTOList, OrderMasterDO orderMasterDO) {
        if (CollectionUtils.isEmpty(acceptPictureDTOList)) {
            return;
        }
        // 构建入库参数
        List<OrderDetailAcceptancePicRequestDTO> saveList = New.list();
        for (AcceptPictureDTO acceptPictureDTO : acceptPictureDTOList) {
            List<Integer> orderDetailIdList = acceptPictureDTO.getOrderDetailIdList();
            // 只校验pictureDTO
            if (Objects.isNull(acceptPictureDTO.getPictureDTO())) {
                continue;
            }
            PictureDTO pictureDTO = acceptPictureDTO.getPictureDTO();

            // 允许不关联详情
            if (CollectionUtils.isEmpty(orderDetailIdList)) {
                OrderDetailAcceptancePicRequestDTO orderDetailAcceptanceFileDTO = new OrderDetailAcceptancePicRequestDTO();
                orderDetailAcceptanceFileDTO.setOrderId(orderMasterDO.getId());
                orderDetailAcceptanceFileDTO.setDetailId(null); // 不关联详情ID
                orderDetailAcceptanceFileDTO.setUrl(pictureDTO.getPictureUrl());
                orderDetailAcceptanceFileDTO.setFileName(StringUtils.defaultString(pictureDTO.getPictureName()));
                saveList.add(orderDetailAcceptanceFileDTO);
                continue;
            }

            // 关联详情ID的逻辑
            for (Integer orderDetailId : orderDetailIdList) {
                OrderDetailAcceptancePicRequestDTO orderDetailAcceptanceFileDTO = new OrderDetailAcceptancePicRequestDTO();
                orderDetailAcceptanceFileDTO.setOrderId(orderMasterDO.getId());
                orderDetailAcceptanceFileDTO.setDetailId(orderDetailId);
                orderDetailAcceptanceFileDTO.setUrl(pictureDTO.getPictureUrl());
                orderDetailAcceptanceFileDTO.setFileName(StringUtils.defaultString(pictureDTO.getPictureName()));
                saveList.add(orderDetailAcceptanceFileDTO);
            }
        }
        // 删除旧的
        orderAcceptCommentClient.deleteDetailAcceptancePic(orderMasterDO.getId());
        // 插入新的
        if (CollectionUtils.isNotEmpty(saveList)) {
            orderAcceptCommentClient.batchSaveDetailAcceptancePic(saveList);
        }
    }


    /**
     * 江西肿瘤 验收发送信息
     * @param orderMasterDO     订单主信息
     * @param orderDetailList   订单详情
     */
    private void sendReceiptMessageForJiangZhong(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList) {
        List<String> goodNameList = orderDetailList.stream().map(OrderDetailDO::getFgoodname).collect(toList());
        String goodNames = StringUtils.join(goodNameList, ",");
        orderEmailHandler.sendEmailToPurchaserForJiangZhong(orderMasterDO);
        try {
            orderEmailHandler.sendWeChatToPurchaserForJiangZhong(orderMasterDO, goodNames);
        } catch (CallRpcException e) {
            LOGGER.error("获取openId异常！", e);
            Cat.logError(CAT_TYPE, "sendWeCatToPurchaserForJiangZhong", "获取openId异常！", e);
        }
    }

    /**
     * 格式化 验收图片链接
     *
     * @param picUrlList 验收图片URLs
     * @return
     */
    private String formatUrlList(List<String> picUrlList) {
        if (CollectionUtils.isNotEmpty(picUrlList)) {
            StringBuilder sb = new StringBuilder();
            for (String str : picUrlList) {
                if (sb.length() > 0) {
                    sb.append(";");
                }
                sb.append(str);
            }
            return sb.toString();
        } else {
            return "";
        }
    }

    /**
     * 订单验收后定制化业务, 以后可以考虑解耦, 新建订单验收下游业务系统, 处理验收后置业务
     *
     * @param orderReceiptParamDTO 验收订单入参
     * @param orderMasterDO        订单信息
     * @param receiptConfigMap     验收审批
     */
    private void pushReceiptInfoStrategy(OrderReceiptParamDTO orderReceiptParamDTO, OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, Map<String, String> receiptConfigMap) {
        final Integer userId = orderReceiptParamDTO.getUserId();
        final Integer orgId = orderMasterDO.getFuserid();
        //中爆对接推送数据
        if (standingBookOrgIdList.contains(orgId) && userClient.getUserReportStatus(orgId, orderMasterDO.getFbuydepartmentid(), userId)) {
            pushOrderToStandingBook(orderMasterDO, orderReceiptParamDTO);
            return;
        }
        //深圳妇幼要在收货时预算冻结经费
        if (OrgEnum.SHEN_ZHEN_SHI_FU_YOU_BAO_JIAN_YUAN.getValue() == orgId) {
            RjSessionInfo rjSessionInfo = (RjSessionInfo) RpcContext.getProviderContext().getCallAttachment(GatewayConstant.SESSION_INFO);
            LoginUserInfoBO loginUserInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
            AsyncExecutor.listenableRunAsync(() -> {
                refFundcardOrderService.orderFundCardFreeze(orderMasterDO, orderDetailList, loginUserInfo);
            }).addFailureCallback(throwable -> {
                LOGGER.error("订单:" + orderMasterDO.getForderno() + "经费扣减失败{}", throwable);
            });
        }
        //陆军医推送 todo 验收后置动作之后重构, 先写在这里
        if (OrgEnum.LU_JUN_JUN_YI_DA_XUE.getValue() == orgId) {
            // 是否 验收审批, 用就推送到延时队列
            boolean isAcceptApproval = this.parseBooleanConfig(receiptConfigMap, ConfigConstant.ORG_ACCEPTANCE_APPROVAL_CONFIG, ConfigConstant.ACCEPTANCE_APPROVAL.toString());
            if (!isAcceptApproval) {
                return;
            }
            AsyncExecutor.listenableRunAsync(() -> acceptApprovalClient.submitDelayApproval(orderMasterDO.getFusercode(), orderMasterDO.getId(), orderMasterDO.getFbuyerid(), orderMasterDO.getFbuyername()));
        }
        //临床手动验收要更新二维码的外观信息
        if (OrgEnum.ZHONG_SHAN_SAN_YUAN_LIN_CHUANG.getValue() == orgId) {
            List<OrderAcceptDetailDTO> acceptDetailList = orderReceiptParamDTO.getOrderAcceptDetailList();
            if (CollectionUtils.isEmpty(acceptDetailList)) {
                return;
            }
            Runnable task = () -> {
                List<OrderUniqueBarCodeDTO> barCodeDTOList = orderUniqueBarCodeRPCClient.findByDetailIdList(acceptDetailList.stream().map(OrderAcceptDetailDTO::getDetailId).collect(toList()));
                Map<Integer, Integer> detailIdExteriorMap = DictionaryUtils.toMap(acceptDetailList, OrderAcceptDetailDTO::getDetailId, OrderAcceptDetailDTO::getExterior);
                barCodeDTOList.forEach(it -> it.setExterior(Optional.ofNullable(detailIdExteriorMap.get(it.getOrderDetailId())).orElse(0)));
                orderUniqueBarCodeRPCClient.modifyOrderBarCodeBatches(barCodeDTOList);
            };
            AsyncExecutor.runAsync(task);
        }

        //广西肿瘤的同步
        if (OrgEnum.GUANG_XI_ZHONG_LIU.getValue() == orgId && !OrderDateConstant.isOldOrderForNormal(orderMasterDO.getFusercode(), orderMasterDO.getForderdate())) {
            UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
            updateOrderParamDTO.setOrderId(orderMasterDO.getId());
            updateOrderParamDTO.setInventoryStatus(InventoryStatusEnum.WAITING_FOR_REVIEW.getCode());
            orderMasterMapper.updateOrderById(updateOrderParamDTO);

            ThirdPartyPlatformOrderBO platformOrderBO = new ThirdPartyPlatformOrderBO();
            platformOrderBO.setOrgCode(DockingConstant.GUANG_XI_ZHONG_LIU);
            platformOrderBO.setReagentOrderNo(orderMasterDO.getForderno());
            platformOrderBO.setExtraOrderNo(orderMasterDO.getForderno());
            platformOrderBO.setStatus(OrderStatusEnum.WaitingForStatement_1.getValue());

            orderMasterForTPIService.updateOrderStatusAsync(platformOrderBO);
        }
        //单据对接的验收推送
        if (dockingConfigCommonService.getIfNeedOldPush(orderMasterDO, New.list(OuterBuyerDockingTypeEnum.ORDER_PUSH))) {
            ThirdPartOrderMasterDTO thirdPartOrder = OrderMasterTranslator.doToThirdPartOrderDTO(orderMasterDO);
            thirdPartOrderRPCClient.pushOrderInfo(thirdPartOrder, OrderEventTypeEnum.ACCEPT_ORDER, orderReceiptParamDTO.getUserGuid(), orderReceiptParamDTO.getUserName());
        }
    }

    /**
     * 推送信息给中爆对接平台
     *
     * @param orderMasterDO
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    private void pushOrderToStandingBook(OrderMasterDO orderMasterDO, OrderReceiptParamDTO orderReceiptParamDTO) {
        RecordOrderInfoDTO recordOrderInfoDTO = new RecordOrderInfoDTO();
        recordOrderInfoDTO.setOrderId(orderMasterDO.getId().longValue());
        recordOrderInfoDTO.setBuyerId(orderMasterDO.getFbuyerid());
        recordOrderInfoDTO.setDeptId(orderMasterDO.getFbuydepartmentid());
        if (CollectionUtils.isNotEmpty(orderReceiptParamDTO.getLegalUseUrlList())) {
            recordOrderInfoDTO.setDescriptionOfLegalUse(orderReceiptParamDTO.getLegalUseUrlList().get(0));
        }

        if (CollectionUtils.isNotEmpty(orderReceiptParamDTO.getTradeUrlList())) {
            recordOrderInfoDTO.setTransactionVoucher(orderReceiptParamDTO.getTradeUrlList().get(0));
        }
        recordOrderInfoDTO.setStoreHouseId(orderReceiptParamDTO.getStoreHouseId());
        recordOrderInfoDTO.setStoreHouseName(orderReceiptParamDTO.getStoreHouseName());

        List<OrderMasterSearchDTO> orderMasterSearchDTOS = orderSearchBoostService.searchOrderById(orderMasterDO.getId());
        if (CollectionUtils.isNotEmpty(orderMasterSearchDTOS)) {
            OrderMasterSearchDTO orderMasterSearchDTO = orderMasterSearchDTOS.get(0);
            List<OrderDetailSearchDTO> orderDetail = orderMasterSearchDTO.getOrderDetail();

            // 用户输入的易爆品数据
            List<OrderExplosivesGoodsDTO> explosivesGoodsList = orderReceiptParamDTO.getExplosivesGoodsList();
            Map<Integer, OrderExplosivesGoodsDTO> detailIdMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(explosivesGoodsList)) {
                detailIdMap = explosivesGoodsList.stream().collect(Collectors.toMap(OrderExplosivesGoodsDTO::getId, Function.identity()));
            }

            Map<Integer, OrderExplosivesGoodsDTO> finalDetailIdMap = detailIdMap;
            List<RecordOrderGoodsInfoDTO> goodList = orderDetail.stream().filter(orderDetailSearchDTO ->
                    //过滤掉非易爆品的商品
                    (orderDetailSearchDTO.getDangerousType() != null
                            && DangerousTypeEnum.EASY_MAKE_BOMB.getValue().equals(orderDetailSearchDTO.getDangerousType()))
            ).map(orderDetailDO -> this.createGoodByOrderDetail(orderDetailDO, finalDetailIdMap)).collect(toList());
            recordOrderInfoDTO.setGoods(goodList);
            if (CollectionUtils.isNotEmpty(goodList)) {
                //危化品商品不为空才推送中爆数据
                cooperationClient.pushOrderInfo(recordOrderInfoDTO);
            }
        }
    }

    /**生成台账所需获取信息
     * @param orderDetailDO
     * @param detailIdMap
     * @return
     */
    private RecordOrderGoodsInfoDTO createGoodByOrderDetail(OrderDetailSearchDTO orderDetailDO, Map<Integer, OrderExplosivesGoodsDTO> detailIdMap) {
        RecordOrderGoodsInfoDTO recordOrderGoodsInfoDTO = new RecordOrderGoodsInfoDTO();
        Integer detailId = orderDetailDO.getDetailId();
        recordOrderGoodsInfoDTO.setOrderDetailId(Long.valueOf(detailId));
        recordOrderGoodsInfoDTO.setGoodsId(orderDetailDO.getProductId());
        recordOrderGoodsInfoDTO.setGoodName(orderDetailDO.getFgoodname());
        recordOrderGoodsInfoDTO.setUnit(orderDetailDO.getFunit());
        recordOrderGoodsInfoDTO.setQuantity(orderDetailDO.getFquantity());
        recordOrderGoodsInfoDTO.setCasNo(orderDetailDO.getCasNo());

        if (detailIdMap.containsKey(detailId)) {
            OrderExplosivesGoodsDTO explosivesGoodsDTO = detailIdMap.get(detailId);
            Assert.notNull(explosivesGoodsDTO.getQuantity(), "易爆品数量不能为空！");
            Assert.notNull(explosivesGoodsDTO.getUnit(), "易爆品计量单位不能为空！");
            Assert.notNull(explosivesGoodsDTO.getAttrition(), "易爆品损耗量不能为空！");
            recordOrderGoodsInfoDTO.setQuantity(explosivesGoodsDTO.getQuantity().intValue());
            recordOrderGoodsInfoDTO.setUnit(explosivesGoodsDTO.getUnit());
            recordOrderGoodsInfoDTO.setLoss(explosivesGoodsDTO.getAttrition().intValue());
            recordOrderGoodsInfoDTO.setLegalUse(explosivesGoodsDTO.getLegalUse());

        }
        return recordOrderGoodsInfoDTO;
    }

    /**
     * 比较配置并返回配置
     *
     * @param receiptConfigMap
     * @param orgAcceptanceConfigCode
     * @param s
     * @return
     */
    private boolean parseBooleanConfig(Map<String, String> receiptConfigMap, String orgAcceptanceConfigCode, String s) {
        String acceptanceConfig = receiptConfigMap.get(orgAcceptanceConfigCode);
        BusinessErrUtil.isTrue(StringUtils.isNotEmpty(acceptanceConfig), ExecptionMessageEnum.ACCEPTANCE_CONFIG_NOT_SET, acceptanceConfig);
        return s.equals(acceptanceConfig);
    }

    private Integer parseIntegerConfig(Map<String, String> receiptConfigMap, String orgAcceptanceConfigCode){
        String acceptanceConfig = receiptConfigMap.get(orgAcceptanceConfigCode);
        BusinessErrUtil.isTrue(StringUtils.isNotEmpty(acceptanceConfig), ExecptionMessageEnum.ACCEPTANCE_CONFIG_NOT_SET, acceptanceConfig);
        return NumberUtils.toInt(acceptanceConfig);
    }

    /**
     * @param receiptConfigMap 单位在oms配置
     * @param isNormal         是否线上单
     * @return
     */
    private boolean isUsedStatement(Map<String, String> receiptConfigMap, Boolean isNormal) {
        /* 当前单位是否使用结算系统 add by Kimmy 2021/03/29*/
        boolean usedStatement = false;
        if (isNormal) {
            //线上单是否使用结算系统
            String usedStatementConfig = receiptConfigMap.get(ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM);
            usedStatement = ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM_VALUE.equals(usedStatementConfig);
        } else {
            //线下单是否使用结算系统
            String usedStatementConfig = receiptConfigMap.get(ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM);
            usedStatement = ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM_VALUE.equals(usedStatementConfig);
        }
        return usedStatement;
    }

    /**
     * 是否为未对接经费的订单
     *
     * @param orderMasterDO
     * @return
     */
    private boolean isUnRelateOrderData(OrderMasterDO orderMasterDO) {
        String orgCode = orderMasterDO.getFusercode();
        String oldDate = OrderDateConstant.ORG_CODE_OLD_ORDER_DATE_MAP.get(orgCode);
        if ((OrgEnum.NAN_FANG_YI_KE.getCode().equals(orgCode) && orderMasterDO.getForderdate().before(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, oldOrderDateForNanFangYiKe)))
                || (oldDate != null && orderMasterDO.getForderdate().before(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, oldDate)))) {
            return true;
        }
        return false;
    }

    /**
     * 订单验收超时处理
     *
     * @param orgId
     * @param orgCode
     * @param departmentId
     */
    private void orderTimeOutStatisticsHandler(Integer orgId, String orgCode, Integer departmentId) {
        timeoutStatisticsService.executeTimeOutStatisticsDecrease(1, orgId, orgCode, departmentId, TimeOutBusinessType.ACCEPTANCE);
    }

    /**
     * 记录订单验收日志
     *
     * @param userId
     * @param orderId
     */
    private void createOrderOperateLog(Integer orderId, Integer approveStatus, Integer userId, String reason) {
        OrderApprovalLog orderApprovalLog = new OrderApprovalLog();
        orderApprovalLog.setOrderId(orderId);
        orderApprovalLog.setApproveStatus(approveStatus);
        orderApprovalLog.setOperatorId(userId);
        orderApprovalLog.setReason(reason);
        orderApprovalLogMapper.insertSelective(orderApprovalLog);
        LOGGER.info("记录订单日志成功！入参{}", JsonUtils.toJson(orderApprovalLog));
    }

    /**
     * 保存拓展数据
     * @param params 验收参数
     * @param orderMasterDO 订单快照
     */
    private void saveOrderExtraData(OrderReceiptParamDTO params, OrderMasterDO orderMasterDO){
        List<BaseOrderExtraDTO> needSaveExtras = New.list();
        if(params.getExitDept() != null){
            BaseOrderExtraDTO exitExtra = OrderExtraTranslator.orderMaterDO2BaseExtra(orderMasterDO, OrderExtraEnum.EXIT_DEPT, params.getExitDept());
            needSaveExtras.add(exitExtra);
        }
        if (StringUtils.isNotBlank(params.getExperimentDataUrl())) {
            BaseOrderExtraDTO exitExtra = OrderExtraTranslator.orderMaterDO2BaseExtra(orderMasterDO, OrderExtraEnum.EXPERIMENT_DATA_URL, params.getExperimentDataUrl());
            needSaveExtras.add(exitExtra);
        }

        if(!CollectionUtils.isEmpty(needSaveExtras)){
            orderExtraClient.saveList(New.list(needSaveExtras));
        }
    }

    /**
     * 校验订单合同 是否需要上传且已上传
     *
     * @param orderMasterDO      订单主表数据
     * @param orderDetailList    订单详情数据
     * @param contractConfigJson 合同配置JSON
     */
    private void checkOrderContract(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, String contractConfigJson) {
        Integer orderId = orderMasterDO.getId();
        // 未查询到配置，不校验
        if (StringUtils.isBlank(contractConfigJson)) {
            return;
        }

        // 解析合同配置
        OrderContractConfig contractConfig = JsonUtils.fromJson(contractConfigJson, OrderContractConfig.class);

        // 1.不开启合同校验，则不校验
        if (Objects.isNull(contractConfig) || Objects.equals(contractConfig.getUploadContract(), OrderUploadContractEnum.NO.getValue())) {
            return;
        }

        // 2.查询合同上传状态
        Map<Integer, Integer> uploadStatusMap = orderContractService.getOrderContractUploadStatus(
                New.list(orderMasterDO), New.map(orderId, orderDetailList), contractConfigJson, orderMasterDO.getFuserid());
        Integer uploadStatus = uploadStatusMap.get(orderId);
        // 如果是无需上传或已上传，则不校验
        if (Objects.equals(uploadStatus, OrderUploadStatusEnum.NO_NEED_TO_UPLOAD.value) ||
                Objects.equals(uploadStatus, OrderUploadStatusEnum.TRUE.value)) {
            return;
        }

        // 3.根据上传条件进行校验
        Integer uploadCondition = contractConfig.getUploadCondition();
        // 必须上传
        BusinessErrUtil.isTrue(!Objects.equals(uploadCondition, OrderContractUploadConditionEnum.MUST_UPLOAD.getValue()), ExecptionMessageEnum.PLEASE_UPLOAD_CONTRACT);
        // 按金额限制上传
        if (Objects.equals(uploadCondition, OrderContractUploadConditionEnum.AMOUNT_LIMIT_UPLOAD.getValue())) {
            double orderAmountLimit = NumberUtils.toDouble(contractConfig.getAmountLimit());
            if (orderAmountLimit > 0) {
                BigDecimal orderAmount = orderMasterDO.getForderamounttotal().subtract(BigDecimal.valueOf(orderMasterDO.getReturnAmount()));
                boolean overLimit = orderAmount.compareTo(BigDecimal.valueOf(orderAmountLimit)) >= 0;
                BusinessErrUtil.isTrue(!overLimit, ExecptionMessageEnum.ORDER_EXCEEDS_LIMIT_UPLOAD_CONTRACT, String.valueOf(orderAmountLimit));
            }
        }

        // 4.单位定制个性化的合同校验
        this.customValidationOrderContract(orderMasterDO, orderDetailList);
    }

    /**
     * 单位定制个性化的合同校验
     *
     * @param orderMasterDO   订单主表信息
     * @param orderDetailList 订单详情信息
     */
    private void customValidationOrderContract(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList) {
        if (OrgEnum.JIN_FENG_SHI_YAN_SHI.getValue() == orderMasterDO.getFuserid()) {
            // 是否线上单
            boolean isOnlineOrder = OrderSpeciesEnum.NORMAL.getValue() == orderMasterDO.getSpecies().intValue();
            BigDecimal totalPrice = orderDetailList.stream().map(OrderDetailDO::getFbidamount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal onlineOrderTotalPriceThreshold = BigDecimal.valueOf(50000);
            BusinessErrUtil.isTrue(!(totalPrice.compareTo(onlineOrderTotalPriceThreshold) >= 0 && isOnlineOrder), ExecptionMessageEnum.ONLINE_ORDER_CONTRACT_REQUIRED);
            // 科研服务类品类
            final int scienceServiceCategoryId = 113;
            // 线下单商品总价阈值
            final BigDecimal offlineOrderTotalPriceThreshold = BigDecimal.valueOf(10000);
            // 含科研服务类商品
            boolean containsScienceServiceGoods = orderDetailList.stream().anyMatch(detail -> scienceServiceCategoryId == detail.getFirstCategoryId());

            // 线下单商品总价大于阈值
            boolean offlineTotalPriceOverThreshold = totalPrice.compareTo(offlineOrderTotalPriceThreshold) >= 0 && !isOnlineOrder;

            BusinessErrUtil.isTrue(!(offlineTotalPriceOverThreshold && containsScienceServiceGoods), ExecptionMessageEnum.OFFLINE_OR_RESEARCH_CONTRACT_REQUIRED);
            BusinessErrUtil.isTrue(!containsScienceServiceGoods, ExecptionMessageEnum.RESEARCH_SERVICE_CONTRACT_REQUIRED);
            BusinessErrUtil.isTrue(!offlineTotalPriceOverThreshold, ExecptionMessageEnum.OFFLINE_ORDER_CONTRACT_REQUIRED);
        }
    }

    /**
     * 中肿判断 是否不需要拍照验收
     *
     * @return
     */
    private boolean isNoPhotoAcceptance(final OrderDetailDO detailDO) {
        if (null == detailDO) {
            return false;
        }

        Integer lastCategoryId = detailDO.getCategoryid();
        Integer firstCategoryId = detailDO.getFirstCategoryId();
        Integer secondCategoryId = detailDO.getSecondCategoryId();
        if (lastCategoryId == null && firstCategoryId == null && secondCategoryId == null) {
            return false;
        }

        List<Integer> idList = New.listWithCapacity(3);
        idList.add(lastCategoryId);
        idList.add(firstCategoryId);
        idList.add(secondCategoryId);
        if (idList.contains(ConfigConstant.SERVICE_CATEGORY_ID) || (idList.contains(ConfigConstant.ANIMAL_CATEGORY_ID) && !idList.contains(ConfigConstant.MODEL_ANIMAL_CATEGORY_ID))) {
            return true;
        }

        return false;
    }

    /**
     * 推送订单信息到评价系统
     *
     * @param orderMasterDO
     * @param orderDetailList
     * @throws CallRpcException
     */
    private void sendOrderReceivedCredit(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList) throws CallRpcException {
        SendOrderRecivedDataReq sendOrderRecivedDataReq = new SendOrderRecivedDataReq();
        List<SendOrderReceivedData> sendDataList = New.listWithCapacity(orderDetailList.size());
        for (OrderDetailDO orderDetail : orderDetailList) {
            SendOrderReceivedData sendOrderReceivedData = new SendOrderReceivedData();
            sendOrderReceivedData.setOrderDetailId(orderDetail.getId().toString());
            sendOrderReceivedData.setOrderId(orderMasterDO.getId().toString());
            sendOrderReceivedData.setProductId(orderDetail.getProductSn().toString());
            sendOrderReceivedData.setProductName(orderDetail.getFgoodname());
            sendOrderReceivedData.setProductPicUrl(orderDetail.getFpicpath());
            sendOrderReceivedData.setProductSpecification(orderDetail.getFspec());
            sendOrderReceivedData.setBuyerId(String.valueOf(orderMasterDO.getFbuyerid()));
            sendOrderReceivedData.setBuyerName(orderMasterDO.getFbuyername());
            sendOrderReceivedData.setOrgId(orderMasterDO.getFuserid().toString());
            sendOrderReceivedData.setOrgName(orderMasterDO.getFusername());
            sendOrderReceivedData.setDeptId(String.valueOf(orderMasterDO.getFbuydepartmentid()));
            sendOrderReceivedData.setDeptName(orderMasterDO.getFbuydepartment());
            sendOrderReceivedData.setSupplierName(orderMasterDO.getFsuppname());
            sendOrderReceivedData.setSupplierId(String.valueOf(orderMasterDO.getFsuppid()));
            sendOrderReceivedData.setReceiveTime(orderMasterDO.getFlastreceivedate());
            //验收人id
            sendOrderReceivedData.setReceiveUserId(orderMasterDO.getFlastreceivemanid() == null ? "0" : orderMasterDO.getFlastreceivemanid());
            sendDataList.add(sendOrderReceivedData);
        }
        sendOrderRecivedDataReq.setOrderList(sendDataList);
        creditServiceClient.addOrderReceivedData(sendOrderRecivedDataReq);
    }

    /**
     * 判断 订单 使用哪种验收模式
     * @param orderMasterDO      订单信息
     * @param orderDetailList    订单商品明细
     * @param isAcceptApproval   手否 验收审批
     * @param platformWorkFunds  是否 平台经费
     * @param unRelateOrderData  是否 旧单
     * @return Integer  无匹配模式（需要报错）
     *                  1--验收--完成 模式
     *                  2--验收--审批 模式
     *                  3--验收--（待）结算 模式
     */
    @Override
    public Integer calculateAcceptModel(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, boolean isAcceptApproval,
                                        boolean platformWorkFunds, boolean unRelateOrderData) {
        //1 验收--完成模式,2 验收--审批模式 ,3 验收--（待）结算 ，0 -- 无匹配模式报错
        String orgCode = orderMasterDO.getFusercode();
        boolean isDistributeStatement = OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue().equals(orderMasterDO.getFundStatus());
        boolean isFinishOrg = ACCEPT_FINISH_ORG_SET.contains(orgCode);
        //自结算使用 验收--完成 模式
        if (isDistributeStatement) {
            return 1;
        }

        if (!isAcceptApproval) {
            // 非验收审批 且 （验收完成单位 || 平台运行经费|| 旧未对接的单 ） 是验收--完成 模式
            if (isFinishOrg || platformWorkFunds || unRelateOrderData) {
                return 1;
            }
        } else {
            //验收--审批 模式
            return 2;
        }
        return 3;
    }


    /**
     * 订单验收 核心代码
     * @param orderMasterDO
     * @param receiptConfigMap
     * @param orderDetailList
     * @return
     */
    private ReceiptOrderResponseDO acceptCore(OrderReceiptParamDTO params, Map<String, String> receiptConfigMap, OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList) {
        final Integer orderId = orderMasterDO.getId();

        List<OrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdAndExtraKey(New.list(orderId), New.list(OrderExtraEnum.IS_TRIAL_ORDER.getValue()));
        boolean isTrialOrder = CollectionUtils.isNotEmpty(orderExtraDTOList) && CommonValueUtils.parseNumberStrToBoolean(orderExtraDTOList.get(0).getExtraValue());
        if(isTrialOrder){
            // 是试用订单
            return this.trialOrderAcceptCore(params, orderMasterDO, orderDetailList);
        }
        return normalOrderAcceptCore(params, receiptConfigMap, orderMasterDO, orderDetailList);
    }

    /**
     * 试用订单验收
     * @param params 验收参数
     * @param orderMasterDO   订单参数
     * @param orderDetailList   订单详情
     * @return 结果
     */
    private ReceiptOrderResponseDO trialOrderAcceptCore(OrderReceiptParamDTO params, OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList){
        final Integer orderId = orderMasterDO.getId();
        final String orderNo = orderMasterDO.getForderno();
        final Integer operatorId = params.getUserId();
        final String reason = params.getAcceptReason();
        final Integer inventoryStatus = params.getInventoryStatus();
        ReceiptOrderResponseDO response = new ReceiptOrderResponseDO();
        response.setOrderNo(orderMasterDO.getForderno());

        String picUrl = this.formatUrlList(params.getPictureUrlList());

        // 1.记验收日志
        this.createOrderOperateLog(orderId, OrderApprovalEnum.RECEIPT.getValue(), operatorId, reason);
        this.createOrderOperateLog(orderId, OrderApprovalEnum.ORDER_CLOSE.getValue(), operatorId, "该订单为试用商品订单，无需报账，验收完成后将直接关闭订单");
        // 保存拓展信息
        this.saveOrderExtraData(params, orderMasterDO);
        // 2.更新为已关闭
        UpdateOrderParamDTO updated = this.getReceiveOrderCommonParam(params);
        // 一物一码特殊需求，由于校验是否已经全部扫码入库完成是订单这边操作的，所以入库完成是在订单侧更新的。
        // 而目前全部扫码入库完成后更新库房状态放到了收货这边一并处理，后续考虑将入库完成状态更新进行分离出来，调用自动收货前先更新
        if(inventoryStatus != null){
            updated.setInventoryStatus(inventoryStatus);
            orderMasterDO.setInventoryStatus(inventoryStatus.byteValue());
        }
        updated.setOrderId(orderMasterDO.getId());
        updated.setStatus(OrderStatusEnum.Close.getValue());
        updated.setShutDownDate(updated.getLastReceiveDate());
        orderMasterMapper.updateOrderById(updated);

        LOGGER.info("订单收货，试用订单，订单id为{}的订单状态更新到已关闭,无须入库",orderMasterDO.getId());

        // 3.使用入库且订单当前库房状态非待入库。防止重新操作或三院临床特殊逻辑（三院临床先入库，扫码入库完成再调用自动收货进行收货操作）,不再调用库房接口
        // 当前库房状态
        InventoryStatusEnum inventoryStatusEnum = InventoryStatusEnum.getByCode(orderMasterDO.getInventoryStatus().intValue());
        inventoryStatusEnum = inventoryStatusEnum == null ? InventoryStatusEnum.WAITING_FOR_STORE : inventoryStatusEnum;
        if(InventoryStatusEnum.WAITING_FOR_STORE.equals(inventoryStatusEnum)){
            // 无需入库处理
            OrderAcceptService customOrderAcceptService = BeanContainer.getBean(OrderAcceptService.class, OrderAcceptConstant.getAcceptStrategy().get(orderMasterDO.getFusercode()));
            if (customOrderAcceptService == null) {
                customOrderAcceptService = this;
            }
            InWarehouseModeEnum warehouseMode = InWarehouseModeEnum.NO_NEED;
            // 根据入库模式进行相关入库处理
            WarehouseResultDTO warehouseResultDTO = customOrderAcceptService.executeInWareHouse(warehouseMode, orderMasterDO, orderDetailList, picUrl);
            response.setWarehouseResultDTO(warehouseResultDTO);
        }

        // 4.解冻
        orderFundCardService.orderFundCardUnFreeze(orderMasterDO, null);

        // 5.订单超时统计处理
        this.orderTimeOutStatisticsHandler(orderMasterDO.getFuserid(), orderMasterDO.getFusercode(), orderMasterDO.getFbuydepartmentid());

        // 6.更新一物状态为已验收
        this.updateBarcodeStatusToReceived(orderMasterDO.getForderno());

        // 7.释放预占库存
        warehouseStockOccupyService.releaseAll(orderId,orderNo);

        return response;
    }

    /**
     * 普通订单验收
     * @param params 验收参数
     * @param orderMasterDO   订单参数
     * @param orderDetailList   订单详情
     * @return 结果
     */
    private ReceiptOrderResponseDO normalOrderAcceptCore(OrderReceiptParamDTO params, Map<String, String> receiptConfigMap, OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList){
        final Integer orgId = params.getOrgId();
        final Integer operatorId = params.getUserId();
        final String operatorName = params.getUserName();
        final String urls = this.formatUrlList(params.getPictureUrlList());
        final String reason = params.getAcceptReason();
        final Integer inventoryStatus = params.getInventoryStatus();
        final String orgCode = orderMasterDO.getFusercode();
        final Integer orderId = orderMasterDO.getId();
        final Integer departmentId = orderMasterDO.getFbuydepartmentid();

        UpdateOrderParamDTO updated = this.getReceiveOrderCommonParam(params);
        updated.setOrderId(orderMasterDO.getId());
        //订单验收成功，默认是 待结算状态6，如果验收自动提交结算，订单状态变为结算中10。特殊医院 南方医和暨大 验收后变为订单验收审批20
        updated.setStatus(OrderStatusEnum.WaitingForStatement_1.value);

        // 是否 验收审批, 使用验收审批的话，先不处理库房，结算的配置，都为false
        boolean isAcceptApproval = this.parseBooleanConfig(receiptConfigMap, ConfigConstant.ORG_ACCEPTANCE_APPROVAL_CONFIG, ConfigConstant.ACCEPTANCE_APPROVAL.toString());
        // 是否验收审批，除了验收审批配置，还要判断是否订单验收
        isAcceptApproval = isAcceptApproval && this.isOrderAcceptance(orderId);
        // 判断是否是平台运行经费
        boolean platformWorkFunds = researchBaseService.isPlatformFound(orderMasterDO);
        // 是否未对接经费的历史订单
        boolean unRelateOrderData = this.isUnRelateOrderData(orderMasterDO);

        // 订单更新日志处理
        this.createOrderOperateLog(orderId, OrderApprovalEnum.RECEIPT.getValue(), operatorId, reason);

        // 保存拓展信息
        this.saveOrderExtraData(params, orderMasterDO);

        boolean isNormal = orderMasterDO.getSpecies().intValue() == ProcessSpeciesEnum.NORMAL.getValue();

        //计算验收模式
        Integer acceptModel;
        OrderAcceptService customOrderAcceptService = BeanContainer.getBean(OrderAcceptService.class, OrderAcceptConstant.getAcceptStrategy().get(orgCode));
        if (customOrderAcceptService != null) {
            acceptModel = customOrderAcceptService.calculateAcceptModel(orderMasterDO, orderDetailList, isAcceptApproval, platformWorkFunds, unRelateOrderData);
        } else {
            acceptModel = this.calculateAcceptModel(orderMasterDO, orderDetailList, isAcceptApproval, platformWorkFunds, unRelateOrderData);
        }

        // 扣服务费
        if (isNormal) {
            // 线上单 插入钱包扣费信息 如果已存在订单号则不重复扣费
            walletOrderRpcClient.addOrderWalletQueue(orderMasterDO);
        }
        // 设置库房更新数据
        Date receiveDate = updated.getLastReceiveDate();
        // 一物一码特殊需求，由于校验是否已经全部扫码入库完成是订单这边操作的，所以入库完成是在订单侧更新的。
        // 而目前全部扫码入库完成后更新库房状态放到了收货这边一并处理，后续考虑将入库完成状态更新进行分离出来，调用自动收货前先更新
        if(Objects.nonNull(inventoryStatus)){
            updated.setInventoryStatus(inventoryStatus);
            orderMasterDO.setInventoryStatus(inventoryStatus.byteValue());
        }

        // 验收结果
        ReceiptOrderResponseDO response = new ReceiptOrderResponseDO();
        response.setOrderNo(orderMasterDO.getForderno());

        // 先手更新状态，再去进行库房/结算数据的写入。以防数据写入失败导致库房入库回调/结算完成状态更新为结算中但没有验收数据；或数据写入慢于库房回调，导致库房回调产生的数据被覆盖
        switch (acceptModel) {
            case 1:
                updated.setStatus(OrderStatusEnum.Finish.value);
                updated.setFinishDate(receiveDate);
                orderMasterMapper.updateOrderById(updated);
                this.acceptToFinish(response, orderMasterDO, receiptConfigMap, urls, orderDetailList);
                break;
            case 2:
                updated.setStatus(OrderStatusEnum.OrderReceiveApproval.value);
                orderMasterMapper.updateOrderById(updated);
                LOGGER.info("订单收货，验收-审批模式，订单id为{}的订单状态更新到验收审批",orderMasterDO.getId());
                this.acceptToApproval(orderMasterDO, params);
                break;
            case 3:
                updated.setStatus(OrderStatusEnum.WaitingForStatement_1.value);
                orderMasterMapper.updateOrderById(updated);
                this.acceptNormal(response, orderMasterDO, receiptConfigMap, urls, operatorId, operatorName, orderDetailList);
                break;
            default:
                BusinessErrUtil.isTrue(true, ExecptionMessageEnum.NO_MATCHING_ACCEPTANCE_MODE);
        }
        // 订单超时统计处理
        this.orderTimeOutStatisticsHandler(orgId, orderMasterDO.getFusercode(), departmentId);

        // 更新一物状态为已验收
        this.updateBarcodeStatusToReceived(orderMasterDO.getForderno());

        return response;
    }

    private UpdateOrderParamDTO getReceiveOrderCommonParam(OrderReceiptParamDTO params){
        String picUrl = this.formatUrlList(params.getPictureUrlList());
        String secondReceiverName = params.getSecondReceiverName();
        Integer operatorId = params.getUserId();
        String operatorName = params.getUserName();

        UpdateOrderParamDTO updated = new UpdateOrderParamDTO();
        // 设置库房更新数据
        Date receiveDate = new Date();
        updated.setReceivePicUrls(picUrl);
        updated.setLastReceiveMan(Optional.ofNullable(secondReceiverName).orElse(operatorName));
        updated.setLastReceiveManId(String.valueOf(operatorId));
        updated.setLastReceiveDate(receiveDate);
        return updated;
    }

    /**
     * 华农推送订单信息(旧版本/基里版本)
     *
     * @param orderMasterDO
     */
    @Deprecated
    private void sendOrderInfo(OrderMasterDO orderMasterDO) {
        // 华农兼容基里逻辑，基里是推送采购单，需判断采购单生成时间作为旧单时间
        ApplicationMasterDTO applicationMasterDTO = applicationBaseClient.getApplicationMasterByApplyId(orderMasterDO.getFtbuyappid(), false);
        // 生成采购单时间晚于新对接事件，不推送基里
        if (applicationMasterDTO.getCreateTime().after(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, OrderDateConstant.HUA_NAN_NONG_YE_DA_XUE_OLD_DOCKING_TIME))) {
            return;
        }

        HNOrderBySelfBuyingReq req = new HNOrderBySelfBuyingReq();
        Integer masterDOId = orderMasterDO.getId();
        List<OrderContract> orderContractList = orderContractMapper.findByOrderId(masterDOId);
        if (CollectionUtils.isNotEmpty(orderContractList)) {
            req.setContractFile(orderContractList.get(0).getContractLocation());
        }

        //获取华农用户信息
        UserBaseInfoDTO userInfo = userClient.getUserInfo(orderMasterDO.getFbuyerid(), orderMasterDO.getFuserid());
        BusinessErrUtil.notNull(userInfo, ExecptionMessageEnum.PURCHASER_INFO_NOT_FOUND);
        String jobnumber = userInfo.getJobnumber();
        Preconditions.notNull(jobnumber, "采购人工号信息为空，锐竞系统获取不到第三方人员信息！");
        List<DepartmentThirdPartyDTO> departmentThirdPartyDTOS = departmentRpcClient
                .findByUserIdAndOrgIdAndDepName(orderMasterDO.getFusercode(), jobnumber, orderMasterDO.getFbuydepartment());

        HNOrderBySelfBuyingReq.Data data = new HNOrderBySelfBuyingReq.Data();
        if (CollectionUtils.isNotEmpty(departmentThirdPartyDTOS)){
            DepartmentThirdPartyDTO huaNonInfo = departmentThirdPartyDTOS.get(0);
            data.setUser_id(huaNonInfo.getJobNumber());
            data.setGroup_id(huaNonInfo.getDepartmentId().toString());
        }

        List<OrderRemark> orderRemarks = orderRemarkMapper.selectByFtbuyappidIn(New.list(orderMasterDO.getFtbuyappid()));
        if (CollectionUtils.isNotEmpty(orderRemarks)) {
            OrderRemark orderRemark = orderRemarks.get(0);
            data.setNote(orderRemark.getRemark().length() > 250 ? orderRemark.getRemark().substring(0, 250) : orderRemark.getRemark());
        }

        String time = DateUtils.format("yyyy-MM-dd HH:mm:ss", orderMasterDO.getForderdate());
        data.setCtime(time);
        data.setPhone(orderMasterDO.getFbuyertelephone());
        data.setEmail(orderMasterDO.getFbuyeremail());
        data.setAddress(orderMasterDO.getFbiderdeliveryplace());
        data.setPostcode("暂无");
        data.setVendor_name(orderMasterDO.getFsuppname());
        Integer suppId = orderMasterDO.getFsuppid();

        List<OrderDetailDO> orderDetailDOS = orderDetailMapper.findByFmasterid(masterDOId);
        List<HNOrderBySelfBuyingReq.Data.item> itemList = orderDetailDOS.stream()
                .map(a->formatItem(orderMasterDO, a))
                .collect(toList());
        BigDecimal amount = orderDetailDOS.stream()
                .map(d -> d.getFbidprice().multiply(d.getFquantity()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        data.setPrice(amount.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
        data.setItems(itemList);
        req.setData(data);

        // 调用华农确认验收
        BaseResp<OrderResp> orderRespBaseResp = huaNongServiceClient.saveOrder(req, orderMasterDO.getForderno());
        DockingExtra extra = new DockingExtra();
        extra.setType(DockingTypeEnum.Order.getValue());
        extra.setStatusextra(StatusExtraEnum.NoStatus.getValue());
        extra.setInfo(req.getMall_voucher());
        extra.setExtraInfo(orderRespBaseResp.getData().getExtraOrderNo());
        extra.setUpdateTime(new Date());
        extra.setMemo("");
        dockingExtraMapper.insertSelective(extra);
    }

    /**
     * 华农完善订单明细信息
     * @param orderMasterDO
     * @param detail
     * @return
     */
    private HNOrderBySelfBuyingReq.Data.item formatItem(OrderMasterDO orderMasterDO, OrderDetailDO detail) {
        //工业品分类id
        final int INDUSTRIAL = 1615;
        Integer detailId = detail.getId();
        HNOrderBySelfBuyingReq.Data.item item = new HNOrderBySelfBuyingReq.Data.item();
        item.setName(detail.getFgoodname());
        item.setQuantity(detail.getFquantity().setScale(0));
        item.setUnit_price(detail.getFbidprice());
        item.setPrice(detail.getFquantity().multiply(detail.getFbidprice()).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
        item.setManufacturer("暂无");   //生产商名称
        item.setCatalog_no(detail.getFgoodcode());
        item.setPackages(detail.getFspec());
        item.setBrand(detail.getFbrand());
        item.setModel("暂无");  //型号
        item.setSpec(detail.getFspec());
        item.setOrigin("暂无"); //原产地
        item.setSource("暂无"); //分装/原装
        item.setLink("暂无");    //链接

        Integer orderType = orderMasterDO.getOrderType();
        // 商品快照描述表的枚举和订单详情表-单据来源的枚举 不一样
        Integer orderTypeForDesc;
        String detailIdForDesc;

        if (orderType.equals(OrderTypeEnum.PURCHASE_ORDER.getCode())) {
//            private final Integer ORDER_TYPE_FOR_DESC_PURCHASE = 1;
            orderTypeForDesc = 1;
            detailIdForDesc = orderMasterDO.getFtbuyappid().toString();
        } else {
//            private final Integer ORDER_TYPE_FOR_DESC_BID = 2;
            orderTypeForDesc = 2;
            detailIdForDesc = detailId.toString();
        }
        List<ProductDescriptionSnapshotDO> descSnapshotList = descSnapshotMapper.findProductDescByIds(detailIdForDesc,orderTypeForDesc,detail.getProductSn());
        if (CollectionUtils.isNotEmpty(descSnapshotList)) {
            item.setDescription(descSnapshotList.get(0).getDescription());
        }

        if (null == detail.getCategoryid()){
            return item;
        }
        final List<CategoryDTO> categoryDTOList = categoryServiceClient.queryListByCache(Arrays.asList(detailId.longValue()));
        if (CollectionUtils.isEmpty(categoryDTOList)) {
            return item;
        }
        final CategoryDTO categoryDTO = categoryDTOList.get(0);
        if (null == categoryDTO) {
            return item;
        }
        int firstCategoryId;
        boolean isFirstCategoryLevel = false;
        if (StringUtils.isBlank(categoryDTO.getPath())) {
            //path为空证明是一级分类
            firstCategoryId = categoryDTO.getId().intValue();
            isFirstCategoryLevel = true;
        } else {
            String[] categoryIdArr = categoryDTO.getPath().split("\\.");
            firstCategoryId = Integer.valueOf(categoryIdArr[0]);
        }

        switch (firstCategoryId) {
            //危险化学品、化学试剂
            case 460:
            case 51:
                item.setType("chem_reagent");
                // 获取危化品标签
                List<DangerousTagDO> dangerousTagDOS = dangerousTagDOMapper.selectByBusinessIdInAndBusinessType(New.list(detailId.toString()), DangerousTagEnum.ORDER_DETAIL.getValue());
                if (CollectionUtils.isNotEmpty(dangerousTagDOS)) {
                    DangerousTagDO dangerousTagDO = dangerousTagDOS.get(0);
                    item.setCasNo(dangerousTagDO.getCasNo());
                    item.setRgt_type(isFirstCategoryLevel ? StringUtils.EMPTY : DangerousTypeEnum.get(dangerousTagDO.getDangerousType()).getName());
                }
                break;

            //实验耗材、试验服务、元器件、农资类、仪器设备、办公用品、实验动物
            case 1:
            case 113:
            case 632:
            case 667:
            case 700:
            case 701:
            case 56:
            case INDUSTRIAL:
                item.setType("consumable");
                item.setRgt_type(isFirstCategoryLevel ? StringUtils.EMPTY : categoryDTO.getName());
                item.setCasNo("");
                break;

            // 生物试剂
            case 518:
                item.setType("bio_reagent");
                item.setRgt_type(isFirstCategoryLevel ? StringUtils.EMPTY : categoryDTO.getName());
                item.setCasNo("");
                break;
            default:
        }

        item.setCategory(item.getType());
        return item;
    }


    /**
     * 验收--审批
     * @param orderMasterDO 订单
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType=OperationType.WRITE)
    private void acceptToApproval(OrderMasterDO orderMasterDO, OrderReceiptParamDTO paramDTO){
        acceptApprovalClient.submitToApproval(new SubmitAcceptApproveRequestDTO()
                .setOrderId(orderMasterDO.getId())
                .setOrgCode(orderMasterDO.getFusercode())
                .setUserId(paramDTO.getUserId())
                .setUserName(paramDTO.getUserName())
                .setUserGuid(paramDTO.getUserGuid())
                .setPassword(paramDTO.getPassword()));
    }

    /**
     * 异步写 我的待审的数缓存
     * @param orderMasterDO
     * @param operatorId
     */
    @Deprecated
    private void asyncUpdateApprovalCount(OrderMasterDO orderMasterDO, Integer operatorId) {
        // 异步写 我的待审的数缓存
        Runnable task = () -> {
            List<DepartmentDTO> canApprovalDeptCollect = userClient.getDeptForUserByAccess(operatorId, orderMasterDO.getFuserid(), ConfigConstant.ORDER_APPROVE);
            // 待审列表只显示能审的单, 若canApprovalDeptCollect.isEmpty, 则deptId搜索条件传一个不存在的课题组id
            final List<Integer> canApprovalDeptIds = canApprovalDeptCollect.stream().map(DepartmentDTO::getId).collect(toList());
            if (CollectionUtils.isEmpty(canApprovalDeptIds)) {
                return;
            }
            if (canApprovalDeptIds.stream().anyMatch(f -> f.equals(orderMasterDO.getFbuydepartmentid()))) {
                final String uniKey = OrderSearchBoostServiceImpl.ORDER_APPROVAL_COUNT + ":" + orderMasterDO.getFuserid() + ":" + operatorId;
                try {
                    cacheClient.lockRetry(OrderSearchBoostServiceImpl.ORDER_APPROVAL_COUNT_LOCK, 3);
                    Map<OrderStatusEnum, Long> cache = (Map<OrderStatusEnum, Long>) cacheClient.getFromCache(uniKey);
                    if (cache == null) {
                        cache = new HashMap<OrderStatusEnum, Long>() {{
                            put(OrderStatusEnum.OrderReceiveApproval, 0L);
                        }};
                    }
                    cache.computeIfPresent(OrderStatusEnum.OrderReceiveApproval, (orderStatusEnum, count) -> count + 1);
                    cacheClient.setToCache(uniKey, cache, 3600);
                } catch (Exception e) {
                    LOGGER.error("获取分布式锁{}失败:{}", OrderSearchBoostServiceImpl.ORDER_APPROVAL_COUNT_LOCK, e);
                } finally {
                    cacheClient.removeCache(OrderSearchBoostServiceImpl.ORDER_APPROVAL_COUNT_LOCK);
                }
            }
        };
        AsyncExecutor.runAsync(task);
    }

    /**
     * 验收--完成（自结算、特数医院、平台运行经费）
     *
     * @param receiptOrderResponseDO 验收结果
     * @param orderMasterDO          订单主表数据
     * @param receiptConfigMap       验收配置
     * @param picUrl                 上传图片
     * @param orderDetailList        订单详情数据
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    private void acceptToFinish(ReceiptOrderResponseDO receiptOrderResponseDO, OrderMasterDO orderMasterDO, Map<String, String> receiptConfigMap, String picUrl, List<OrderDetailDO> orderDetailList) {
        boolean isUsedStore = this.parseBooleanConfig(receiptConfigMap, ConfigConstant.USE_WAREHOUSE_SYSTEM, ConfigConstant.USE_WAREHOUSE_SYSTEM_VALUE);
        LOGGER.info("订单收货，验收-结束模式，订单id为{}的订单状态更新到结束,是否使用库房系统:{}",orderMasterDO.getId(), isUsedStore);
        // 订单入库处理器策略, 现在是江西中医入库有定制业务
        OrderAcceptService customOrderAcceptService = BeanContainer.getBean(OrderAcceptService.class, OrderAcceptConstant.getAcceptStrategy().get(orderMasterDO.getFusercode()));
        if (customOrderAcceptService == null) {
            customOrderAcceptService = this;
        }
        InWarehouseModeEnum warehouseMode;
        if (isUsedStore) {
            // 计算入库模式
            warehouseMode = customOrderAcceptService.calculateInWarehouseMode(orderMasterDO, receiptConfigMap, orderDetailList);
        }else{
            // 不使用库房，为无需入库
            warehouseMode = InWarehouseModeEnum.NO_NEED;
        }
        // 根据入库模式进行相关入库处理
        WarehouseResultDTO warehouseResultDTO = customOrderAcceptService.executeInWareHouse(warehouseMode, orderMasterDO, orderDetailList, picUrl);
        receiptOrderResponseDO.setWarehouseResultDTO(warehouseResultDTO);
    }

    /**
     * 验收标准流程
     * @param receiptOrderResponseDO 验收结果
     * @param orderMasterDO     订单
     * @param receiptConfigMap  配置map
     * @param operatorId        操作人id
     * @param operatorName      操作人名称
     * @param orderDetailList   订单详情
     * @return AcceptResultBO
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    private void acceptNormal(ReceiptOrderResponseDO receiptOrderResponseDO,OrderMasterDO orderMasterDO, Map<String, String> receiptConfigMap, String picUrl, Integer operatorId, String operatorName,
                              List<OrderDetailDO> orderDetailList) {
        // 当前单位是否使用结算系统
        boolean isUsedStatement = orderStatementService.isUseStatement(receiptConfigMap, orderMasterDO);
        // 是否使用库房
        boolean isUsedStore = this.parseBooleanConfig(receiptConfigMap, ConfigConstant.USE_WAREHOUSE_SYSTEM, ConfigConstant.USE_WAREHOUSE_SYSTEM_VALUE);
        LOGGER.info("订单收货，验收-无审批模式，订单id为{}的订单状态更新到待结算，是否使用库房系统:{},是否使用结算系统:{}",orderMasterDO.getId(), isUsedStore, isUsedStatement);
        // 当前库房状态
        InventoryStatusEnum inventoryStatusEnum = InventoryStatusEnum.getByCode(orderMasterDO.getInventoryStatus().intValue());
        inventoryStatusEnum = inventoryStatusEnum == null ? InventoryStatusEnum.WAITING_FOR_STORE : inventoryStatusEnum;
        // 入库模式
        InWarehouseModeEnum warehouseMode;
        // 1.使用入库且订单当前库房状态非待入库。防止重新操作或三院临床特殊逻辑（三院临床先入库，扫码入库完成再调用自动收货进行收货操作）,不再调用库房接口
        if(InventoryStatusEnum.WAITING_FOR_STORE.equals(inventoryStatusEnum)){
            // 订单入库处理器策略
            OrderAcceptService customOrderAcceptService = BeanContainer.getBean(OrderAcceptService.class, OrderAcceptConstant.getAcceptStrategy().get(orderMasterDO.getFusercode()));
            if (customOrderAcceptService == null) {
                customOrderAcceptService = this;
            }
            if (isUsedStore) {
                // 判断入库模式
                warehouseMode = customOrderAcceptService.calculateInWarehouseMode(orderMasterDO, receiptConfigMap, orderDetailList);
            }else {
                // 不使用库房系统，则入库模式为无须入库
                warehouseMode = InWarehouseModeEnum.NO_NEED;
            }

            // 根据入库模式调用库房
            WarehouseResultDTO warehouseResultDTO = customOrderAcceptService.executeInWareHouse(warehouseMode, orderMasterDO, orderDetailList, picUrl);
            inventoryStatusEnum = warehouseResultDTO.getInventoryStatus();
            receiptOrderResponseDO.setWarehouseResultDTO(warehouseResultDTO);
        }
        
        // 2.结算
        // 先入库后收货（扫码入库） || 不用依赖入库回调 
        boolean goAhead = !isUsedStore || InventoryStatusEnum.COMPLETE.equals(inventoryStatusEnum) || InventoryStatusEnum.NOT_INBOUND.equals(inventoryStatusEnum);
        if (goAhead) {
            orderStatementService.orderStatementCore(orderMasterDO, operatorId, operatorName, isUsedStatement, inventoryStatusEnum.getCode());
        }
    }

    /**
     * 计算入库模式
     * 
     * @param orderMasterDO 订单主表信息
     * @param receiptConfigMap 验收配置
     * @param orderDetailList 订单商品详情
     * @return 入库模式
     */
    @Override
    public InWarehouseModeEnum calculateInWarehouseMode(OrderMasterDO orderMasterDO, Map<String, String> receiptConfigMap, List<OrderDetailDO> orderDetailList) {

        boolean isUsedNewWareHouse = this.parseBooleanConfig(receiptConfigMap, 
                ConfigConstant.WAREHOUSE_SYSTEM_VERSION_CODE, ConfigConstant.WAREHOUSE_SYSTEM_VERSION_VALUE);
        //旧入库 只有中大使用，不进行维护
        if (isUsedNewWareHouse) {
            // 新库房是否自动入库
            boolean isAutoStore = wmsRuleRpcServiceClient.getNewWareHouseConfig(orderMasterDO.getFuserid());
            if (isAutoStore) {
                return InWarehouseModeEnum.NEW_WAREHOUSE_SYSTEM_AUTO_INBOUND;
            }
        }
        return InWarehouseModeEnum.WAITING_INBOUND;
    }

    /**
     * 根据入库模式执行入库
     *
     * @param inWarehouseModeEnum   入库模式
     * @param orderMasterDO   订单主表
     * @param orderDetailList 订单详情
     * @return 入库状态
     */
    @Override
    public WarehouseResultDTO executeInWareHouse(InWarehouseModeEnum inWarehouseModeEnum, OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, String picUrl) {
        WarehouseResultDTO warehouseResultDTO = new WarehouseResultDTO();
        warehouseResultDTO.setWarehouseSuccess(true);
        
        Integer orderId = orderMasterDO.getId();
        LOGGER.info("订单id:{},验收审批,入库模式: {}", orderId , inWarehouseModeEnum);
        
        InventoryStatusEnum inventoryStatusEnum;
        UpdateOrderParamDTO updateInventoryStatusDTO = new UpdateOrderParamDTO();
        updateInventoryStatusDTO.setOrderId(orderId);
        switch (inWarehouseModeEnum){
            case NO_NEED:
                inventoryStatusEnum = InventoryStatusEnum.NOT_INBOUND;
                // 如果是需要入库推送时修改入库状态的单位，则改为入库推送中
                if (WarehouseCallbackServiceImpl.ORG_CHANGE_INVENTORY_STATUS.contains(orderMasterDO.getFusercode())) {
                    inventoryStatusEnum = InventoryStatusEnum.WAITING_FOR_CALLBACK;
                }
                updateInventoryStatusDTO.setInventoryStatus(inventoryStatusEnum.getCode());
                orderMasterMapper.updateOrderById(updateInventoryStatusDTO);
                // 更新为无需入库
                AsyncExecutor.runAsync(() ->{
                    List<OrderUniqueBarCodeDTO> barCodeDTOList = orderUniqueBarCodeRPCClient.findByOrderNo(orderMasterDO.getForderno(), New.list(UniqueBarCodeTypeEnum.ORG.getCode()));
                    List<String> notReturnBarcodeList = barCodeDTOList.stream().filter(item-> OrderProductTransactionStatusEnum.RETURNED.getCode() != item.getTransactionStatus()).map(OrderUniqueBarCodeDTO::getUniBarCode).collect(Collectors.toList());
                    orderUniqueBarCodeRPCClient.updateStatusByBarcode(notReturnBarcodeList, null, OrderProductInventoryStatusEnum.NO_NEED.getCode());
                });
                break;
            case WAITING_INBOUND:
                // 待入库
                inventoryStatusEnum = InventoryStatusEnum.WAITING_FOR_STORE;
                updateInventoryStatusDTO.setInventoryStatus(inventoryStatusEnum.getCode());
                orderMasterMapper.updateOrderById(updateInventoryStatusDTO);
                break;
            case NEW_WAREHOUSE_SYSTEM_AUTO_INBOUND:
                // 新库房自动入库
                inventoryStatusEnum = this.autoInBoundWithNewWarehouseSystem(orderMasterDO, orderDetailList, picUrl, warehouseResultDTO);
                break;
            case NEW_WAREHOUSE_SYSTEM_SAVE_APPLICATION_FORM:
                // 新库房批量插入入库申请单
                inventoryStatusEnum = this.batchSaveApplicationFormToNewWarehouseSystem(orderMasterDO, orderDetailList, null);
                break;
            default:
                inventoryStatusEnum = InventoryStatusEnum.WAITING_FOR_STORE;
                break;
        }
        warehouseResultDTO.setInventoryStatus(inventoryStatusEnum);
        return warehouseResultDTO;
    }

    /**
     * 向新库房系统提交自动入库
     * @param orderMasterDO 订单主表信息
     * @param orderDetailList 订单商品详情信息
     * @param warehouseResultDTO 入库结果
     * @return 最终库房状态
     */
    protected InventoryStatusEnum autoInBoundWithNewWarehouseSystem(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, String picUrl, WarehouseResultDTO warehouseResultDTO) {
        InventoryStatusEnum inventoryStatusEnum = InventoryStatusEnum.WAITING_FOR_STORE;
        try {
            //用户信息
            UserBaseInfoDTO userInfo = userClient.getUserInfo(orderMasterDO.getFbuyerid(), orderMasterDO.getFuserid());
            bizWareHouseClient.autoInbound(orderMasterDO, orderDetailList, userInfo, picUrl);
        } catch (Exception e) {
            // 自动入库异常, 状态为待入库, 用户页面手动提交
            LOGGER.error("自动入库失败: \n", e);
            Cat.logError(CAT_TYPE, "autoInbound", "自动入库失败", e);
            warehouseResultDTO.setWarehouseSuccess(false);
            warehouseResultDTO.setWarehouseErrorMsg(e.getMessage());
        }
        return inventoryStatusEnum;
    }

    /**
     * 向新库房系统插入入库申请单
     * @param orderMasterDO 订单主表信息
     * @param orderDetailList 订单详情信息
     * @param warehouseRoomId 申请入库的库房id
     * @return 最终库房状态
     */
    protected InventoryStatusEnum batchSaveApplicationFormToNewWarehouseSystem(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, Integer warehouseRoomId) {
        
        RjSessionInfo rjSessionInfo = (RjSessionInfo) RpcContext.getProviderContext().getCallAttachment(GatewayConstant.SESSION_INFO);;
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.OPERATION_FAILED_UNAUTHORIZED);
        LoginUserInfoBO loginUserInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), orderMasterDO.getFuserid());

        BusinessErrUtil.notEmpty(orderDetailList, ExecptionMessageEnum.ORDER_ID_PRODUCT_NOT_FOUND, orderMasterDO.getId());
        UserBaseInfoDTO purchaser = userClient.getNotNullUserDetailById(orderMasterDO.getFbuyerid());

        List<BizWarehouseEntryDTO> bizWarehouseEntryDTOList = new ArrayList<>(orderDetailList.size());
        BizWarehouseEntryDTO bizWarehouseEntryDTO = new BizWarehouseEntryDTO();
        bizWarehouseEntryDTO.setRoomId(warehouseRoomId);
        bizWarehouseEntryDTO.setStatus(InboundStatus.NOTINSTORAGE.getValue());
        bizWarehouseEntryDTO.setApplyUserName(loginUserInfo.getUserName());
        bizWarehouseEntryDTO.setApprovalStatus(ApprovalTaskStatusEnum.APPROVALING.getValue());
        bizWarehouseEntryDTO.setOrderNo(orderMasterDO.getForderno());
        bizWarehouseEntryDTO.setDeptId(orderMasterDO.getFbuydepartmentid());
        bizWarehouseEntryDTO.setDeptName(orderMasterDO.getFbuydepartment());
        bizWarehouseEntryDTO.setApplyUserGuid(loginUserInfo.getUserGuid());
        bizWarehouseEntryDTO.setOrgId(orderMasterDO.getFuserid());
        bizWarehouseEntryDTO.setPurchaseUserGuid(purchaser.getGuid());
        bizWarehouseEntryDTO.setPurchaseUserName(purchaser.getName());
        bizWarehouseEntryDTO.setSpecies(orderMasterDO.getSpecies().intValue());
        bizWarehouseEntryDTOList.add(bizWarehouseEntryDTO);

        //入库申请单关联的商品列表如果为空就新起一个
        List<BizWarehouseEntryDetailDTO> bizWarehouseEntryDetailDTOList = bizWarehouseEntryDTO.getDetailDTOList();
        if (CollectionUtils.isEmpty(bizWarehouseEntryDetailDTOList)) {
            bizWarehouseEntryDetailDTOList = new ArrayList<>();
            bizWarehouseEntryDTO.setDetailDTOList(bizWarehouseEntryDetailDTOList);
        }

        for (OrderDetailDO orderDetailDO : orderDetailList) {
            OrderDetailDTO orderDetailDTO = OrderDetailTranslator.orderDetailDOToTorderDetailDTO(orderDetailDO);
            BizWarehouseEntryDetailDTO bizWarehouseEntryDetailDTO = new BizWarehouseEntryDetailDTO();
            bizWarehouseEntryDetailDTO.setProductCode(orderDetailDTO.getFgoodcode());
            bizWarehouseEntryDetailDTO.setBrandName(orderDetailDTO.getFbrand());
            bizWarehouseEntryDetailDTO.setCasNo(orderDetailDTO.getCasno());
            bizWarehouseEntryDetailDTO.setDangerousType(OrderDetailsUtil.getDangerousType(orderDetailDTO.getDangerousTypeId()));
            bizWarehouseEntryDetailDTO.setProductName(orderDetailDTO.getFgoodname());
            bizWarehouseEntryDetailDTO.setSpecifications(orderDetailDTO.getFspec());
            bizWarehouseEntryDetailDTO.setSuppId(orderMasterDO.getFsuppid());
            bizWarehouseEntryDetailDTO.setSuppName(orderMasterDO.getFsuppname());

            bizWarehouseEntryDetailDTO.setMeasurementNum(BigDecimal.valueOf(OrderDetailsUtil.getRealOrderProductQuantity(orderDetailDTO)));
            bizWarehouseEntryDetailDTO.setMeasurementUnit(orderDetailDTO.getFunit());
            //应收数量暂时与实收数量相等
            int realQuantity = OrderDetailsUtil.getRealOrderProductQuantity(orderDetailDTO);
            bizWarehouseEntryDetailDTO.setReceivableNum(realQuantity);
            bizWarehouseEntryDetailDTO.setReceivedNum(realQuantity);
            bizWarehouseEntryDetailDTO.setReceivedUnit(orderDetailDTO.getFunit());
            //这里商品图片orderDetailDTO里没有，只能从请求参数获取
            bizWarehouseEntryDetailDTO.setFpicpath(orderDetailDTO.getFpicpath());
            bizWarehouseEntryDetailDTO.setControlFlag(orderDetailDTO.getRegulatoryTypeId());
            bizWarehouseEntryDetailDTO.setCategoryId(orderDetailDTO.getFirstCategoryId());
            bizWarehouseEntryDetailDTO.setSort(orderDetailDO.getFgoodname());
            BigDecimal realTotalPrice = OrderDetailsUtil.getRealOrderProductPrice(orderDetailDTO);
            // 总价（含差价）
            BigDecimal totalPriceWithRemainderPrice = realTotalPrice.add(orderDetailDTO.getRemainderPrice());
            bizWarehouseEntryDetailDTO.setPrice(totalPriceWithRemainderPrice);
            bizWarehouseEntryDetailDTO.setUnitPrice(orderDetailDTO.getFbidprice());
            bizWarehouseEntryDetailDTO.setOrderDetailId(orderDetailDTO.getId());
            bizWarehouseEntryDetailDTO.setFirstCategoryId(orderDetailDTO.getFirstCategoryId());
            bizWarehouseEntryDetailDTO.setSecondCategoryId(orderDetailDTO.getSecondCategoryId());
            bizWarehouseEntryDetailDTOList.add(bizWarehouseEntryDetailDTO);
        }
        try{
            bizWareHouseClient.checkIncompatibility(bizWarehouseEntryDTOList, false);
            bizWareHouseClient.batchSaveWarehouseApplication(bizWarehouseEntryDTOList);
        } catch (Exception e){
            LOGGER.error(orderMasterDO.getForderno() + "验收时调用批量插入入库单失败", e);
        }
        return InventoryStatusEnum.WAITING_FOR_STORE;
    }

    /**
     * 校验附件上传规则
     *
     * @param receiptConfigMap  oms配置映射
     * @param attachmentList 验收附件列表（可为空）
     * @param orderDetailList   订单详情列表（不可为空）
     */
    @Override
    public void validationAttachment(Map<String, String> receiptConfigMap,
                                      List<AttachmentDTO> attachmentList,
                                      List<AttachmentDTO> orderVideoAttachmentList,
                                      List<OrderDetailDO> orderDetailList) {
        this.checkUploadAttachmentOmsConfig(receiptConfigMap, attachmentList, orderVideoAttachmentList, orderDetailList);
        BusinessErrUtil.isTrue(CollectionUtils.isEmpty(attachmentList) || attachmentList.size() <= OrderAcceptConstant.UPLOAD_RECEIVE_ATTACHMENT, ExecptionMessageEnum.UPLOAD_IMAGES_BEFORE_SUBMIT, OrderAcceptConstant.UPLOAD_RECEIVE_ATTACHMENT);

    }

    /**
     * 是否需要上传验收附件 从oms读取配置
     *
     * @param receiptConfigMap  oms配置映射（不可为空）
     * @param orderDetailList   订单详情列表（不可为空）
     * @return
     */
    private void checkUploadAttachmentOmsConfig(Map<String, String> receiptConfigMap,
                                                List<AttachmentDTO> otherAttachmentList,
                                                List<AttachmentDTO> orderVideoAttachmentListList,
                                                List<OrderDetailDO> orderDetailList) {
        List<String> attachmentConfigList = New.list(ConfigConstant.OTHER_ORG_RECEIPT_ATTACHMENT, ConfigConstant.ORDER_VIDEO_ATTACHMENT);
        for (String attachmentConfig : attachmentConfigList) {
            String configVal = receiptConfigMap.get(attachmentConfig);
            List<AttachmentDTO> attachmentList = attachmentConfig.equals(ConfigConstant.OTHER_ORG_RECEIPT_ATTACHMENT) ? otherAttachmentList : orderVideoAttachmentListList;
            switch (configVal) {
                case "1":
                    //无需上传
                    BusinessErrUtil.isTrue(CollectionUtils.isEmpty(attachmentList), ExecptionMessageEnum.NO_NEED_TO_UPLOAD_ATTACHMENTS);
                    break;
                case "2":
                    //非必须上传
                    break;
                case "3":
                    if (ConfigConstant.OTHER_ORG_RECEIPT_ATTACHMENT.equals(attachmentConfig)) {
                        //包含服务类商品，其他附件为必传
                        Set<InboundTypeEnum> serviceCategorySet = orderDetailList.stream()
                                .filter(orderDetailDO -> !GoodsReturnStatusEnum.RETURNED_GOODS.getCode().equals(orderDetailDO.getReturnStatus()))
                                .map(orderDetailDO -> InboundTypeEnum.descOf(orderDetailDO.getCategoryTag()))
                                .filter(d -> InboundTypeEnum.SERVICE.equals(d))
                                .collect(Collectors.toSet());
                        BusinessErrUtil.isTrue(!(CollectionUtils.isNotEmpty(serviceCategorySet) && CollectionUtils.isEmpty(attachmentList)), ExecptionMessageEnum.SERVICE_GOODS_ATTACHMENTS_REQUIRED);
                    }
                    if (ConfigConstant.ORDER_VIDEO_ATTACHMENT.equals(attachmentConfig)) {
                        BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(orderVideoAttachmentListList), ExecptionMessageEnum.UPLOAD_ATTACHMENTS_VIDEO_BEFORE_SUBMITTING);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 将一物交易状态改为验收
     * @param orderNo 订单号
     */
    private void updateBarcodeStatusToReceived(String orderNo){
        AsyncExecutor.runAsync(() ->{
            List<OrderUniqueBarCodeDTO> barCodeDTOList = orderUniqueBarCodeRPCClient.findByOrderNo(orderNo, New.list(UniqueBarCodeTypeEnum.ORG.getCode()));
            List<String> notReturnBarcodeList = barCodeDTOList.stream().filter(item-> OrderProductTransactionStatusEnum.RETURNED.getCode() != item.getTransactionStatus()).map(OrderUniqueBarCodeDTO::getUniBarCode).collect(Collectors.toList());
            orderUniqueBarCodeRPCClient.updateStatusByBarcode(notReturnBarcodeList, OrderProductTransactionStatusEnum.RECEIVED.getCode(), null);
        });
    }

    /**
     * 是否订单验收
     * @param orderId 订单id
     * @return 是否订单验收
     */
    private boolean isOrderAcceptance(Integer orderId){
        List<BaseOrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(New.list(orderId), OrderExtraEnum.ACCEPTANCE_WAY.getValue());
        if(CollectionUtils.isEmpty(orderExtraDTOList)){
            return true;
        }
        return OrderAcceptanceWayEnum.NORMAL.value.toString().equals(orderExtraDTOList.get(0).getExtraValue());
    }

    /**
     * 获取订单额外信息
     *
     * @param orderId 订单ID
     */
    private Map<Integer, String> getOrderExtraMap(Integer orderId) {
        List<OrderExtraDTO> orderExtraDTOS = orderExtraClient.selectByOrderIdAndExtraKey(New.list(orderId), New.list(OrderExtraEnum.STOCK_WAREHOUSE_TYPE.getValue()));
        Map<Integer, List<OrderExtraDTO>> orderIdExtraListMap = DictionaryUtils.groupBy(orderExtraDTOS, OrderExtraDTO::getOrderId);
        List<OrderExtraDTO> orderExtraDTOList = orderIdExtraListMap.get(orderId);
        Map<Integer, String> extraKeyValueMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(orderExtraDTOList)) {
            extraKeyValueMap = DictionaryUtils.toMap(orderExtraDTOList, OrderExtraDTO::getExtraKey, OrderExtraDTO::getExtraValue);
        }
        return MapUtils.isNotEmpty(extraKeyValueMap) ? extraKeyValueMap : Collections.emptyMap();
    }



}
