package com.ruijing.store.order.base.docking.model;

import java.util.Date;

/**
 * <AUTHOR> zhukai
 * @create: 2019/09/25
 */
public class DockingExtra {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date creationTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 对接类型，0：订单
     */
    private Integer type;

    /**
     * 我方信息
     */
    private String info;

    /**
     * 敌方信息
     */
    private String extraInfo;

    /**
     * 结算单状态:  (0, "订单", "默认"),(202, "结算单", "已提交"),(203, "结算单", "财务处理中"),(203, "结算单", "结束")
     */
    private Integer statusextra;

    /**
     * 对接情况
     */
    private String memo;

    public DockingExtra(String info, String extraInfo, Integer statusextra, String memo) {
        this.info = info;
        this.extraInfo = extraInfo;
        this.statusextra = statusextra;
        this.memo = memo;
    }

    public DockingExtra() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo;
    }

    public Integer getStatusextra() {
        return statusextra;
    }

    public void setStatusextra(Integer statusextra) {
        this.statusextra = statusextra;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", creationTime=").append(creationTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", type=").append(type);
        sb.append(", info=").append(info);
        sb.append(", extraInfo=").append(extraInfo);
        sb.append(", statusextra=").append(statusextra);
        sb.append(", memo=").append(memo);
        sb.append("]");
        return sb.toString();
    }
}