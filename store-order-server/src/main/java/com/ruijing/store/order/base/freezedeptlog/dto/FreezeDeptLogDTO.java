package com.ruijing.store.order.base.freezedeptlog.dto;

import com.google.common.base.Objects;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: FreezeDeptLog DTO
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/10/29 17:57
 **/
public class FreezeDeptLogDTO implements Serializable {

    private static final long serialVersionUID = -4544108395345555505L;
    /**
     * id
     */
    private Long id;

    /**
     * 医院ID
     */
    private Integer orgId;

    /**
     * 部门id
     */
    private Integer depId;

    /**
     * 冻结类型 1-结算 2-验收
     */
    private Integer type;

    /**
     * 是否被删除
     */
    private Integer hasDeleted;

    /**
     * 创建时间
     */
    private Date creationTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    public FreezeDeptLogDTO() {
    }

    public FreezeDeptLogDTO(Integer orgId, Integer depId, Integer type, Integer hasDeleted, Date creationTime) {
        this.orgId = orgId;
        this.depId = depId;
        this.type = type;
        this.hasDeleted = hasDeleted;
        this.creationTime = creationTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getDepId() {
        return depId;
    }

    public void setDepId(Integer depId) {
        this.depId = depId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getHasDeleted() {
        return hasDeleted;
    }

    public void setHasDeleted(Integer hasDeleted) {
        this.hasDeleted = hasDeleted;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orgId=").append(orgId);
        sb.append(", depId=").append(depId);
        sb.append(", type=").append(type);
        sb.append(", hasDeleted=").append(hasDeleted);
        sb.append(", creationTime=").append(creationTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FreezeDeptLogDTO that = (FreezeDeptLogDTO) o;
        return Objects.equal(id, that.id) &&
                Objects.equal(orgId, that.orgId) &&
                Objects.equal(depId, that.depId) &&
                Objects.equal(type, that.type) &&
                Objects.equal(hasDeleted, that.hasDeleted) &&
                Objects.equal(creationTime, that.creationTime) &&
                Objects.equal(updateTime, that.updateTime);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id, orgId, depId, type, hasDeleted, creationTime, updateTime);
    }
}
