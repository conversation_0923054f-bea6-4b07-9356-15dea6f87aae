package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.shop.goods.api.dto.BaseProductDTO;
import com.ruijing.shop.goods.api.dto.ProductUniqDTO;
import com.ruijing.shop.goods.api.service.BizShopProductService;
import com.ruijing.shop.shopcommon.base.ApiResult;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品的rpc服务
 */
@ServiceClient
public class ProductClient {

    @MSharpReference(remoteAppkey = "shop-goods-service")
    private BizShopProductService bizShopProductService;

    /**
     * 先使用COMMON_SERVICE类型调用吧，客户端的日志切面后续优化再改
     * @param ids 商品 productSn => id
     * @return
     */
    @ServiceLog(description = "根据商品id查询商品信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<BaseProductDTO> findByIdList(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<ProductUniqDTO> request = ids.stream().map(id -> {
            ProductUniqDTO item = new ProductUniqDTO();
            item.setId(id);
            return item;
        }).collect(Collectors.toList());

        ApiResult<List<BaseProductDTO>> response = bizShopProductService.getByIds(request);
        Preconditions.isTrue(response.successful(), response.getMessage());
        return response.getData();
    }

    @ServiceLog(description = "根据商品id查询商品信息(支持查询短期(两个月内)内删除的商品)", serviceType = ServiceType.COMMON_SERVICE)
    public List<BaseProductDTO> findByIdListWithDeleted(List<Long> ids, Integer suppId) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<ProductUniqDTO> request = ids.stream().map(id -> {
            ProductUniqDTO item = new ProductUniqDTO();
            item.setId(id);
            item.setSupplierId(suppId);
            return item;
        }).collect(Collectors.toList());

        ApiResult<List<BaseProductDTO>> response = bizShopProductService.getByIds(request);
        Preconditions.isTrue(response.successful(), response.getMessage());
        return response.getData();
    }

    /**
     * 根据单个商品id查询商品信息
     * @param
     * @return
     */
    @ServiceLog(description = "根据单个商品id查询商品信息", serviceType = ServiceType.COMMON_SERVICE)
    public BaseProductDTO findById(ProductUniqDTO productUniqDTO) {
        ApiResult<BaseProductDTO> remoteResponse = bizShopProductService.load(productUniqDTO);
        Preconditions.isTrue(remoteResponse.successful(), remoteResponse.getMessage());
        return remoteResponse.getData();
    }
}
