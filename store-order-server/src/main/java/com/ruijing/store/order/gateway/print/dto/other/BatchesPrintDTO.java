package com.ruijing.store.order.gateway.print.dto.other;

import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.StringJoiner;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-09-20 11:33
 * @description:
 */
@Model("批次打印信息")
public class BatchesPrintDTO implements Serializable {

    private static final long serialVersionUID = -5060298601492868687L;

    /**
     * 批号
     */
    @ModelProperty("批号")
    private String batches;

    /**
     * 有效期
     */
    @ModelProperty("有效期")
    private String expiration;

    @ModelProperty("生产厂家")
    private String manufacturer;

    @ModelProperty("生产日期")
    private Date productionDate;

    @ModelProperty("数量")
    private Integer quantity;

    public String getBatches() {
        return batches;
    }

    public BatchesPrintDTO setBatches(String batches) {
        this.batches = batches;
        return this;
    }

    public String getExpiration() {
        return expiration;
    }

    public BatchesPrintDTO setExpiration(String expiration) {
        this.expiration = expiration;
        return this;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public BatchesPrintDTO setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
        return this;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public BatchesPrintDTO setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
        return this;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public BatchesPrintDTO setQuantity(Integer quantity) {
        this.quantity = quantity;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", BatchesPrintDTO.class.getSimpleName() + "[", "]")
                .add("batches='" + batches + "'")
                .add("expiration='" + expiration + "'")
                .add("manufacturer='" + manufacturer + "'")
                .add("productionDate=" + productionDate)
                .add("quantity=" + quantity)
                .toString();
    }
}
