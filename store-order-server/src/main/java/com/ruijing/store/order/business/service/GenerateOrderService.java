package com.ruijing.store.order.business.service;

import com.ruijing.store.order.api.base.other.dto.GenerateOrderDTO;
import com.ruijing.store.order.api.base.other.dto.GenerateOrderResultDTO;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2025-04-14 11:08
 * @description:
 */
public interface GenerateOrderService {

    /**
     * 生成订单
     */
    List<GenerateOrderResultDTO> generateNewOrder(List<GenerateOrderDTO> generateOrderDTOList);
}
