<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.core.model.OrderApprovalLog">
    <!--@mbg.generated-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="photo" jdbcType="VARCHAR" property="photo" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="approve_status" jdbcType="TINYINT" property="approveStatus" />
    <result column="approve_level" jdbcType="INTEGER" property="approveLevel" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName"/>
    <result column="op_user_type" jdbcType="INTEGER" property="opUserType" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_id, photo, reason, approve_status, approve_level, operator_id, operator_name, op_user_type, creation_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_order_approval_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from t_order_approval_log
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.core.model.OrderApprovalLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_order_approval_log (order_id, photo, reason, 
      approve_status, operator_id, operator_name, op_user_type, creation_time
      )
    values (#{orderId,jdbcType=INTEGER},
      #{photo,jdbcType=VARCHAR},
      #{reason,jdbcType=VARCHAR},
      #{approveStatus,jdbcType=TINYINT},
      #{operatorId,jdbcType=INTEGER},
      #{operatorName,jdbcType=VARCHAR},
      <choose>
          <when test="opUserType != null ">
              #{opUserType,jdbcType=INTEGER},
          </when>
          <otherwise>
              0,
          </otherwise>
      </choose>
      #{creationTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.core.model.OrderApprovalLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_order_approval_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="photo != null">
        photo,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="approveStatus != null">
        approve_status,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
        <if test="operatorName != null">
            operator_name,
        </if>
        <if test="opUserType != null">
            op_user_type,
        </if>
      <if test="creationTime != null">
        creation_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="photo != null">
        #{photo,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="approveStatus != null">
        #{approveStatus,jdbcType=TINYINT},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
        <if test="operatorName != null">
            #{operatorName,jdbcType=VARCHAR},
        </if>
        <if test="opUserType != null">
            #{opUserType,jdbcType=INTEGER},
        </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.core.model.OrderApprovalLog">
    <!--@mbg.generated-->
    update t_order_approval_log
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="photo != null">
        photo = #{photo,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="approveStatus != null">
        approve_status = #{approveStatus,jdbcType=TINYINT},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
        <if test="operatorName != null">
            operatorName = #{operatorName,jdbcType=VARCHAR},
        </if>
        <if test="opUserType != null">
            op_user_type = #{opUserType,jdbcType=INTEGER},
        </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

    <update id="updateByPrimaryKeyListSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        <foreach collection="list" item="item" index="index" separator=";">
            update t_order_approval_log
            <set>
                <if test="item.operatorId != null">
                    operator_id = #{item.operatorId,jdbcType=INTEGER},
                </if>
                <if test="item.opUserType != null">
                    op_user_type = #{item.opUserType,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>

<!--auto generated by MybatisCodeHelper on 2019-12-31-->
    <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
        <foreach collection="list" item="element" index="index" separator=";">
            INSERT INTO t_order_approval_log
            <trim prefix="(" suffix=")" suffixOverrides=",">
                order_id,
                photo,
                reason,
                approve_status,
                operator_id,
                operator_name,
                <if test="element.opUserType != null">
                    op_user_type,
                </if>
                <if test="element.creationTime != null">
                    creation_time,
                </if>
            </trim>
            VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{element.orderId,jdbcType=INTEGER},
                #{element.photo,jdbcType=VARCHAR},
                #{element.reason,jdbcType=VARCHAR},
                #{element.approveStatus,jdbcType=TINYINT},
                #{element.operatorId,jdbcType=INTEGER},
                #{element.operatorName,jdbcType=VARCHAR},
                <if test="element.opUserType != null">
                    #{element.opUserType,jdbcType=INTEGER},
                </if>
                <if test="element.creationTime != null">
                    #{element.creationTime,jdbcType=TIMESTAMP},
                </if>
            </trim>
        </foreach>
    </insert>

<!--auto generated by MybatisCodeHelper on 2020-07-13-->
  <select id="findByOrderIdDesc" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_order_approval_log
        where order_id=#{orderId,jdbcType=INTEGER}
        order by id desc
    </select>

<!--auto generated by MybatisCodeHelper on 2020-08-03-->
  <select id="findByOrderIdInAndApproveStatusIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_order_approval_log
        where order_id in
        <foreach item="item" index="index" collection="orderIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>

        <if test="approveStatusCollection != null and approveStatusCollection.size() != 0">
            and approve_status in
            <foreach item="item" index="index" collection="approveStatusCollection"
                     open="(" separator="," close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>

    </select>

<!--auto generated by MybatisCodeHelper on 2021-01-13-->
  <select id="findByOrderIdOrderByCreationTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_order_approval_log
        where order_id=#{orderId,jdbcType=INTEGER} order by creation_time asc
    </select>

    <update id="loopUpdateByIds">
        <foreach item="item" index="index" collection="list"
                 open="" separator=";">
            update t_order_approval_log
            <set>
                order_id = #{item.orderId,jdbcType=INTEGER},
                <if test="item.creationTime != null">
                    creation_time = #{item.creationTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.reason != null">
                    reason = #{item.reason,jdbcType=VARCHAR},
                </if>

            </set>
            where id = #{item.id,jdbcType=INTEGER}
        </foreach>

    </update>
</mapper>