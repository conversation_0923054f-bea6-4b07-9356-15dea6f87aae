package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;

import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/13 17:08
 * @description
 */
@Model("每一级的审批人")
public class OrderApprovalManItemVO implements Serializable {
    
    private static final long serialVersionUID = -7796776546580342292L;

    @ModelProperty("审批等级")
    private Integer level;

    @ModelProperty("有权限审批的人")
    private List<String> manList;
    
    @ModelProperty("操作时间，已办会返回，代办则不返回")
    private Date operateTime;

    public Integer getLevel() {
        return level;
    }

    public OrderApprovalManItemVO setLevel(Integer level) {
        this.level = level;
        return this;
    }

    public List<String> getManList() {
        return manList;
    }

    public OrderApprovalManItemVO setManList(List<String> manList) {
        this.manList = manList;
        return this;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public OrderApprovalManItemVO setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
        return this;
    }

    @Override
    public String toString() {
        return "OrderApprovalManItemVO{" +
                "level=" + level +
                ", manList=" + manList +
                ", operateTime=" + operateTime +
                '}';
    }
}
