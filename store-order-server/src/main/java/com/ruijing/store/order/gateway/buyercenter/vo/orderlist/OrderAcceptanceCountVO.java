package com.ruijing.store.order.gateway.buyercenter.vo.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;


@RpcApi
public class OrderAcceptanceCountVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("待审批数量")
    private Integer pendingApprovalCount;

    @RpcModelProperty("已审批数量")
    private Integer approvedCount;

    public Integer getPendingApprovalCount() {
        return pendingApprovalCount;
    }

    public void setPendingApprovalCount(Integer pendingApprovalCount) {
        this.pendingApprovalCount = pendingApprovalCount;
    }

    public Integer getApprovedCount() {
        return approvedCount;
    }

    public void setApprovedCount(Integer approvedCount) {
        this.approvedCount = approvedCount;
    }

    @Override
    public String toString() {
        return "OrderAcceptanceCountVO{" +
                "pendingApprovalCount=" + pendingApprovalCount +
                ", approvedCount=" + approvedCount +
                '}';
    }
}
