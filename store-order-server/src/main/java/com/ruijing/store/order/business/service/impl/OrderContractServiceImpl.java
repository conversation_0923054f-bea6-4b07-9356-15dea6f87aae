package com.ruijing.store.order.business.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.risk.control.riskmessage.dto.RiskOrderListRequest;
import com.ruijing.fundamental.risk.control.riskmessage.dto.RiskOrderResponse;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.saturn.api.contract.dto.OrderContractQueryDTO;
import com.ruijing.order.saturn.api.contract.vo.OrderContractInfoVO;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.baseconfig.api.dto.typeConfig.orderContract.OrderContractConfig;
import com.ruijing.store.baseconfig.api.dto.typeConfig.orderContract.enums.OrderContractEntryDisplayEnum;
import com.ruijing.store.baseconfig.api.dto.typeConfig.orderContract.enums.OrderContractEntryDisplayPositionEnum;
import com.ruijing.store.baseconfig.api.dto.typeConfig.orderContract.enums.OrderUploadContractEnum;
import com.ruijing.store.baseconfig.api.dto.typeConfig.rules.RuleDTO;
import com.ruijing.store.baseconfig.api.dto.typeConfig.rules.enums.EnableStatusEnum;
import com.ruijing.store.baseconfig.api.dto.typeConfig.rules.enums.RuleSourceEnum;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.rpc.client.RiskControlClient;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.minor.mapper.OrderContractMapper;
import com.ruijing.store.order.base.minor.model.OrderContract;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.OrderConfigTemplateBO;
import com.ruijing.store.order.business.enums.myorderlist.OrderSpeciesEnum;
import com.ruijing.store.order.business.enums.myorderlist.OrderUploadStatusEnum;
import com.ruijing.store.order.business.service.OrderContractService;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.gateway.buyercenter.request.contract.ContractInfoRequest;
import com.ruijing.store.order.gateway.buyercenter.request.contract.UploadContractRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.contract.ContractInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderContractTemplateVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderDetailVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderMasterVO;
import com.ruijing.store.order.rpc.client.OrderContractClient;
import com.ruijing.store.order.rpc.client.StoreRuleClient;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.rule.dto.PurchaseRuleRequestDTO;
import com.ruijing.store.rule.dto.PurchaseRuleResultDTO;
import com.ruijing.store.rule.dto.RuleProductVerificationDTO;
import com.ruijing.store.rule.enums.CheckNodeTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

/**
 * 订单合同服务
 */
@Service
public class OrderContractServiceImpl implements OrderContractService {

    private static final Logger logger = LoggerFactory.getLogger(OrderContractServiceImpl.class);

    @Resource
    private RiskControlClient riskControlClient;

    @Resource
    private StoreRuleClient storeRuleClient;

    @Resource
    private OrderContractMapper orderContractMapper;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private OrderContractClient orderContractClient;

    @Resource
    private SysConfigClient sysConfigClient;

    /**
     * 确定订单合同上传状态
     *
     * @param orderMasterList    订单主表数据列表
     * @param orderDetailMap     订单详情数据映射，key为订单ID，value为订单详情列表
     * @param contractConfigJson 合同配置
     * @param orgId              单位ID
     * @return 订单ID与上传状态的映射
     */
    @Override
    public Map<Integer, Integer> getOrderContractUploadStatus(List<OrderMasterDO> orderMasterList,
                                                              Map<Integer, List<OrderDetailDO>> orderDetailMap,
                                                              String contractConfigJson,
                                                              Integer orgId) {

        // 提取订单ID列表
        List<Integer> orderIdList = orderMasterList.stream()
                .map(OrderMasterDO::getId)
                .collect(Collectors.toList());

        // 获取不到配置或配置值为空，默认未开启
        if (StringUtils.isBlank(contractConfigJson)) {
            return sameUploadStatusMap(orderIdList, OrderUploadStatusEnum.NO_NEED_TO_UPLOAD.value);
        }

        // 解析合同配置
        OrderContractConfig contractConfig = JsonUtils.fromJson(contractConfigJson, OrderContractConfig.class);

        // 未开启上传配置
        if (Objects.equals(OrderUploadContractEnum.NO.getValue(), contractConfig.getUploadContract())) {
            return sameUploadStatusMap(orderIdList, OrderUploadStatusEnum.NO_NEED_TO_UPLOAD.value);
        }

        // 检查上传入口显示位置 - 不包含采购人中心，则无需上传
        List<Integer> entryDisplayPosition = contractConfig.getEntryDisplayPosition();
        if (CollectionUtils.isEmpty(entryDisplayPosition)
                || !entryDisplayPosition.contains(OrderContractEntryDisplayPositionEnum.BUYER_CENTER.getValue())) {
            return sameUploadStatusMap(orderIdList, OrderUploadStatusEnum.NO_NEED_TO_UPLOAD.value);
        }

        // 订单是否需要上传合同Map
        Map<Integer, Boolean> orderNeedUploadMap = New.mapWithCapacity(CollectionUtils.size(orderIdList));

        // 上传入口显示配置
        if (New.set(OrderContractEntryDisplayEnum.RULE_DISPLAY.getValue(), OrderContractEntryDisplayEnum.RULE_NOT_DISPLAY.getValue()).contains(contractConfig.getEntryDisplay())
                && CollectionUtils.isNotEmpty(contractConfig.getRules())) {
            // 按是否命中规则显示
            processOrderUploadNeedByAllRules(orderMasterList, orderDetailMap, orderIdList, contractConfig, orderNeedUploadMap);
        } else {
            // 所有订单都显示
            orderIdList.forEach(orderId -> orderNeedUploadMap.put(orderId, true));
        }

        // 查询订单的合同信息
        List<OrderContract> orderContractList = orderContractMapper.selectByOrderIdIn(orderIdList);
        Map<Integer, List<OrderContract>> orderIdContractMap = CollectionUtils.isEmpty(orderContractList) ?
                New.map() : orderContractList.stream().collect(Collectors.groupingBy(OrderContract::getOrderId));

        // 构建最终结果Map
        Map<Integer, Integer> orderIdUploadValueMap = New.mapWithCapacity(CollectionUtils.size(orderIdList));
        for (Integer orderId : orderIdList) {
            Boolean needUpload = orderNeedUploadMap.get(orderId);
            if (BooleanUtils.isFalse(needUpload)) {
                // 不需要上传合同
                orderIdUploadValueMap.put(orderId, OrderUploadStatusEnum.NO_NEED_TO_UPLOAD.value);
            } else {
                // 需要上传合同
                boolean hasContract = CollectionUtils.isNotEmpty(orderIdContractMap.get(orderId));
                Integer uploadStatus = hasContract ? OrderUploadStatusEnum.TRUE.value : OrderUploadStatusEnum.FALSE.value;
                orderIdUploadValueMap.put(orderId, uploadStatus);
            }
        }

        // 处理单位定制化合同上传状态
        handleCustomUploadContractStatus(orderIdUploadValueMap, orderMasterList, orderDetailMap, orgId);

        return orderIdUploadValueMap;
    }

    /**
     * 通过规则判断订单是否显示上传合同按钮
     *
     * @param orderMasterList    订单主表数据列表
     * @param orderDetailMap     订单详情数据映射
     * @param orderIdList        订单ID列表
     * @param contractConfig     合同配置
     * @param orderNeedUploadMap 订单是否需要上传的结果映射,value 为True需要上传
     */
    private void processOrderUploadNeedByAllRules(
            List<OrderMasterDO> orderMasterList,
            Map<Integer, List<OrderDetailDO>> orderDetailMap,
            List<Integer> orderIdList,
            OrderContractConfig contractConfig,
            Map<Integer, Boolean> orderNeedUploadMap) {

        if (CollectionUtils.isEmpty(orderMasterList) || CollectionUtils.isEmpty(contractConfig.getRules())) {
            return;
        }

        // 1. 规则分类
        List<RuleDTO> allRules = contractConfig.getRules();
        // 采购规则
        List<RuleDTO> purchaseRules = allRules.stream()
                .filter(rule -> Objects.equals(rule.getRuleSource(), RuleSourceEnum.SPECIFIC_GOODS.getValue()))
                .collect(Collectors.toList());
        // 限额规则
        List<RuleDTO> quotaRules = allRules.stream()
                .filter(rule -> Objects.equals(rule.getRuleSource(), RuleSourceEnum.PURCHASE_LIMIT_CONTROL.getValue()))
                .collect(Collectors.toList());

        // 2. 调用规则查询接口，填充命中结果
        Map<Integer, Boolean> orderHitAnyRuleMap = New.map();
        fillPurchaseRuleHits(orderMasterList, orderDetailMap, purchaseRules, orderHitAnyRuleMap);
        fillQuotaRuleHits(orderMasterList, quotaRules, orderHitAnyRuleMap);

        // 3. 处理合同显示状态
        for (Integer orderId : orderIdList) {
            boolean isHit = orderHitAnyRuleMap.getOrDefault(orderId, false);

            boolean needUpload;
            // 根据配置确定是否需要上传
            if (Objects.equals(contractConfig.getEntryDisplay(), OrderContractEntryDisplayEnum.RULE_DISPLAY.getValue())) {
                // 命中时显示
                needUpload = isHit;
            } else if (Objects.equals(contractConfig.getEntryDisplay(), OrderContractEntryDisplayEnum.RULE_NOT_DISPLAY.getValue())) {
                // 命中时不显示
                needUpload = !isHit;
            } else {
                // 默认显示
                needUpload = true;
            }
            orderNeedUploadMap.put(orderId, needUpload);
        }
    }

    /**
     * 填充订单是否命中采购规则
     *
     * @param orderMasterList    订单主表数据列表
     * @param orderDetailMap     订单详情数据映射
     * @param purchaseRules      采购规则列表
     * @param orderHitAnyRuleMap 订单命中规则的结果映射,value为True表示命中
     */
    private void fillPurchaseRuleHits(
            List<OrderMasterDO> orderMasterList,
            Map<Integer, List<OrderDetailDO>> orderDetailMap,
            List<RuleDTO> purchaseRules,
            Map<Integer, Boolean> orderHitAnyRuleMap) {

        if (CollectionUtils.isEmpty(orderMasterList) || CollectionUtils.isEmpty(purchaseRules)) {
            return;
        }

        List<Long> ruleIds = purchaseRules.stream()
                .filter(rule -> Objects.equals(rule.getStatus(), EnableStatusEnum.NORMAL.getVal()))
                .map(RuleDTO::getRuleId)
                .filter(Objects::nonNull)
                .collect(toList());

        if (CollectionUtils.isEmpty(ruleIds)) {
            return;
        }

        // 创建批量请求的数据结构
        PurchaseRuleRequestDTO batchRequestDTO = new PurchaseRuleRequestDTO();
        batchRequestDTO.setRuleIds(ruleIds);
        batchRequestDTO.setCheckNodeTypeEnum(CheckNodeTypeEnum.ORDER_CONFIRM_ACCEPTANCE);
        batchRequestDTO.setReturnTargetRule(true);

        // 收集所有商品信息，并维护商品seqId到订单ID的映射
        List<RuleProductVerificationDTO> allProductVerifications = New.list();
        Map<Long, Integer> seqIdToOrderIdMap = New.map();

        // 记录哪些订单有详情信息被处理
        Set<Integer> processedOrderIds = New.set();

        // 基础序列号，用于生成唯一的seqId
        long baseSeqId = 0;

        // 获取组织ID
        Integer orgId = orderMasterList.get(0).getFuserid();

        // 遍历所有订单，收集单位ID和商品信息
        for (OrderMasterDO orderMasterDO : orderMasterList) {
            Integer orderId = orderMasterDO.getId();
            List<OrderDetailDO> detailList = orderDetailMap.get(orderId);

            if (CollectionUtils.isEmpty(detailList)) {
                continue;
            }

            // 添加该订单的所有商品到验证列表
            for (OrderDetailDO detailDO : detailList) {
                RuleProductVerificationDTO verificationDTO = new RuleProductVerificationDTO();
                // 使用递增的序列号，确保唯一性
                long currentSeqId = ++baseSeqId;
                verificationDTO.setSeqId(currentSeqId);
                verificationDTO.setProductId(detailDO.getProductSn());
                verificationDTO.setProductName(detailDO.getFgoodname());
                verificationDTO.setCategoryId(detailDO.getCategoryid());
                verificationDTO.setBrandId(detailDO.getFbrandid());
                verificationDTO.setSuppId(detailDO.getSuppId());
                verificationDTO.setPrice(detailDO.getOriginalPrice());

                // 将序列号和订单ID的关系存储起来
                seqIdToOrderIdMap.put(currentSeqId, orderId);
                allProductVerifications.add(verificationDTO);
            }

            processedOrderIds.add(orderId);
        }

        // 设置批量请求参数
        batchRequestDTO.setOrgIdList(New.list(orgId));
        batchRequestDTO.setRuleProductVerificationList(allProductVerifications);

        // 一次性调用规则引擎进行批量验证
        List<PurchaseRuleResultDTO> allRuleResults = storeRuleClient.purchaseRulesVerify(batchRequestDTO);

        // 处理规则结果
        if (CollectionUtils.isNotEmpty(allRuleResults)) {
            // 按seqId分组规则结果，因为一个商品可能匹配多个规则
            Map<Long, List<PurchaseRuleResultDTO>> seqIdToRuleResultsMap = New.map();
            for (PurchaseRuleResultDTO ruleResult : allRuleResults) {
                if (Objects.nonNull(ruleResult) && Objects.nonNull(ruleResult.getSeqId())) {
                    seqIdToRuleResultsMap
                            .computeIfAbsent(ruleResult.getSeqId(), k -> New.list())
                            .add(ruleResult);
                }
            }

            // 按订单ID分组规则结果
            Map<Integer, List<PurchaseRuleResultDTO>> orderIdToRuleResultsMap = New.map();
            for (Map.Entry<Long, List<PurchaseRuleResultDTO>> entry : seqIdToRuleResultsMap.entrySet()) {
                Long seqId = entry.getKey();
                List<PurchaseRuleResultDTO> ruleResults = entry.getValue();

                Integer orderId = seqIdToOrderIdMap.get(seqId);
                if (Objects.nonNull(orderId)) {
                    orderIdToRuleResultsMap
                            .computeIfAbsent(orderId, k -> New.list())
                            .addAll(ruleResults);
                }
            }

            // 处理每个订单的规则验证结果
            for (Integer orderId : processedOrderIds) {
                List<PurchaseRuleResultDTO> orderRuleResults = orderIdToRuleResultsMap.getOrDefault(orderId, New.emptyList());

                // 判断是否有命中规则
                boolean hasMatchedRule = CollectionUtils.isNotEmpty(orderRuleResults);

                // 只要命中了采购规则，就在统一命中Map中标记为true
                if (hasMatchedRule) {
                    orderHitAnyRuleMap.put(orderId, true);
                }
            }
        }
    }

    /**
     * 填充订单是否命中限额规则
     *
     * @param orderMasterList    订单主表数据列表
     * @param quotaRules         限额规则列表
     * @param orderHitAnyRuleMap 订单命中规则的结果映射,value为True表示命中
     */
    private void fillQuotaRuleHits(
            List<OrderMasterDO> orderMasterList,
            List<RuleDTO> quotaRules,
            Map<Integer, Boolean> orderHitAnyRuleMap) {

        if (CollectionUtils.isEmpty(orderMasterList) || CollectionUtils.isEmpty(quotaRules)) {
            return;
        }

        // 1. 提取启用的限额规则ID
        List<Integer> enabledQuotaRuleIds = quotaRules.stream()
                .filter(rule -> Objects.equals(rule.getStatus(), EnableStatusEnum.NORMAL.getVal()))
                .map(rule -> rule.getRuleId().intValue())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(enabledQuotaRuleIds)) {
            return;
        }

        // 2. 提取订单号并创建orderId映射
        List<String> orderNos = New.list();
        Map<String, Integer> orderNoToIdMap = New.map();
        for (OrderMasterDO orderMaster : orderMasterList) {
            orderNoToIdMap.put(orderMaster.getForderno(), orderMaster.getId());
            orderNos.add(orderMaster.getForderno());
        }

        if (CollectionUtils.isEmpty(orderNos)) {
            return;
        }

        // 3. 构建请求并调用风控服务
        Integer orgId = orderMasterList.get(0).getFuserid();

        RiskOrderListRequest request = new RiskOrderListRequest();
        request.setOrgId(orgId);
        request.setRuleIdList(enabledQuotaRuleIds);
        request.setOrderNoList(orderNos);
        List<RiskOrderResponse> hitOrders = riskControlClient.existsRiskMessageOrder(request);
        // 4.填充命中的订单
        if (CollectionUtils.isNotEmpty(hitOrders)) {
            for (RiskOrderResponse response : hitOrders) {
                String hitOrderNo = response.getOrderNo();
                Integer hitOrderId = orderNoToIdMap.get(hitOrderNo);
                if (Objects.nonNull(hitOrderId)) {
                    orderHitAnyRuleMap.put(hitOrderId, true);
                }
            }
        }
    }

    /**
     * 单位定制化的验收合同上传状态处理
     *
     * @param orderIdUploadValueMap 订单id-上传状态映射
     * @param orderMasterList       订单主表数据列表
     * @param orderDetailMap        订单详情数据映射，key为订单ID，value为订单详情列表
     * @param orgId                 单位ID
     */
    private void handleCustomUploadContractStatus(Map<Integer, Integer> orderIdUploadValueMap,
                                                  List<OrderMasterDO> orderMasterList,
                                                  Map<Integer, List<OrderDetailDO>> orderDetailMap,
                                                  Integer orgId) {
        // 定制化额外处理
        if (Objects.equals(OrgEnum.JIN_FENG_SHI_YAN_SHI.getValue(), orgId)) {
            // 科研服务类品类
            final int scienceServiceCategoryId = 113;
            // 线下单商品总价阈值
            final BigDecimal offlineOrderTotalPriceThreshold = BigDecimal.valueOf(10000);
            final BigDecimal onlineOrderTotalPriceThreshold = BigDecimal.valueOf(50000);

            // 金凤实验室定制需求，只有 线上单仪器设备类商品总价≥1万元/线下单订单总价≥1万元/线上单或线下单存在科研服务类商品 的单才显示上传按钮
            Map<Integer, OrderMasterDO> orderIdDataMap = orderMasterList.stream()
                    .collect(Collectors.toMap(OrderMasterDO::getId, Function.identity()));

            for (Map.Entry<Integer, Integer> entry : orderIdUploadValueMap.entrySet()) {
                if (OrderUploadStatusEnum.FALSE.value.equals(entry.getValue())) {
                    Integer orderId = entry.getKey();
                    OrderMasterDO order = orderIdDataMap.get(orderId);
                    boolean isOnlineOrder = OrderSpeciesEnum.NORMAL.getValue() == order.getSpecies().intValue();

                    // 获取订单详情
                    List<OrderDetailDO> orderDetailList = orderDetailMap.get(orderId);
                    if (CollectionUtils.isEmpty(orderDetailList)) {
                        continue;
                    }

                    // 线下单商品总价大于等于阈值
                    BigDecimal totalPrice = orderDetailList.stream()
                            .map(OrderDetailDO::getFbidamount)
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);

                    boolean needToShowUploadContract = !isOnlineOrder && totalPrice.compareTo(offlineOrderTotalPriceThreshold) >= 0;
                    needToShowUploadContract = needToShowUploadContract || (isOnlineOrder && totalPrice.compareTo(onlineOrderTotalPriceThreshold) >= 0);

                    // 或存在科研服务类商品
                    boolean containsScienceServiceGoods = orderDetailList.stream()
                            .anyMatch(detail -> scienceServiceCategoryId == detail.getFirstCategoryId());

                    needToShowUploadContract = needToShowUploadContract || containsScienceServiceGoods;

                    if (!needToShowUploadContract) {
                        entry.setValue(OrderUploadStatusEnum.NO_NEED_TO_UPLOAD.value);
                    }
                }
            }
        }
    }

    /**
     * @param orderIdList
     * @param uploadStatus
     * @description: 为订单设置全部相同的合同上传状态，仅用于降低重复代码
     * @date: 2020/12/3 10:15
     * @author: zengyanru
     */
    private Map<Integer, Integer> sameUploadStatusMap(List<Integer> orderIdList, Integer uploadStatus) {
        Map<Integer, Integer> orderIdUploadValue = new HashMap<>(orderIdList.size());
        for (Integer orderId : orderIdList) {
            orderIdUploadValue.put(orderId, uploadStatus);
        }
        return orderIdUploadValue;
    }

    /**
     * @param request
     * @return java.util.List<com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO>
     * @description: 获取订单合同信息和其他基本信息
     * @date: 2021/1/6 15:30
     * @author: zengyanru
     */
    @Override
    @ServiceLog(description = "获取订单合同信息和其他基本信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderInfoVO> getContractInfo(OrderBasicParamDTO request) {
        Preconditions.notNull(request, "获取订单合同信息入参不可为空");
        Preconditions.notEmpty(request.getOrderIdList(), "获取订单合同信息入参订单id列表不可为空");
        List<Integer> orderIdList = request.getOrderIdList();
        // 基本信息（搜索）
        OrderSearchParamDTO searchParam = new OrderSearchParamDTO();
        searchParam.setOrderIdList(orderIdList);
        SearchPageResultDTO<OrderMasterSearchDTO> response = orderSearchBoostService.commonSearch(searchParam);
        BusinessErrUtil.notNull(response, ExecptionMessageEnum.ORDER_CONTRACT_INFO_ERROR);
        List<OrderMasterSearchDTO> masterSearchList = response.getRecordList();
        Preconditions.notEmpty(masterSearchList, "获取订单合同信息,搜索订单为空，请重试");
        // 合同信息
        List<OrderContract> contractList = orderContractMapper.selectByOrderIdIn(orderIdList);
        Map<Integer, List<OrderContract>> orderIdContractMap = contractList.stream().collect(groupingBy(OrderContract::getOrderId));
        // 组装信息
        List<OrderInfoVO> orderInfoList = New.listWithCapacity(masterSearchList.size());
        for (OrderMasterSearchDTO masterSearch : masterSearchList) {
            OrderInfoVO orderInfo = new OrderInfoVO();
            OrderMasterVO order = new OrderMasterVO();
            orderInfo.setOrder(order);
            // 主表
            orderInfo.getOrder().setOrderDate(masterSearch.getForderdate());
            orderInfo.getOrder().setDepartmentName(masterSearch.getFbuydepartment());
            orderInfo.getOrder().setOrderNo(masterSearch.getForderno());
            orderInfo.getOrder().setSupplierName(masterSearch.getFsuppname());
            orderInfo.getOrder().setId(masterSearch.getId());
            orderInfo.getOrder().setSpecies(masterSearch.getSpecies());
            orderInfo.getOrder().setBuyUserId(masterSearch.getFbuyerid());
            orderInfo.getOrder().setBuyerName(masterSearch.getFbuyername());
            // 详情
            List<OrderDetailSearchDTO> detailSearchList = masterSearch.getOrderDetail();
            List<OrderDetailVO> detailInfoList = New.listWithCapacity(detailSearchList.size());
            for (OrderDetailSearchDTO detailSearch : detailSearchList) {
                OrderDetailVO detailInfo = new OrderDetailVO();
                detailInfo.setGoodsCode(detailSearch.getFgoodcode());
                detailInfo.setBrand(detailSearch.getFbrand());
                detailInfo.setGoodsName(detailSearch.getFgoodname());
                detailInfo.setQuantity(detailSearch.getFquantity().doubleValue());
                detailInfo.setSpecification(detailSearch.getFspec());
                detailInfo.setPicturePath(detailSearch.getPicPath());
                detailInfo.setId(detailSearch.getDetailId());
                detailInfo.setFirstCategoryId(detailSearch.getFirstCategoryId());
                detailInfoList.add(detailInfo);
            }
            orderInfo.setOrderDetails(detailInfoList);
            // 合同
            List<OrderContract> contractSearchList = orderIdContractMap.get(masterSearch.getId());
            if (CollectionUtils.isNotEmpty(contractSearchList)) {
                orderInfo.setUploadContractNo(contractSearchList.get(0).getContractNo());
                List<ContractInfoVO> contractInfoList = New.listWithCapacity(contractSearchList.size());
                for (OrderContract contractSearch : contractSearchList) {
                    ContractInfoVO contractInfo = new ContractInfoVO();
                    contractInfo.setContractLocation(contractSearch.getContractLocation());
                    contractInfo.setContractName(contractSearch.getContractName());
                    contractInfoList.add(contractInfo);
                }
                orderInfo.setContractInfoVOList(contractInfoList);
            }

            // 新版合同
            OrderContractQueryDTO orderContractQueryDTO = new OrderContractQueryDTO();
            orderContractQueryDTO.setOrderNoList(New.list(masterSearch.getForderno()));
            List<OrderContractInfoVO> contractInfoVOList = orderContractClient.getOrderContractInfo(orderContractQueryDTO);
            orderInfo.setOrderContractInfoVOList(contractInfoVOList);

            orderInfoList.add(orderInfo);
        }
        return orderInfoList;
    }

    /**
     * @param request
     * @return java.lang.Boolean
     * @description: 上传订单合同
     * @date: 2021/1/6 17:40
     * @author: zengyanru
     */
    @Override
    public Boolean uploadContractList(RjSessionInfo rjSessionInfo, UploadContractRequest request) {
        Preconditions.notNull(request, "上传订单合同入参不可为空");
        Preconditions.notEmpty(request.getContractInfoList(), "上传订单合同文件数量不可为空");
        Preconditions.notEmpty(request.getOrderIdList(), "上传订单合同订单id列表不可为空");
        // 合同文件名称最多100个字符
        request.getContractInfoList().forEach(contractInfoReq -> BusinessErrUtil.isTrue(
                StringUtils.length(contractInfoReq.getContractName()) <= 100, "合同文件名称最多100个字符"));
        // 只可以选同一个供应商, 订单的单位应该是当前登录用户的单位
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        List<Integer> orderIdList = request.getOrderIdList();
        List<ContractInfoRequest> contractReqList = request.getContractInfoList();
        OrderSearchParamDTO searchParam = new OrderSearchParamDTO();
        searchParam.setOrderIdList(orderIdList);
        SearchPageResultDTO<OrderMasterSearchDTO> response = orderSearchBoostService.commonSearch(searchParam);
        BusinessErrUtil.notNull(response, ExecptionMessageEnum.UPLOAD_CONTRACT_SEARCH_ERROR);
        List<OrderMasterSearchDTO> masterSearchList = response.getRecordList();
        Preconditions.notEmpty(masterSearchList, "上传订单合同,搜索订单为空，请重试");
        for (OrderMasterSearchDTO masterSearch : masterSearchList) {
            BusinessErrUtil.isTrue(masterSearch.getFuserid().equals(loginInfo.getOrgId()), ExecptionMessageEnum.UPLOAD_CONTRACT_UNIT_MISMATCH);
        }
        Set<Integer> suppIdList = masterSearchList.stream().map(OrderMasterSearchDTO::getFsuppid).collect(Collectors.toSet());
        Preconditions.isTrue(CollectionUtils.isNotEmpty(suppIdList) && Objects.equals(suppIdList.size(), 1), "选择的订单不可为空，且需要为同一个供应商的订单");
        // 构造插入合同列表
        List<OrderContract> orderContractList = New.listWithCapacity(orderIdList.size());
        Date nowDate = new Date();
        for (Integer orderId : orderIdList) {
            for (ContractInfoRequest contractInfoReq : contractReqList) {
                OrderContract curOrderContract = new OrderContract();
                curOrderContract.setOrderId(orderId);
                curOrderContract.setContractLocation(contractInfoReq.getContractLocation());
                curOrderContract.setContractName(contractInfoReq.getContractName());
                curOrderContract.setCreateTime(nowDate);
                curOrderContract.setContractNo(request.getUploadContractNo());
                orderContractList.add(curOrderContract);
            }
        }
        // 根据订单id批量删除合同信息后，批量根据订单id插入contract list
        orderContractMapper.deleteByOrderIdIn(orderIdList);
        Integer insertAffect = orderContractMapper.insertList(orderContractList);
        BusinessErrUtil.isTrue(insertAffect > 0, ExecptionMessageEnum.UPLOAD_CONTRACT_FAILED);
        return true;
    }

    /**
     * @return
     * @description: 获取当前登录用户单位的需签合同模板
     */
    @Override
    public List<OrderContractTemplateVO> getOrderTemplateList(RjSessionInfo rjSessionInfo) {
        // 获取配置值
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        List<BaseConfigDTO> configList = sysConfigClient.getValueByOrgCodeAndConfigCode(loginInfo.getOrgCode(), New.list(ConfigConstant.PROCUREMENT_CONTRACT_TEMPLATE));
        BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(configList), ExecptionMessageEnum.CONFIG_RETRIEVAL_FAILED);
        BaseConfigDTO config = configList.get(0);
        if (StringUtils.isNotBlank(config.getConfigValue())) {
            // json 转 list
            List<OrderConfigTemplateBO> configTemplateList = JsonUtils.parseList(config.getConfigValue(), OrderConfigTemplateBO.class);
            // 构造输出
            List<OrderContractTemplateVO> contractTemplateList = configTemplateList.stream().map(configTemp -> {
                OrderContractTemplateVO contractTemp = new OrderContractTemplateVO();
                contractTemp.setTemplateName(configTemp.getName());
                contractTemp.setTemplateUrl(configTemp.getValue());
                return contractTemp;
            }).collect(toList());
            return contractTemplateList;
        }
        return New.list();
    }
}
