package com.ruijing.store.order.gateway.buyercenter.request;

import com.reagent.order.base.order.dto.request.OrderMaterialCodeRequest;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;

import java.util.List;

/**
 * Name: OrderMaterialCodeRequest
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2024/3/13
 */
@RpcModel(value = "订单物资编码查询入参")
public class OrderMaterialCodeRequestDTO {

    List<OrderMaterialCodeRequest> requestList;

    public List<OrderMaterialCodeRequest> getRequestList() {
        return requestList;
    }

    public void setRequestList(List<OrderMaterialCodeRequest> requestList) {
        this.requestList = requestList;
    }
}
