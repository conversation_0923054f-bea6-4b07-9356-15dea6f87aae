package com.ruijing.store.order.service;

import com.ruijing.store.order.base.core.model.OrderMasterDO;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON>feng
 * @date: 2021-05-07 17:10
 **/

public interface ResearchBaseService {

    /**
     * 当前方法只有当 当前机构有平台运行经费 && 当前订单为平台运行经费支付 时才返回true
     * 选择了平台经费 && 要求设置了平台经费不进入结算流程的单位 不进入结算流程
     *      目前只有中山五院选择了平台经费，收货后订单状态为“已完成”，不进入结算流程 add 20210428
     * @param orderMasterDO 订单信息
     * @return 不进入结算流程 true ,进入结算流程 false
     */
    Boolean isPlatformFound(OrderMasterDO orderMasterDO);

    /**
     * 判断是否是新单
     *
     * @param orderMasterDO 订单信息
     * @param oldOrderDateString 旧单划分时间
     * @return true为新单，false为旧单
     */
    Boolean isNewOrder(OrderMasterDO orderMasterDO,String oldOrderDateString);
}
