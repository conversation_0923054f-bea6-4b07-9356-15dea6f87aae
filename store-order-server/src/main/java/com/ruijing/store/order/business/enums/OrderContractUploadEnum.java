package com.ruijing.store.order.business.enums;

/**
 * Author: zhukai
 * CreateTime : 2019/12/4 8:13 下午
 * Description: 订单合同上传配置枚举
 */
public enum OrderContractUploadEnum {

    /**
     * 没有合同上传功能
     */
    FALSE(0,"否"),
    /**
     * 有合同上传功能
     */
    TRUE(1,"是");

    private final Integer value;

    private final String name;

    OrderContractUploadEnum(int value, String name){
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
