package com.ruijing.store.order.base.docking.service.impl;

import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.docking.dto.DockingExtraDTO;
import com.ruijing.store.order.api.base.docking.enums.DockingPushStatusEnum;
import com.ruijing.store.order.api.base.docking.enums.DockingTypeEnum;
import com.ruijing.store.order.base.docking.mapper.DockingExtraMapper;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.base.docking.translator.DockingExtraTranslator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zhuk
 * @create: 2019-09-25 10:02
 **/
@Service
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
public class DockingExtraServiceImpl implements DockingExtraService {

    @Resource
    private DockingExtraMapper dockingExtraMapper;

    /**
     * 插入 dockingExtra
     * @param dockingExtraDTO 入参
     */
    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public void insertDockingExtra(DockingExtraDTO dockingExtraDTO){
        DockingExtra dockingExtra = DockingExtraTranslator.dtoToDockingExtra(dockingExtraDTO);
        dockingExtraMapper.insertSelective(dockingExtra);
    }

    /**
     * 查询DockingExtra
     * @param dockingExtraDTO 入参
     * @return  结果
     */
    @Override
    public List<DockingExtraDTO> findDockingExtra(DockingExtraDTO dockingExtraDTO){
        DockingExtra dockingExtra = DockingExtraTranslator.dtoToDockingExtra(dockingExtraDTO);
        List<DockingExtra> dockingExtraList = dockingExtraMapper.findDockingExtra(dockingExtra);
        return dockingExtraList.stream().map(DockingExtraTranslator::dockingExtraToDto).collect(Collectors.toList());
    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public Boolean updateDockingExtra(DockingExtraDTO dockingExtraDTO) {
        DockingExtra dockingExtra = DockingExtraTranslator.dtoToDockingExtra(dockingExtraDTO);
        return dockingExtraMapper.updateByPrimaryKeySelective(dockingExtra) == 1 ? true : false;
    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public void updateExtraInfoByInfo(String extraInfo,String info) {
        dockingExtraMapper.updateExtraInfoByInfo(extraInfo,info);
    }

    @Override
    public Integer insertList(List<DockingExtraDTO> dockingExtraDTOList){
        if (CollectionUtils.isNotEmpty(dockingExtraDTOList)) {
            List<DockingExtra> dockingExtraList = dockingExtraDTOList.stream().map(DockingExtraTranslator::dtoToDockingExtra).collect(Collectors.toList());
            int i = dockingExtraMapper.insertList(dockingExtraList);
            return i;
        }
        return 0;
    }

    @Override
    @ServiceLog(description = "查询第三方单号关联信息", serviceType = ServiceType.RPC_SERVICE)
    public List<DockingExtraDTO> findDockingExtraByInfo(List<String> infoList) {
        Assert.notEmpty(infoList, "查询失败！入参错误！");
        List<DockingExtra> extraList = dockingExtraMapper.findByInfoIn(infoList);

        if (CollectionUtils.isEmpty(extraList)) {
            return Collections.emptyList();
        }

        return extraList.stream().map(DockingExtraTranslator::dockingExtraToDto).collect(Collectors.toList());
    }

    @Override
    public List<DockingExtraDTO> findDockingByExtraInfoAndType(List<String> extraInfoList, Integer type) {
        List<DockingExtra> byExtraInfoInAndType = dockingExtraMapper.findByExtraInfoInAndType(extraInfoList, type);
        List<DockingExtraDTO> result = byExtraInfoInAndType.stream().map(DockingExtraTranslator::dockingExtraToDto).collect(Collectors.toList());
        return result;
    }

    @Override
    public Integer saveOrUpdateDockingExtra(String orderNo, String extraNo, Boolean processResult, String message) {
        DockingExtra dockingExtra = new DockingExtra();
        dockingExtra.setType(DockingTypeEnum.Order.getValue());
        dockingExtra.setInfo(orderNo);
        if (processResult) {
            dockingExtra.setExtraInfo(extraNo);
            dockingExtra.setStatusextra(DockingPushStatusEnum.SUCCESS.getCode());
        } else {
            dockingExtra.setStatusextra(DockingPushStatusEnum.FAILED.getCode());
        }
        dockingExtra.setMemo(message);
        return saveOrUpdateDockingExtra(dockingExtra);
    }

    @Override
    public Integer saveOrUpdateDockingExtra(DockingExtra dockingExtra) {
        if (dockingExtra.getInfo() == null) {
            return 0;
        }
        dockingExtra.setType(DockingTypeEnum.Order.getValue());
        String memo = dockingExtra.getMemo();
        if (StringUtils.isNotBlank(memo)) {
            if (memo.length() >= 200) {
                dockingExtra.setMemo(memo.substring(0, 200));
            }
        }

        // 对接记录默认只保留一条
        Long existed = dockingExtraMapper.countByInfo(dockingExtra.getInfo());
        Integer affect = 0;
        if (existed == 0L) {
            affect = dockingExtraMapper.insertSelective(dockingExtra);
        } else {
            affect = dockingExtraMapper.updateByInfo(dockingExtra);
        }
        return affect;
    }

    @Override
    public void customValidationDockingStatus(String info) {
        List<DockingExtraDTO> infoList = this.findDockingExtraByInfo(Arrays.asList(info));
        // 如果发货推送失败，不允许收货
        if (CollectionUtils.isNotEmpty(infoList) && DockingPushStatusEnum.FAILED.getCode().equals(infoList.get(0).getStatusextra())) {
            throw new BusinessInterceptException(ExecptionMessageEnum.ORDER_STATUS_NOT_SYNCHRONIZED);
        }
    }
}
