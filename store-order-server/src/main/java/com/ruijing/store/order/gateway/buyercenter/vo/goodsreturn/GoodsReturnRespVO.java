package com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/4 17:52
 * @Description
 **/
@RpcModel("我的退货-退货列表分页信息")
public class GoodsReturnRespVO implements Serializable {

    private static final long serialVersionUID = -4030119635119255142L;

    /**
     * 页数
     */
    @RpcModelProperty("页数")
    private Integer pageNo;

    /**
     * 每页数量
     */
    @RpcModelProperty("每页数量")
    private Integer pageSize;

    /**
     * 总页数
     */
    @RpcModelProperty("总页数")
    private Integer totalPages;

    /**
     * 总条目数
     */
    @RpcModelProperty("总条目数")
    private Long total;

    /**
     * 订单列表
     */
    @RpcModelProperty("订单列表")
    private List<GoodsReturnOrderBriefVO> orderList;

    public GoodsReturnRespVO() {
    }

    public GoodsReturnRespVO(ArrayList<GoodsReturnOrderBriefVO> orderListForGRModels, Integer pageNo, Integer pageSize) {
        this.setPageNo(pageNo);
        this.setPageSize(pageSize);
        this.setOrderList(orderListForGRModels);
    }

    public GoodsReturnRespVO(List<GoodsReturnOrderBriefVO> models, Integer pageNo, Integer pageSize, Integer totalPage) {
        this.setPageNo(pageNo);
        this.setPageSize(pageSize);
        this.setOrderList(models);
        this.setTotalPages(totalPage);
    }


    public List<GoodsReturnOrderBriefVO> getOrderList() {
        return orderList;
    }

    public void setOrderList(List<GoodsReturnOrderBriefVO> orderList) {
        this.orderList = orderList;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(Integer totalPages) {
        this.totalPages = totalPages;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnRespVO{");
        sb.append("pageNo=").append(pageNo);
        sb.append(", pageSize=").append(pageSize);
        sb.append(", totalPages=").append(totalPages);
        sb.append(", total=").append(total);
        sb.append(", orderList=").append(orderList);
        sb.append('}');
        return sb.toString();
    }
}
