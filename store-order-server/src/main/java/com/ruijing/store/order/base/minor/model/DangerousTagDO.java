package com.ruijing.store.order.base.minor.model;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020/5/7 0007 15:24
 * @Version 1.0
 * @Desc:描述
 */
/**
    * 危化品标签
    */
public class DangerousTagDO {
    /**
    * Id
    */
    private Long id;

    /**
    * 业务Id
    */
    private String businessId;

    /**
    * CAS号
    */
    private String casNo;

    /**
    * 危化品类型
    */
    private Integer dangerousType;

    /**
    * 业务类型
    */
    private Integer businessType;

    /**
    * 创建时间
    */
    private Date creationTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 1,管制类;2,非管制
    */
    private Integer regulatoryType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public Integer getDangerousType() {
        return dangerousType;
    }

    public void setDangerousType(Integer dangerousType) {
        this.dangerousType = dangerousType;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getRegulatoryType() {
        return regulatoryType;
    }

    public void setRegulatoryType(Integer regulatoryType) {
        this.regulatoryType = regulatoryType;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", businessId=").append(businessId);
        sb.append(", casNo=").append(casNo);
        sb.append(", dangerousType=").append(dangerousType);
        sb.append(", businessType=").append(businessType);
        sb.append(", creationTime=").append(creationTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", regulatoryType=").append(regulatoryType);
        sb.append("]");
        return sb.toString();
    }
}