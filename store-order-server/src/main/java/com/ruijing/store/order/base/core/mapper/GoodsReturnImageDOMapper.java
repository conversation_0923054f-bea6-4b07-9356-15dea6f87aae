package com.ruijing.store.order.base.core.mapper;

import com.ruijing.store.order.base.core.model.GoodsReturnImageDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GoodsReturnImageDOMapper {
    /**
     * 通过returnId范围查询退货图片记录
     * @param minReturnId
     * @param maxReturnId
     * @return
     */
    List<GoodsReturnImageDO> findByReturnIdBetween(@Param("minReturnId")Integer minReturnId,@Param("maxReturnId")Integer maxReturnId);

}