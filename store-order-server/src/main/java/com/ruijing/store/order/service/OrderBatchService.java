package com.ruijing.store.order.service;

import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeStatisticsDTO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.OrderBatchesVO;
import com.ruijing.store.order.other.dto.UniqueBarCodeDTO;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2025-03-24 14:34
 * @description: 订单批次模式服务（启用批次，不启用一物一码）
 */
public interface OrderBatchService {

    /**
     * 覆盖写入批次（启用批次，不启用一物一码）
     * @param requestDataList 请求数据
     */
    void overWriteBatches(List<UniqueBarCodeDTO> requestDataList);

    OrderBatchesVO getOrderBatchesVO(OrderMasterDO orderMasterDO);

    List<OrderUniqueBarCodeDTO> getBatchesByOrders(List<OrderMasterDO> orderMasterDOList);

    /**
     * 获取批次的填写情况
     * @param orderNo 订单号
     * @param typeList 类型
     * @return 填写情况
     */
    List<OrderUniqueBarCodeStatisticsDTO> getBarCodeBatchesByOrderNo(String orderNo, List<Integer> typeList);
}
