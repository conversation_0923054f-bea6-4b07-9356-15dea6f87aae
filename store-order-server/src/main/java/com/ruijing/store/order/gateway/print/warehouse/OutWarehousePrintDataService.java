package com.ruijing.store.order.gateway.print.warehouse;

import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.gateway.print.dto.warehouse.OutWarehousePrintDataDTO;
import com.ruijing.store.warehouse.message.vo.outwarehouse.OutWarehouseApplicationDetailVO;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseExitDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/1 18:38
 * @description
 */
public interface OutWarehousePrintDataService {

    /**
     * 出库单数据打印
     * @param orderMasterDO 订单主表数据
     * @param orderDetailDOList 订单商品详情
     * @param warehouseApplicationInfoList  入库单数据，过滤用
     * @return 出库数据
     */
    List<OutWarehousePrintDataDTO> getOutWarehousePrintData(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList, List<BizWarehouseEntryDTO> warehouseApplicationInfoList);
}
