package com.ruijing.store.order.business.enums;

public enum DangerousTagEnum {

    /**
     * 商品
     */
    PRODUCT(1, "商品"),

    /**
     * 采购申请单详情
     */
    BUY_APP_DETAIL(2, "采购申请单详情"),

    /**
     * 订单详情
     */
    ORDER_DETAIL(3, "订单详情"),

    /**
     * 出入库商品
     */
    WAREHOUSE_GOODS(4, "出入库商品"),

    /**
     * 竞价单
     */
    BID_ORDER(5, "竞价单");

    private Integer value;
    private String name;

    DangerousTagEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}