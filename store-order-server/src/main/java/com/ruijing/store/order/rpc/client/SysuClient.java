package com.ruijing.store.order.rpc.client;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.reagent.research.custom.api.common.CommonConfigRPCService;
import com.reagent.research.custom.dto.common.CommonConfigDTO;
import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.reagent.research.sysu.order.api.dto.order.OrderOfflinePayeeUserDTO;
import com.reagent.research.sysu.order.api.service.OrderOfflinePayeeUserService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.order.api.base.other.dto.OrderPayeeUserDTO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.log.annotation.ServiceLog;
import com.ruijing.store.order.log.enums.ServiceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @author: liwenyu
 * @createTime: 2023-07-03 16:35
 * @description:
 **/
@ServiceClient
public class SysuClient {

    private final Logger logger = LoggerFactory.getLogger(this.getClass().getName());

    /**
     * 中大单位对应的host缓存
     */
    private final Cache<String, String> orgCodeHostMap = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.HOURS).build();

    @MSharpReference(remoteAppkey = "research-sysu-order-service")
    private OrderOfflinePayeeUserService orderOfflinePayeeUserService;

    @MSharpReference(remoteAppkey = "research-custom-business-service")
    private CommonConfigRPCService commonConfigRpcService;

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "获取中大host")
    public String getAppHostByOrgCode(String orgCode){
        String host = orgCodeHostMap.getIfPresent(orgCode);
        if(Objects.nonNull(host)){
            return host;
        }
        CommonConfigDTO param = new CommonConfigDTO();
        param.setOrgCode(orgCode);
        RemoteResponse<String> response = commonConfigRpcService.getAppHostByOrgCode(param);
        BusinessErrUtil.isTrue(response.isSuccess(), "获取中大host失败!");
        host = response.getData();
        orgCodeHostMap.put(orgCode, host);
        return host;
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "校验中大封账期")
    public void checkIsApplyStealOff(Integer orgId){
        if(ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(orgId)){
            RemoteResponse<Boolean> response = commonConfigRpcService.checkIsApplyStealOff(orgId);
            Preconditions.isTrue(response.isSuccess(), "获取中大封账配置失败！");
            // 获取到校验数据，需要限制
            BusinessErrUtil.isTrue(Boolean.TRUE.equals(response.getData()), response.getMsg());
        }
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "保存/更新订单个人收款信息快照")
    public void saveOrderOfflinePayeeUser(OrderMasterDO orderMasterDO, OrderPayeeUserDTO orderPayeeUserDTO){
        OrderOfflinePayeeUserDTO sysuDTO = new OrderOfflinePayeeUserDTO();
        sysuDTO.setName(orderPayeeUserDTO.getName());
        sysuDTO.setJobNumber(orderPayeeUserDTO.getJobNumber());
        sysuDTO.setOrgId(orderMasterDO.getFuserid());
        sysuDTO.setSuppId(orderMasterDO.getFsuppid());
        sysuDTO.setOrderId(orderMasterDO.getId());
        this.saveOrderOfflinePayeeUser(New.list(sysuDTO));
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "保存/更新订单个人收款信息快照")
    public void saveOrderOfflinePayeeUser(List<OrderOfflinePayeeUserDTO> list){
        RemoteResponse<Boolean> response = orderOfflinePayeeUserService.saveOrderOfflinePayeeUser(list);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }
}
