package com.ruijing.store.order.base.core.translator;

import com.ruijing.store.order.api.base.other.dto.RefOrderDetailTagDTO;
import com.ruijing.store.order.api.base.other.dto.RefOrderDetailTagResponseDTO;
import com.ruijing.store.order.base.core.model.RefOrderDetailTagDO;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class RefOrderDetailTagTranslator {

    /**
     * order detail tag do to dto
     * @param item
     * @return
     */
    public static RefOrderDetailTagResponseDTO doToDto(RefOrderDetailTagDO item) {
        if (item == null) {
            return null;
        }
        RefOrderDetailTagResponseDTO dto = new RefOrderDetailTagResponseDTO();
        dto.setId(item.getId());
        dto.setRefId(item.getRefId());
        dto.setTagValue(item.getTagValue());
        dto.setTagName(item.getTagName());
        dto.setTagType(item.getTagType());
        dto.setCreationTime(item.getCreationTime());
        dto.setUpdateTime(item.getUpdateTime());
        dto.setIsDeleted(item.getIsDeleted());
        dto.setDeletionTime(item.getDeletionTime());

        return dto;
    }

    /**
     * order detail tag do to dto with list
     * @param doList
     * @return
     */
    public static List<RefOrderDetailTagResponseDTO> doToDto(List<RefOrderDetailTagDO> doList) {
        if (CollectionUtils.isEmpty(doList)) {
            return Collections.emptyList();
        }

        return doList.stream().map(r -> doToDto(r)).collect(Collectors.toList());
    }

    /**
     * order detail tag dto to do with list
     * @param dtoList
     * @return
     */
    public static List<RefOrderDetailTagDO> dtoToDo(List<RefOrderDetailTagDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }

        return dtoList.stream().map(r -> dtoToDo(r)).collect(Collectors.toList());
    }

    /**
     * order detail tag dto to do
     * @param item
     * @return
     */
    public static RefOrderDetailTagDO dtoToDo(RefOrderDetailTagDTO item) {
        if (item == null) {
            return null;
        }
        RefOrderDetailTagDO refOrderDetailTagDO = new RefOrderDetailTagDO();
        refOrderDetailTagDO.setId(item.getId());
        refOrderDetailTagDO.setRefId(item.getRefId());
        refOrderDetailTagDO.setTagValue(item.getTagValue());
        refOrderDetailTagDO.setTagName(item.getTagName());
        refOrderDetailTagDO.setTagType(item.getTagType());
        refOrderDetailTagDO.setCreationTime(item.getCreationTime());
        refOrderDetailTagDO.setUpdateTime(item.getUpdateTime());
        refOrderDetailTagDO.setIsDeleted(item.getIsDeleted());
        refOrderDetailTagDO.setDeletionTime(item.getDeletionTime());
        return refOrderDetailTagDO;

    }
}
