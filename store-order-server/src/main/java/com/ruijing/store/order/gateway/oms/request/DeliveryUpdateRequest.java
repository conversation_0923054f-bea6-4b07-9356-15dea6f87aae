package com.ruijing.store.order.gateway.oms.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description：
 * @date ：Created in 2022-11-25 11:05
 */
public class DeliveryUpdateRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    @RpcModelProperty("1完成分拣，2完成配送，3追加分拣图片，4追加配送图片")
    private Integer operationType;

    @RpcModelProperty("订单id")
    private Integer orderId;

    @RpcModelProperty("图片列表")
    private List<String> fileUrlList;

    @RpcModelProperty("备注")
    private String note;

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public List<String> getFileUrlList() {
        return fileUrlList;
    }

    public void setFileUrlList(List<String> fileUrlList) {
        this.fileUrlList = fileUrlList;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("DeliveryUpdateRequest{");
        sb.append("operationType=").append(operationType);
        sb.append(", orderId=").append(orderId);
        sb.append(", fileUrlList=").append(fileUrlList);
        sb.append(", note='").append(note).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
