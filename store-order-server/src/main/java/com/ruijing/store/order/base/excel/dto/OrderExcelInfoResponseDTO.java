package com.ruijing.store.order.base.excel.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;

public class OrderExcelInfoResponseDTO implements Serializable {

    private static final long serialVersionUID = 8466590889319994148L;

    /**
     * 主键
     */
    @RpcModelProperty("id")
    private Integer id;

    /**
     * 导出文件名称
     */
    @RpcModelProperty("导出文件名称")
    private String fileName;

    /**
     * 导出时间
     */
    @RpcModelProperty("导出时间")
    private Date exportDate;

    /**
     * 导出人id
     */
    @RpcModelProperty("导出时间")
    private Integer userId;

    /**
     * 导出人名称
     */
    @RpcModelProperty("导出人名称")
    private String userName;

    /**
     * 导出状态 1导出中2导出成功3导出失败
     */
    @RpcModelProperty("导出状态 1导出中2导出成功3导出失败")
    private Integer status;

    /**
     * 文件类型 1订单明细2商品明细
     */
    @RpcModelProperty("文件类型 1订单明细2商品明细")
    private Integer fileType;

    /**
     * 文件下载地址
     */
    @RpcModelProperty("文件下载地址")
    private String fileUrl;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Date getExportDate() {
        return exportDate;
    }

    public void setExportDate(Date exportDate) {
        this.exportDate = exportDate;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getFileType() {
        return fileType;
    }

    public void setFileType(Integer fileType) {
        this.fileType = fileType;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    @Override
    public String toString() {
        return "OrderExcelInfoResponseDTO{" +
                "id=" + id +
                ", fileName='" + fileName + '\'' +
                ", exportDate=" + exportDate +
                ", userId=" + userId +
                ", userName='" + userName + '\'' +
                ", status=" + status +
                ", fileType=" + fileType +
                ", fileUrl='" + fileUrl + '\'' +
                '}';
    }
}
