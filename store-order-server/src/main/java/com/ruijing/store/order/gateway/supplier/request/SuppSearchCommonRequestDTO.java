package com.ruijing.store.order.gateway.supplier.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2023/2/1 16:24
 * @description
 */
@RpcModel("供应商查询通用请求体")
public class SuppSearchCommonRequestDTO implements Serializable {

    private static final long serialVersionUID = -5340815197615401525L;
    
    @RpcModelProperty("供应商名")
    private String suppName;

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", SuppSearchCommonRequestDTO.class.getSimpleName() + "[", "]")
                .add("suppName='" + suppName + "'")
                .toString();
    }
}
