package com.ruijing.store.order.base.timeoutstatistics.service.impl;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.research.statement.api.statement.dto.StatementTimeoutDTO;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.message.api.dto.MessageDTO;
import com.ruijing.message.api.enums.MessageTypeEnum;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.utils.DictionaryUtils;
import com.ruijing.search.client.enums.SortOrder;
import com.ruijing.search.client.filter.*;
import com.ruijing.search.client.query.MultiPhraseQuery;
import com.ruijing.search.client.query.NestedQuery;
import com.ruijing.search.client.query.OrQuery;
import com.ruijing.search.client.request.Request;
import com.ruijing.search.client.sort.FieldSortItem;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.base.enums.*;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailTimeOutDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterTimeOutDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.TimeOutOrderParamsDTO;
import com.ruijing.store.order.api.base.other.dto.OrderTimeOutDTO;
import com.ruijing.store.order.api.base.other.dto.TimeoutStatisticsDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.general.enums.OrderNestedEnum;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.base.baseconfig.dto.OrganizationConfigDTO;
import com.ruijing.store.order.base.baseconfig.enums.TimeOutConfigType;
import com.ruijing.store.order.base.core.enums.DangerousEnum;
import com.ruijing.store.order.base.core.enums.TimeOutEnums;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.freezedeptlog.mapper.FreezeDeptLogDOMapper;
import com.ruijing.store.order.base.freezedeptlog.model.FreezeDeptLogDO;
import com.ruijing.store.order.base.minor.mapper.DangerousTagDOMapper;
import com.ruijing.store.order.base.minor.model.DangerousTagDO;
import com.ruijing.store.order.base.timeoutstatistics.mapper.TimeoutStatisticsMapper;
import com.ruijing.store.order.base.timeoutstatistics.model.TimeoutStatisticsDO;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutQueryService;
import com.ruijing.store.order.base.timeoutstatistics.translator.OrderMasterTimeOutTranslator;
import com.ruijing.store.order.base.util.OrderCommonUtils;
import com.ruijing.store.order.business.enums.DangerousTagEnum;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.service.OrderAnnotationService;
import com.ruijing.store.order.business.service.OrderDetailRelatedService;
import com.ruijing.store.order.business.service.OrderMasterForScheduledService;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.gateway.buyercenter.request.timeout.TimeOutNoticeItemDTO;
import com.ruijing.store.order.gateway.buyercenter.request.timeout.TimeOutNoticeRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.vo.OverTimeSettingVO;
import com.ruijing.store.order.gateway.buyercenter.vo.TimeOutTipsVO;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.util.IllegalInputControlUtil;
import com.ruijing.store.user.api.dto.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ruijing.store.exception.enums.ExecptionMessageEnum.CONFIG_RETRIEVAL_FAILED;
import static com.ruijing.store.order.api.base.enums.TimeOutBusinessType.*;
import static java.util.stream.Collectors.toList;

/**
 * @author: liwenyu
 * @createTime: 2023-03-10 10:10
 * @description:
 **/
@Service
public class TimeoutQueryServiceImpl implements TimeoutQueryService {

    private final String CAT_TYPE = TimeoutQueryServiceImpl.class.getName();

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 一天的微秒数
     */
    private final long oneDayTimeMillis = 24 * 60 * 60 * 1000;

    /**
     *  结算超时订单状态
      */
    private final List<Integer> balanceTimeOutStatusList = New.list(OrderStatusEnum.WaitingForStatement_1.getValue(), OrderStatusEnum.OrderReceiveApproval.getValue());
    /**
     *  验收超时订单状态
     */
    private final List<Integer> examineTimeOutStatusList = New.list(OrderStatusEnum.WaitingForReceive.getValue());
    /**
     *  需过滤的退货状态
     */
    private final List<Integer> notOverTimeReturnStatus = New.list(0, 1, 2, 4);

    /**
     *  超时配置项
     */
    private final List<String> timeOutConfigTypeList = New.list(TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getCode(), TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS.getCode());

    /**
     * 需要校验采购人的枚举
     */
    private final List<ValidationScopeEnum> VALID_PURCHASER_ENUMS = New.list(ValidationScopeEnum.ALL, ValidationScopeEnum.ONLY_CHECK_PERSON);

    /**
     * 需要校验部门的枚举
     */
    private final List<ValidationScopeEnum> VALID_DEPT_ENUMS = New.list(ValidationScopeEnum.ALL, ValidationScopeEnum.ONLY_CHECK_DEPT);


    /**
     * 个人验收入库审批超时提醒文案
     */
    private final String PERSONAL_ACCEPT_APPROVE_OR_WAREHOUSE_MSG = "你已有{}张订单超过{}天未完成入库，请完成入库并结算后再进行采购。";


    /**
     * 部门验收入库审批超时提醒文案
     */
    private final String DEPT_ACCEPT_APPROVE_OR_WAREHOUSE_MSG = "你所在的{departName}有{}张订单超过{}天未完成入库，请完成入库并结算后再进行采购";


    /**
     * 个人结算超时提醒文案
     */
    private final String PERSONAL_BALANCE_MSG = "你已有{}张订单超过{}天未发起结算，请发起结算后再进行采购。";

    /**
     * 部门结算超时提醒文案
     */
    private final String DEPT_BALANCE_MSG = "你所在的{departName}有{}张订单超过{}天未发起结算，请发起结算后再进行采购。";


    /**
     * 验收超时提醒文案
     */
    private final String PERSONAL_ACCEPTANCE_MSG = "你已有{}张订单超过{}天未验收，请验收后再进行采购。";


    /**
     * 验收超时提醒文案
     */
    private final String DEPT_ACCEPTANCE_MSG = "你所在的{}有{}张订单超过{}天未验收，请验收后再进行采购。";



    /**
     *  结算超时查的时间字段
     */
    private final String balanceTimeOutQueryField = "flastreceivedate";

    /**
     * 验收超时查的时间字段
     */
    private final String examineTimeOutQueryField = "fdeliverydate";

    /**
     * 订单详情表
     */
    private final String ORDER_DETAIL_NESTED_TABLE = "order_detail";

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;


    @Resource
    private DangerousTagDOMapper dangerousTagMapper;

    @Resource
    private FreezeDeptLogDOMapper freezeDeptLogDOMapper;

    @Resource
    private TimeoutStatisticsMapper timeoutStatisticsMapper;

    @Resource
    private ResearchStatementClient researchStatementClient;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderEmailHandler orderEmailHandler;

    @Resource
    private OrganizationClient organizationClient;

    @Resource
    private OrderAnnotationService orderAnnotationService;

    @Resource
    private DepartmentRpcClient departmentRpcClient;

    @Override
    public List<TimeOutTipsVO> checkDeptTimeoutStat(Integer orgId, Integer deptId) {
        List<TimeOutTipsVO> timeOutTipsVOList = this.checkOrderTimeoutStat(orgId, null, New.list(deptId), null, ValidationScopeEnum.ONLY_CHECK_DEPT, false);
        if(CollectionUtils.isEmpty(timeOutTipsVOList)){
            // 由于只需要获取一条且接口调用较频繁，为加快速度，前面的没超时才需要判断结算是否超时
            return this.getBalanceTimeoutStat(orgId, deptId);
        }
        return timeOutTipsVOList;
    }

    @Override
    public List<TimeOutTipsVO> checkOrderAcceptanceStat(Integer orgId, Integer userId) {
        List<DepartmentDTO> deptList = userClient.getDepartmentsForUser(userId, orgId);
        if(CollectionUtils.isEmpty(deptList)){
            return New.emptyList();
        }
        //此处可能会有问题，部门不是父子继承关系
        List<Integer> deptIds = deptList.stream()
                    .map(DepartmentDTO::getId)
                    .collect(toList());
        return this.checkOrderTimeoutStat(orgId, userId,deptIds, ACCEPTANCE.getValue(), ValidationScopeEnum.ALL, true);
    }

    @Override
    public List<TimeOutTipsVO> checkOrderBalanceStat(Integer orgId, Integer userId) {
        List<DepartmentDTO> deptList = userClient.getDepartmentsForUser(userId, orgId);
        if(CollectionUtils.isEmpty(deptList)){
            return New.emptyList();
        }
        //此处可能会有问题，部门不是父子继承关系
        List<Integer> deptIds = deptList.stream()
                    .map(DepartmentDTO::getId)
                    .collect(toList());
        return this.checkOrderTimeoutStat(orgId, userId, deptIds, BALANCE.getValue(), ValidationScopeEnum.ALL, true);
    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public BasePageResponseDTO<OrderMasterTimeOutDTO> findTimeOutOrders(TimeOutOrderParamsDTO params) {
        Preconditions.notNull(params.getUserId(), "单位id不可空！");
        Preconditions.isTrue(params.getPageSize() != null && params.getPageNo() != null, "分页参数不可空！");
        Preconditions.notNull(params.getOverTimeType(), "超时类型不可空！");

        if(params.getOrgCode() == null && params.getUserId() != null){
            params.setOrgCode(organizationClient.findSimpleOrgDTOById(params.getUserId()).getCode());
        }else {
            BusinessErrUtil.notNull(params.getOrgCode(), "单位参数不可空");
        }
        // 1.获取医院机构的配置
        Map<String, Integer> timeOutConfigMap = this.getOverTimeConfigMap(params.getOrgCode());
        // 获取配置的结算超时天数
        final int balanceLimitDays = timeOutConfigMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getCode());
        // 验收超时天数
        final int examineLimitDays = timeOutConfigMap.get(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS.getCode());
        // 当前时间
        long currentTimeMillis = System.currentTimeMillis();
        // 2.查超时订单
        SearchPageResultDTO<OrderMasterSearchDTO> result = this.queryTimeOutOrder(params, currentTimeMillis, balanceLimitDays, examineLimitDays);
        List<OrderMasterTimeOutDTO> timeOutOrders = New.emptyList();
        // 3.数据组装
        if (result.getTotalHits() > 0) {
            timeOutOrders = result.getRecordList().stream().map(searchDTO -> {
                OrderMasterTimeOutDTO orderMasterTimeOutDTO = OrderMasterTimeOutTranslator.searchMasterDto2TimeoutDto(searchDTO);
                // 3.1订单超时类型、时间处理
                if(balanceTimeOutStatusList.contains(searchDTO.getStatus())){
                    orderMasterTimeOutDTO.setWaitingBalanceDays(this.getDiffDays(searchDTO.getFlastreceivedate().getTime(), currentTimeMillis));
                    orderMasterTimeOutDTO.setWaitingExamineDays(this.getDiffDays(searchDTO.getFdeliverydate().getTime(), searchDTO.getFlastreceivedate().getTime()));
                }else {
                    orderMasterTimeOutDTO.setWaitingExamineDays(this.getDiffDays(searchDTO.getFdeliverydate().getTime(), currentTimeMillis));
                }
                executeTimeOutType(balanceLimitDays, examineLimitDays, orderMasterTimeOutDTO);
                return orderMasterTimeOutDTO;
            }).collect(Collectors.toList());

            // 3.2 处理订单商品危化品
            List<OrderDetailTimeOutDTO> orderDetailTimeOutDTOList = timeOutOrders.stream()
                    .flatMap(orderMasterTimeOutDTO -> orderMasterTimeOutDTO.getOrderDetails().stream())
                    .collect(toList());
            executeDetailDangerousTag(orderDetailTimeOutDTOList);
        }
        // 4.返回结果
        return new BasePageResponseDTO<>(params.getPageNo(), params.getPageSize(), result.getTotalHits(), timeOutOrders);
    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public SearchPageResultDTO<OrderMasterSearchDTO> queryTimeOutOrder(TimeOutOrderParamsDTO params, int balanceDaysInt, int examineDaysInt) {
        return this.queryTimeOutOrder(params, System.currentTimeMillis(), balanceDaysInt, examineDaysInt);
    }


    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public Map<Integer, Integer> queryTimeOutOrderCountByDepartmentId(TimeOutOrderParamsDTO params, int balanceDaysInt, int examineDaysInt) {
        return this.queryTimeOutOrderCountByDepartmentId(params, System.currentTimeMillis(), balanceDaysInt, examineDaysInt);
    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public SearchPageResultDTO<OrderMasterSearchDTO> queryTimeOutAccOrWhOrder(TimeOutOrderParamsDTO params, int balanceDaysInt, boolean useWareHouseSystem) {
        long currentTimeMillis = System.currentTimeMillis();
        params.setOverTimeType(TimeOutEnums.BALANCE.getType());
        Request request = this.constructCommonTimeOutQueryParam(params);

        // 计算超时时间，进行范围过滤
        Date timeOutLimitDate = new Date(currentTimeMillis - (long) (balanceDaysInt - 1) * oneDayTimeMillis);
        RangeFilter timeRangeFilter = new RangeFilter(balanceTimeOutQueryField, null, DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, timeOutLimitDate), true, false);
        request.addFilter(timeRangeFilter);

        // 验收超时过滤
        AndFilter accFilter = new AndFilter()
                .addFilter(new TermFilter("status", New.list(OrderStatusEnum.WaitingForReceive.getValue())));

        Filter finalFilter = accFilter;

        if(useWareHouseSystem){
            // 用入库系统就需要入库超时过滤
            AndFilter whTimeOutFilter = new AndFilter()
                    .addFilter(new TermFilter("status", New.list(OrderStatusEnum.WaitingForStatement_1.getValue())))
                    .addFilter(new TermFilter("inventory_status", OrderCommonUtils.NOT_WAREHOUSE_INVENTORY_STATUS_LIST));

            OrFilter orFilter = new OrFilter()
                    .addFilter(accFilter)
                    .addFilter(whTimeOutFilter);

            finalFilter = new AndFilter()
                    .addFilter(orFilter);
        }
        request.addFilter(finalFilter);

        return orderSearchBoostService.search(request);
    }

    @Override
    public void noticeUserByEmail(TimeOutNoticeRequestDTO timeOutNoticeRequestDTO) {
        TimeOutEnums timeOutEnums = parseBatchType2TimeOutEnums(timeOutNoticeRequestDTO.getBatchType());
        BusinessErrUtil.notNull(timeOutEnums, "无效的超时类型！");
        Integer orgId = timeOutNoticeRequestDTO.getOrgId();
        Map<Integer, List<Integer>> deptIdOrderIdMap = DictionaryUtils.groupFieldByKey(timeOutNoticeRequestDTO.getDtos(), TimeOutNoticeItemDTO::getDeptId, TimeOutNoticeItemDTO::getOrderId);

        TimeOutOrderParamsDTO timeOutOrderParamsDTO = new TimeOutOrderParamsDTO();
        timeOutOrderParamsDTO.setUserId(orgId);
        timeOutOrderParamsDTO.setOverTimeType(timeOutEnums.getType());
        timeOutOrderParamsDTO.setPageNo(1);
        timeOutOrderParamsDTO.setPageSize(200);

        //获取邮件模板
        byte[] strBuffer = getStrBuffer(timeOutEnums);
        for(Map.Entry<Integer, List<Integer>> entry : deptIdOrderIdMap.entrySet()){
            Integer deptId = entry.getKey();
            timeOutOrderParamsDTO.setDepartmentIds(New.list(deptId));
            BasePageResponseDTO<OrderMasterTimeOutDTO> responseDTO = this.findTimeOutOrders(timeOutOrderParamsDTO);
            if(CollectionUtils.isEmpty(responseDTO.getData())){
                continue;
            }
            Map<Integer, List<String>> typeOrderNoMap = DictionaryUtils.groupFieldByKey(responseDTO.getData(), OrderMasterTimeOutDTO::getTimeOutType, OrderMasterTimeOutDTO::getOrderNo);
            List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(entry.getValue());
            if(CollectionUtils.isEmpty(orderMasterDOList)){
                continue;
            }
            List<UserBaseInfoDTO> buyers = userClient.getUserByIdsAndOrgId(orderMasterDOList.stream().map(OrderMasterDO::getFbuyerid).collect(Collectors.toSet()), timeOutNoticeRequestDTO.getOrgId());
            if(CollectionUtils.isEmpty(buyers)){
                continue;
            }
            Set<String> emails = buyers.stream().map(UserBaseInfoDTO::getEmail).filter(Objects::nonNull).collect(Collectors.toSet());
            if(CollectionUtils.isEmpty(emails)){
                continue;
            }
            this.excuseNumAndSendEMail(timeOutEnums, orgId, strBuffer, deptId, emails, typeOrderNoMap);
        }
    }

    @Override
    public OverTimeSettingVO getOldOverTimeSetting(Integer orgId) {
        OrganizationDTO orgDTO = userClient.getOrgById(orgId);
        BusinessErrUtil.notNull(orgDTO, "机构信息无效！");
        String orgCode = orgDTO.getCode();
        List<BaseConfigDTO> baseConfigDTOList = sysConfigClient.getValueByOrgCodeAndConfigCode(orgCode, timeOutConfigTypeList);
        Set<String> baseConfigCodeSet = baseConfigDTOList.stream()
                .map(BaseConfigDTO::getConfigCode)
                .collect(Collectors.toSet());

        for (String timeOutConfigType : timeOutConfigTypeList) {
            if (!baseConfigCodeSet.contains(timeOutConfigType)) {
                BaseConfigDTO baseConfigDTO = new BaseConfigDTO();
                baseConfigDTO.setOrgCode(orgCode);
                baseConfigDTO.setOrgId(orgId);
                baseConfigDTO.setConfigCode(timeOutConfigType);
                baseConfigDTO.setConfigValue(String.valueOf(TimeOutConfigType.getByCode(timeOutConfigType).getDefaultSet()));
                baseConfigDTOList.add(baseConfigDTO);
            }
        }

        AtomicReference<Integer> acceptDay = new AtomicReference<>(getLimitDay(baseConfigDTOList, TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS));
        AtomicReference<Integer> balanceDay = new AtomicReference<>(getLimitDay(baseConfigDTOList, TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS));

        List<TimeoutStatisticsDO> timeoutStatisticsDOList = timeoutStatisticsMapper.queryByOrgIdAndDepIdIn(orgId, null);
        Map<Integer, TimeoutStatisticsDO> type2TimeoutStatisticsDOSMap = timeoutStatisticsDOList.stream()
                .collect(Collectors.toMap(TimeoutStatisticsDO::getType, Function.identity(), (v1, v2) -> v2));
        if(CollectionUtils.isNotEmpty(timeoutStatisticsDOList)){
            type2TimeoutStatisticsDOSMap.forEach((type, list) -> {
                if(BALANCE.getValue().equals(type)){
                    balanceDay.set(list.getOldConfigDay());
                }else if(ACCEPTANCE.getValue().equals(type)){
                    acceptDay.set(list.getOldConfigDay());
                }
            });
        }
        return new OverTimeSettingVO(orgId, acceptDay.get(), balanceDay.get());
    }

    @Override
    public List<TimeOutTipsVO> checkPersonalTimeoutStat(Integer orgId, Integer userId) {
        return this.checkOrderTimeoutStat(orgId, userId, null, null, ValidationScopeEnum.ONLY_CHECK_PERSON, false);
    }


    /**
     * 获取超时时间
     *
     * @param baseConfigDTOList
     * @param type  com.ruijing.store.order.base.baseconfig.enums.TimeOutConfigType
     * @return
     */
    private int getLimitDay(List<BaseConfigDTO> baseConfigDTOList, TimeOutConfigType type){
        BusinessErrUtil.notNull(type, "timeOutConfigType不能为空");
        List<Integer> limitDayList = baseConfigDTOList.stream()
                .filter(baseConfigDTO -> baseConfigDTO.getConfigCode().equals(type.getCode()))
                .map(BaseConfigDTO::getConfigValue)
                .map(Integer::parseInt)
                .collect(toList());
        if(limitDayList.size() != 1){
            BusinessErrUtil.isTrue(false, "系统配置异常");
        }
        return limitDayList.get(0);
    }

    private TimeOutEnums parseBatchType2TimeOutEnums(int batchType){
        switch (batchType){
            case 0:
                return TimeOutEnums.ALL_TIMEOUT_TYPE;
            case 1:
                return TimeOutEnums.BALANCE;
            case 2:
                return TimeOutEnums.EXAMINE;
            default:
                return null;
        }
    }

    /**
     * 处理信息且发送邮件
     * @param timeOutEnums 邮件提醒类型
     * @param orgId 医院id
     * @param strBuffer 邮件模板字节码
     * @param depId 部门id
     * @param emails 邮件地址
     */
    private void excuseNumAndSendEMail(TimeOutEnums timeOutEnums, Integer orgId, byte[] strBuffer, Integer depId,
                                       Set<String> emails, Map<Integer, List<String>> typeOrderNoMap) {
        //根据超时类型 读取邮件模板
        List<String> examineOrderNos = typeOrderNoMap.getOrDefault(TimeOutEnums.EXAMINE.getType(), New.list());
        List<String> balanceOrderNos = typeOrderNoMap.getOrDefault(TimeOutEnums.BALANCE.getType(), New.list());
        // 全部全部超时即结算超时，且验收时间也超了。这种情况下订单状态非待验收，不计入验收超时，计入结算超时中。
        balanceOrderNos.addAll(typeOrderNoMap.getOrDefault(TimeOutEnums.ALL_TIMEOUT_TYPE.getType(), New.emptyList()));
        String examineTypeAmount = !examineOrderNos.isEmpty() ? Integer.toString(examineOrderNos.size()) : "多";
        String balanceTypeAmount = !balanceOrderNos.isEmpty() ? Integer.toString(balanceOrderNos.size()) : "多";

        String emailTemplate = new String(strBuffer, StandardCharsets.UTF_8);
        String content = "";
        String title = "";
        DepartmentDTO department = userClient.getDepartmentInfo(depId);
        switch (timeOutEnums) {
            case ALL_TIMEOUT_TYPE:
                content = emailTemplate.replace("#num#", examineTypeAmount)
                        .replace("#deptName#", department.getName())
                        .replace("#orderNos#", String.join("<br>", examineOrderNos))
                        .replace("#orderNosB#", String.join("<br>", balanceOrderNos))
                        .replace("#orderNum#", balanceTypeAmount);
                title = "订单验收/结算提醒【锐竞采购平台】";
                break;
            case BALANCE:
                content = emailTemplate.replace("#deptName#", department.getName())
                        .replace("#orderNos#", String.join("<br>", balanceOrderNos))
                        .replace("#orderNum#", balanceTypeAmount);
                title = "订单结算提醒【锐竞采购平台】";
                break;
            case EXAMINE:
                content = emailTemplate.replace("#deptName#", department.getName())
                        .replace("#orderNos#", String.join("<br>", examineOrderNos))
                        .replace("#num#", examineTypeAmount);
                title = "订单验收提醒【锐竞采购平台】";
                break;
            default:
                break;
        }
        MessageDTO<Object> messageDTO = new MessageDTO<>();
        messageDTO.setSubject(title);
        messageDTO.setContent(content);
        messageDTO.setMessageType(MessageTypeEnum.EMAIL_HTML);
        messageDTO.setReceiver(emails.toArray(new String[]{}));
        orderEmailHandler.sendEmail(messageDTO);
    }

    /**
     * 获取超时配置映射
     * @param orgCode 机构代码
     * @return 映射
     */
    private Map<String, Integer> getOverTimeConfigMap(String orgCode){
        // 1.获取医院机构的配置
        List<BaseConfigDTO> configs = New.emptyList();
        try {
            configs = sysConfigClient.getValueByOrgCodeAndConfigCode(orgCode, Arrays.stream(TimeOutConfigType.values()).map(TimeOutConfigType::getCode).collect(toList()));
        } catch (Exception e) {
            Cat.logError(CAT_TYPE,"findTimeOutOrders","获取配置异常",e);
        }

        return DictionaryUtils.toMap(configs, BaseConfigDTO::getConfigCode, config->NumberUtils.toInt(config.getConfigValue(), TimeOutConfigType.getByCode(config.getConfigCode()).getDefaultSet()));
    }

    /**
     * 读取邮件模板
     * @param timeOutEnums 超时类型
     * @return 文件流
     */
    private byte[] getStrBuffer(TimeOutEnums timeOutEnums) {
        String fileName = null;
        switch (timeOutEnums) {
            case ALL_TIMEOUT_TYPE:
                fileName = "timeOutAll.txt";
                break;
            case BALANCE:
                fileName = "balanceTimeOut.txt";
                break;
            case EXAMINE:
                fileName = "acceptanceTimeOut.txt";
                break;
            default:
                BusinessErrUtil.isTrue(false, "无对应文件名");
                break;
        }
        Path path = Paths.get("email-templet", fileName);
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        byte[] bytes = null;
        //获取文件输入流 文件输出流
        try (InputStream inputStream = classLoader.getResourceAsStream(path.toString());
             ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while (true) {
                try {
                    if ((bytesRead = inputStream.read(buffer)) == -1) break;
                    byteArrayOutputStream.write(buffer, 0, bytesRead);
                } catch (IOException e) {
                    BusinessErrUtil.isTrue(false, "读取文件异常");
                }
            }
            bytes = byteArrayOutputStream.toByteArray();
        } catch (IOException e) {
            BusinessErrUtil.isTrue(false, "找不到文件路径");
        }
        BusinessErrUtil.notNull(bytes, "读取文件异常");
        return bytes;
    }

    /**
     * 获取超时订单提示
     *
     * @param orgId
     * @param userId
     * @param deptIds
     * @param type 超时类型
     * @param validationScopeEnum 校验范围（全部。课题组，个人）
     * @param isPurchaserPriority 是否优先采购人（有采购人超时，先返回采购人，没有就查课题组）
     * @return
     */
    private List<TimeOutTipsVO> checkOrderTimeoutStat(Integer orgId, Integer userId, List<Integer> deptIds, Integer type, ValidationScopeEnum validationScopeEnum, Boolean isPurchaserPriority) {


        List<TimeOutTipsVO> timeOutTipsVOList = New.list();

        //采购人限制（默认不开启）
        boolean isPersonalLimitOn = false;

        //采购部门限制（默认开启）
        boolean isDeptLimitOn = true;

        List<String> timeoutCode = Arrays.stream(TimeOutConfigType.values()).map(TimeOutConfigType::getCode).collect(toList());
        // 查询用户/机构的有效配置
        Map<String, String> timeOutCodeMap = sysConfigClient.getConfigMapByOrgIdAndConfigCode(New.list(orgId), timeoutCode);

        isPersonalLimitOn = "1".equals(timeOutCodeMap.getOrDefault(TimeOutConfigType.ORDER_TIMEOUT_PURCHASER_LIMIT.getCode(), "0"));
        isDeptLimitOn = "1".equals(timeOutCodeMap.getOrDefault(TimeOutConfigType.ORDER_TIMEOUT_DEPT_LIMIT.getCode(), "1"));

        String purchaserLimit = timeOutCodeMap.get(TimeOutConfigType.ORDER_TIMEOUT_PURCHASER_LIMIT.getCode());
        String deptLimit = timeOutCodeMap.get(TimeOutConfigType.ORDER_TIMEOUT_DEPT_LIMIT.getCode());

        //课题组超时张数
        int deptExamineLimitAmount = Integer.parseInt(timeOutCodeMap.get(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_AMOUNT.getCode()));
        int deptBalanceLimitAmount = Integer.parseInt(timeOutCodeMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_AMOUNT.getCode()));

        //采购人限制张数
        int purchaserExamineLimitAmount = Integer.parseInt(timeOutCodeMap.get(TimeOutConfigType.PURCHASER_EXAMINE_CYCLE_LIMIT_AMOUNT.getCode()));
        int purchaserBalanceLimitAmount = Integer.parseInt(timeOutCodeMap.get(TimeOutConfigType.PURCHASER_BALANCE_CYCLE_LIMIT_AMOUNT.getCode()));

        //超时天数
        Integer examineLimitDay = Integer.parseInt(timeOutCodeMap.get(TimeOutConfigType.EXAMINE_CYCLE_LIMIT_DAYS.getCode()));
        Integer balanceLimitDay = Integer.parseInt(timeOutCodeMap.get(TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getCode()));
        if("0".equals(purchaserLimit) && "0".equals(deptLimit)){
            return New.list();
        }

        if (ACCEPTANCE.getValue().equals(type)) {
            this.handleAcceptance(isPersonalLimitOn, isDeptLimitOn, orgId, userId, examineLimitDay, deptExamineLimitAmount, purchaserExamineLimitAmount, deptIds, timeOutTipsVOList, validationScopeEnum, isPurchaserPriority);
        } else if (BALANCE.getValue().equals(type)) {
            this.handleBalance(isPersonalLimitOn, isDeptLimitOn, orgId, userId, balanceLimitDay, deptBalanceLimitAmount, purchaserBalanceLimitAmount, deptIds, timeOutTipsVOList, validationScopeEnum, isPurchaserPriority);
        } else {
            this.handleAcceptance(isPersonalLimitOn, isDeptLimitOn, orgId, userId, examineLimitDay, deptExamineLimitAmount, purchaserExamineLimitAmount, deptIds, timeOutTipsVOList, validationScopeEnum, false);
            this.handleBalance(isPersonalLimitOn, isDeptLimitOn, orgId, userId, balanceLimitDay, deptBalanceLimitAmount, purchaserBalanceLimitAmount, deptIds, timeOutTipsVOList, validationScopeEnum, false);
        }
        if(CollectionUtils.isEmpty(timeOutTipsVOList)){
            return New.emptyList();
        }
        return timeOutTipsVOList.stream()
                .sorted(Comparator.comparing(TimeOutTipsVO::getSort))
                .collect(toList());
    }


    /**
     * 处理验收超时
     *
     * @param isPersonalLimitOn
     * @param orgId
     * @param userId
     * @param examineLimitDay
     * @param examineLimitAmount
     * @param deptIdList
     * @return Map<Integer, Integer> 部门 -> 验收超时的订单
     */
    public Map<Integer, Integer> handleAcceptance(Boolean isPersonalLimitOn,
                                  Boolean isDeptLimitOn,
                                  Integer orgId,
                                  Integer userId,
                                  Integer examineLimitDay,
                                  Integer deptExamineLimitAmount,
                                  Integer purchaserExamineLimitAmount,
                                  List<Integer> deptIdList,
                                  List<TimeOutTipsVO> timeOutTipsVOList,
                                  ValidationScopeEnum validationScopeEnum,
                                  Boolean isPurchaserPriority){
        if (isPersonalLimitOn && VALID_PURCHASER_ENUMS.contains(validationScopeEnum)) {
            //个人超时订单数量
            long personalExamineOverTimeCount = orderAnnotationService.getOverTimeCount(orgId, userId, null, 0, examineLimitDay, TimeOutEnums.EXAMINE);
            boolean isOverLimit = personalExamineOverTimeCount >= purchaserExamineLimitAmount;
            if(isOverLimit){
                timeOutTipsVOList.add(new TimeOutTipsVO().setSort(3).setType(ACCEPTANCE.getValue()).setMessage(StrUtil.format(PERSONAL_ACCEPTANCE_MSG, personalExamineOverTimeCount, examineLimitDay)));
            }
            //超过限制 并且采购人提示优先 直接返回
            if(isPurchaserPriority && isOverLimit){
                return New.map();
            }
        }
        if (!isDeptLimitOn || CollectionUtils.isEmpty(deptIdList)) {
            return New.map();
        }
        Map<Integer, Integer> deptId2ExamineOverTimeCountMap = this.getOverTimeCountGroupByDepartmentId(orgId, null, deptIdList, 0, examineLimitDay, TimeOutEnums.EXAMINE);
        Map<Integer, Integer> result = New.map();
        List<DepartmentDTO> departmentDTOList = departmentRpcClient.getDepartmentsByIds(deptIdList.stream().map(Long::new).collect(toList()));
        Map<Integer, String> deptId2deptName = DictionaryUtils.toMap(departmentDTOList, DepartmentDTO::getId, DepartmentDTO::getName);
        for(Integer deptId : deptIdList){
            Integer examineOverTimeCount = deptId2ExamineOverTimeCountMap.getOrDefault(deptId, 0);
            if(examineOverTimeCount < deptExamineLimitAmount){
                continue;
            }
            timeOutTipsVOList.add(new TimeOutTipsVO().setSort(3).setType(ACCEPTANCE.getValue()).setMessage(StrUtil.format(DEPT_ACCEPTANCE_MSG, deptId2deptName.get(deptId), examineOverTimeCount, examineLimitDay)));
            result.put(deptId, examineOverTimeCount);
        }
        return result;
    }


    /**
     * 处理结算超时
     *
     * @param isPersonalLimitOn
     * @param orgId
     * @param userId
     * @param balanceLimitDay
     * @param balanceLimitAmount
     * @param deptIdList
     * @param timeOutTipsVOList
     *
     * @return 部门 -> 结算超时的订单
     */
    public Map<Integer, Integer> handleBalance(Boolean isPersonalLimitOn,
                               Boolean isDeptLimitOn,
                               Integer orgId,
                               Integer userId,
                               Integer balanceLimitDay,
                               Integer deptBalanceLimitAmount,
                               Integer purchaserBalanceLimitAmount,
                               List<Integer> deptIdList,
                               List<TimeOutTipsVO> timeOutTipsVOList,
                               ValidationScopeEnum validationScopeEnum,
                               Boolean isPurchaserPriority){
        //是否使用库房配置
        List<BaseConfigDTO> configDTOList = sysConfigClient.getValueByOrgIdAndConfigCode(New.list(orgId), New.list(ConfigConstant.USE_WAREHOUSE_SYSTEM));

        BusinessErrUtil.notEmpty(configDTOList, CONFIG_RETRIEVAL_FAILED);
        BaseConfigDTO warehouseConfig = configDTOList.get(0);
        if (isPersonalLimitOn && VALID_PURCHASER_ENUMS.contains(validationScopeEnum)) {
            long accOrWhTimeOut = 0;
            if(ConfigConstant.USE_WAREHOUSE_SYSTEM_VALUE.equals(warehouseConfig.getConfigValue())){
                //验收审批或入库超时数量
                accOrWhTimeOut = orderAnnotationService.getOverTimeCount(orgId, userId, null, balanceLimitDay, 0, TimeOutEnums.ACCEPT_APPROVE_OR_WAREHOUSE);
            }
            //结算超时订单数量 (和验收审批或入库超时数量 互斥 只展示一个 默认先展示【验收审批或入库超时数量】)
            long allBalanceTimeOut = orderAnnotationService.getOverTimeCount(orgId, userId, null, balanceLimitDay, 0, TimeOutEnums.BALANCE);
            this.distinguishTimeoutSettlementTypes(accOrWhTimeOut, allBalanceTimeOut, purchaserBalanceLimitAmount, balanceLimitDay, timeOutTipsVOList, PERSONAL_BALANCE_MSG, PERSONAL_ACCEPT_APPROVE_OR_WAREHOUSE_MSG);
            if(isPurchaserPriority && (accOrWhTimeOut >= purchaserBalanceLimitAmount || allBalanceTimeOut >= purchaserBalanceLimitAmount)){
                return New.map();
            }
        }

        if(!isDeptLimitOn || CollectionUtils.isEmpty(deptIdList)){
            return New.map();
        }
        Map<Integer, Integer> deptId2AccOrWhTimeOut = New.map();
        if(ConfigConstant.USE_WAREHOUSE_SYSTEM_VALUE.equals(warehouseConfig.getConfigValue())) {
             deptId2AccOrWhTimeOut = this.getOverTimeCountGroupByDepartmentId(orgId, null, deptIdList, balanceLimitDay, 0, TimeOutEnums.ACCEPT_APPROVE_OR_WAREHOUSE);
        }
        Map<Integer, Integer> deptId2AllBalanceOverTimeCount = this.getOverTimeCountGroupByDepartmentId(orgId, null, deptIdList, balanceLimitDay, 0, TimeOutEnums.BALANCE);
        Map<Integer, Integer> result = New.map();
        List<DepartmentDTO> departmentDTOList = departmentRpcClient.getDepartmentsByIds(deptIdList.stream().map(Long::new).collect(toList()));
        Map<Integer, String> deptId2deptName = DictionaryUtils.toMap(departmentDTOList, DepartmentDTO::getId, DepartmentDTO::getName);
        for(Integer deptId : deptIdList){
            Integer accOrWhTimeOut = deptId2AccOrWhTimeOut.getOrDefault(deptId, 0);
            Integer allBalanceTimeOut = deptId2AllBalanceOverTimeCount.getOrDefault(deptId, 0);
            Map<String, String> map = new HashMap<>(1);
            map.put("departName", deptId2deptName.get(deptId));
            this.distinguishTimeoutSettlementTypes(accOrWhTimeOut, allBalanceTimeOut, deptBalanceLimitAmount, balanceLimitDay, timeOutTipsVOList, StrUtil.format(DEPT_BALANCE_MSG, map), StrUtil.format(DEPT_ACCEPT_APPROVE_OR_WAREHOUSE_MSG, map));
            if(allBalanceTimeOut >= deptBalanceLimitAmount){
                result.put(deptId, allBalanceTimeOut);
            }
        }
        return result;
    }
    /**
     * 区分超时结算类型。并设置文案
     *
     * @param accOrWhTimeOut
     * @param allBalanceTimeOut
     * @param balanceLimitAmount
     * @param balanceLimitDay
     * @param timeOutTipsVOList
     * @param balanceMsg
     * @param acceptApproveOrWarehouseMsg
     */
    private void distinguishTimeoutSettlementTypes(long accOrWhTimeOut, long allBalanceTimeOut, int balanceLimitAmount, int balanceLimitDay, List<TimeOutTipsVO> timeOutTipsVOList, String balanceMsg, String acceptApproveOrWarehouseMsg){
        boolean isAccOrWhTimeOutOverLimit = accOrWhTimeOut >= balanceLimitAmount;
        boolean isBalanceOverLimit = allBalanceTimeOut >= balanceLimitAmount;
        if(isAccOrWhTimeOutOverLimit){
            timeOutTipsVOList.add(new TimeOutTipsVO().setSort(3).setType(ACCEPT_APPROVE_OR_WAREHOUSE.getValue()).setMessage(StrUtil.format(acceptApproveOrWarehouseMsg, accOrWhTimeOut, balanceLimitDay)));
        }
        if(!isAccOrWhTimeOutOverLimit && isBalanceOverLimit){
            timeOutTipsVOList.add(new TimeOutTipsVO().setSort(3).setType(BALANCE.getValue()).setMessage(StrUtil.format(balanceMsg, allBalanceTimeOut, balanceLimitDay)));
        }
    }

    /**
     * 获取结算级别的超时状况
     * @param orgId 单位id
     * @param deptId 课题组id
     * @return 超时状况
     */
    private List<TimeOutTipsVO> getBalanceTimeoutStat(Integer orgId, Integer deptId){
        StatementTimeoutDTO statementTimeoutDTO = researchStatementClient.listStatementTimeoutData(orgId, deptId);
        if(statementTimeoutDTO == null){
            // 结算那边没有超时的时候返回null
            return New.emptyList();
        }
        String msg = "该课题组有" + statementTimeoutDTO.getOvertimeCount() + "张结算单超过" + statementTimeoutDTO.getOvertimeDay() + "天未完成卡主审批，请卡主审批后进行采购";
        return New.list(new TimeOutTipsVO().setSort(4).setMessage(msg).setType(TimeOutBusinessType.BALANCE_APPROVE.getValue()));
    }

    /**
     * 查超时订单
     * @param params 请求参数
     * @param currentTimeMillis 当前时间
     * @param balanceDaysInt 结算超时时间
     * @param examineDaysInt 验收超时时间
     * @return 超市数据
     */
    private SearchPageResultDTO<OrderMasterSearchDTO> queryTimeOutOrder(TimeOutOrderParamsDTO params, long currentTimeMillis, int balanceDaysInt, int examineDaysInt){
        Request request = this.constructCommonTimeOutQueryParam(params);
        this.fillTimeOutFilter(request, params, currentTimeMillis, balanceDaysInt, examineDaysInt);
        return orderSearchBoostService.search(request);
    }


    /**
     * 按部门查询对应超时订单数量
     *
     * @param params
     * @param currentTimeMillis
     * @param balanceDaysInt
     * @param examineDaysInt
     * @return
     */
    private Map<Integer, Integer> queryTimeOutOrderCountByDepartmentId(TimeOutOrderParamsDTO params, long currentTimeMillis, int balanceDaysInt, int examineDaysInt){
        Request request = this.constructCommonTimeOutQueryParam(params);
        this.fillTimeOutFilter(request, params, currentTimeMillis, balanceDaysInt, examineDaysInt);
        return orderSearchBoostService.aggFieldToCountOrderBySearchRequest(request, "fbuydepartmentid");
    }

    /**
     * 查超时订单，通用部分数据查询参数组装
     * @param params 请求参数
     * @return 超时数据
     */
    private Request constructCommonTimeOutQueryParam(TimeOutOrderParamsDTO params){
        Request request = new Request();
        request.setKey("order");
        int startHit = params.getPageNo() == null ? 0 : (params.getPageNo() - 1) * params.getPageSize();
        request.setStart(startHit);
        int pageSize = (params.getPageSize() == null || params.getPageSize() < 0) ? 20 : params.getPageSize();
        request.setPageSize(pageSize);
        if(params.getBuyerUserId() != null){
            request.addFilter(new TermFilter("fbuyerid", New.list(params.getBuyerUserId())));
        }
        // 单位过滤
        if(params.getUserId() != null){
            request.addFilter(new TermFilter("fuserid", New.list(params.getUserId())));
            if (params.getUserId() == OrgEnum.NAN_FANG_YI_KE.getValue()) {
                // 南方医科，需过滤的经费卡状态
                request.addNotFilter(new TermFilter("fund_status", New.list(OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue())));
            }
        }
        // 部门过滤
        if(CollectionUtils.isNotEmpty(params.getDepartmentIds())){
            request.addFilter(new TermFilter("fbuydepartmentid", params.getDepartmentIds()));
        }
        // 时间范围
        String startDate = params.getStartDate() == null ? null : DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, params.getStartDate());
        String endDate = params.getEndDate() == null ? null : DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, params.getEndDate());
        if(startDate != null && endDate != null){
            request.addFilter(new RangeFilter("forderdate", startDate, endDate, true, true));
        }
        // 线上单
        request.addFilter(new TermFilter("species", ProcessSpeciesEnum.NORMAL.getValue()));
        // 过滤正在退货处理的订单
        request.addNotFilter(new NestedFilter(OrderNestedEnum.NESTED_TABLE_DETAIL.getName(), new TermFilter(OrderNestedEnum.NESTED_TABLE_DETAIL.getName() + ".return_status", notOverTimeReturnStatus)));
        // 排序
        request.addOrderSortItem(new FieldSortItem("forderdate", SortOrder.DESC));

        // 多字段关键字检索
        OrQuery multiFieldOrQuery = new OrQuery();
        String goodInfo = params.getGoodInfo();
        if(StringUtils.isNotBlank(goodInfo)){
            // 商品信息关键字
            IllegalInputControlUtil.checkSearchInput(goodInfo);
            List<String> detailFieldList = New.list(OrderNestedEnum.NESTED_TABLE_DETAIL.getName() + ".fgoodname",
                    OrderNestedEnum.NESTED_TABLE_DETAIL.getName() + ".fgoodcode",
                    OrderNestedEnum.NESTED_TABLE_DETAIL.getName() + ".casno");
            MultiPhraseQuery multiPhraseQuery = new MultiPhraseQuery(detailFieldList, goodInfo);
            NestedQuery nestedQuery = new NestedQuery(OrderNestedEnum.NESTED_TABLE_DETAIL.getName(), multiPhraseQuery);
            multiFieldOrQuery.addQuery(nestedQuery);

        }
        String searchKey = params.getSearchKey();
        if (StringUtils.isNotBlank(searchKey)) {
            // 订单数据关键字
            List<String> fullTextMasterFields = New.list("forderno.keyword", "fbuydepartment", "fbuyername", "fsuppname");
            multiFieldOrQuery.addQuery(new MultiPhraseQuery(fullTextMasterFields , searchKey));
        }
        if(CollectionUtils.isNotEmpty(multiFieldOrQuery.getQueryList())){
            request.addQuery(multiFieldOrQuery);
        }
        return request;
    }

    /**
     * 填充超时相关filter
     * @param request 到es用
     * @param currentTimeMillis 当前秒数
     * @param params 查询参数
     * @param balanceDaysInt 结算超时日期限制
     * @param examineDaysInt 验收超时时间限制
     */
    private void fillTimeOutFilter(Request request, TimeOutOrderParamsDTO params, long currentTimeMillis, int balanceDaysInt, int examineDaysInt){
        if (TimeOutEnums.BALANCE.getType() == params.getOverTimeType()) {
            // 查结算超时的
            request.addFilter(this.constructTimeOutQuery(currentTimeMillis, balanceDaysInt, balanceTimeOutQueryField, balanceTimeOutStatusList));
        } else if (TimeOutEnums.EXAMINE.getType() == params.getOverTimeType()) {
            // 验收超时的
            request.addFilter(this.constructTimeOutQuery(currentTimeMillis, examineDaysInt, examineTimeOutQueryField, examineTimeOutStatusList));
        }else if(TimeOutEnums.ACCEPT_APPROVE_OR_WAREHOUSE.getType() == params.getOverTimeType()) {
            Filter receiveAppprovalFilter = new TermFilter("status", OrderStatusEnum.OrderReceiveApproval.value);

            // 用入库系统就需要入库超时过滤
            AndFilter whTimeOutFilter = new AndFilter()
                    .addFilter(new TermFilter("status", New.list(OrderStatusEnum.WaitingForStatement_1.getValue())))
                    .addFilter(new TermFilter("inventory_status", OrderCommonUtils.NOT_WAREHOUSE_INVENTORY_STATUS_LIST));

            OrFilter orFilter = new OrFilter()
                    .addFilter(receiveAppprovalFilter)
                    .addFilter(whTimeOutFilter);

            // 计算超时时间，进行范围过滤
            Date timeOutLimitDate = new Date(currentTimeMillis - (long) (balanceDaysInt - 1) * oneDayTimeMillis);
            timeOutLimitDate = this.getLegalDate(timeOutLimitDate);
            AndFilter finalFilter = new AndFilter()
                    .addFilter(orFilter)
                    .addFilter(new RangeFilter(balanceTimeOutQueryField, null, DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, timeOutLimitDate), true, false));
            request.addFilter(finalFilter);

        } else if(TimeOutEnums.ALL_TIMEOUT_TYPE.getType() == params.getOverTimeType()){
            // 两种超时都查的
            OrFilter orFilter = new OrFilter()
                    .addFilter(this.constructTimeOutQuery(currentTimeMillis, balanceDaysInt, balanceTimeOutQueryField, balanceTimeOutStatusList))
                    .addFilter(this.constructTimeOutQuery(currentTimeMillis, examineDaysInt, examineTimeOutQueryField, examineTimeOutStatusList));
            request.addFilter(orFilter);
        }
    }


    /**
     * 组装超时查询的Filter
     * @param currentTimeMillis 当前秒数
     * @param timeOutConfigDays 超时配置日期
     * @param timeQueryField 超时查询字段
     * @param timeOutStatusList 超时查询的状态
     * @return Filter
     */
    private AndFilter constructTimeOutQuery(long currentTimeMillis, int timeOutConfigDays, String timeQueryField, List<Integer> timeOutStatusList) {
        AndFilter andFilter = new AndFilter();
        andFilter.addFilter(new TermFilter("status", timeOutStatusList));
        // 计算超时时间，进行范围过滤
        Date timeOutLimitDate = new Date(currentTimeMillis - (long) (timeOutConfigDays - 1) * oneDayTimeMillis);
        timeOutLimitDate = this.getLegalDate(timeOutLimitDate);
        andFilter.addFilter(new RangeFilter(timeQueryField, null, DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, timeOutLimitDate), true, false));
        return andFilter;
    }


    /**
     * 获取合法日期
     *
     * @return
     */
    private Date getLegalDate(Date timeOutLimitDate){
        //防止出现负数的时间戳
        if (timeOutLimitDate.getTime() <= 0) {
            timeOutLimitDate = new Date(0L);
        }
        return timeOutLimitDate;
    }
    /**
     * 获取相差日期，如有余数则按+1天处理
     * @param low 最小日期
     * @param high 最大日期
     * @return 相差日期
     */
    private int getDiffDays(long low, long high) {
        long diffTimeMills = high - low;
        int realDiff = (int) (diffTimeMills / oneDayTimeMillis);
        long mod = diffTimeMills - oneDayTimeMillis * realDiff;
        return mod == 0 ? realDiff : realDiff + 1;
    }

    /**
     * 商品危化品标签处理
     * @param timeOutDetailDTOList 商品
     */
    private void executeDetailDangerousTag(List<OrderDetailTimeOutDTO> timeOutDetailDTOList) {
        // 获取商品id数组
        List<String> detailIdList = timeOutDetailDTOList.stream().map(OrderDetailTimeOutDTO::getId).map(Objects::toString).collect(toList());

        //获取危化品标签
        List<DangerousTagDO> dangerousTagDOList = dangerousTagMapper.selectByBusinessIdInAndBusinessType(detailIdList, DangerousTagEnum.ORDER_DETAIL.getValue());
        // 3.1危化品id和类型字典 businessId -> businessType
        Map<String, Integer> businessTypeDictionary =
                DictionaryUtils.toMap(dangerousTagDOList, DangerousTagDO::getBusinessId, DangerousTagDO::getDangerousType, (oldType, newType) -> oldType);

        // 3.1危化品标签查询字典 businessType -> dangerTag
        Map<Integer, String> dangerTagDictionary = DictionaryUtils.enumToMap(DangerousEnum.class, DangerousEnum::getCode, DangerousEnum::getName);
        // 商品贴上危化品标签
        timeOutDetailDTOList.forEach(d->{
            String dangerTag = dangerTagDictionary.get(businessTypeDictionary.get(d.getId().toString()));
            d.setDangerousTag(dangerTag);
        });
    }

    /**
     * 设置订单超时类型
     * @param balanceDaysInt
     * @param examineDaysInt
     * @param order
     */
    private void executeTimeOutType(int balanceDaysInt, int examineDaysInt, OrderMasterTimeOutDTO order) {
        if (order.getStatus().equals(OrderStatusEnum.WaitingForReceive.getValue())) {
            if (order.getWaitingExamineDays() >= examineDaysInt) {
                order.setTimeOutType(TimeOutEnums.EXAMINE.getType());
                order.setWaitingBalanceDays(0);
            }
        } else {
            if (order.getWaitingBalanceDays() >= balanceDaysInt) {
                order.setTimeOutType(TimeOutEnums.BALANCE.getType());
            }

            if (order.getWaitingBalanceDays() >= balanceDaysInt && order.getWaitingExamineDays() >= examineDaysInt) {
                order.setTimeOutType(TimeOutEnums.ALL_TIMEOUT_TYPE.getType());
            }
        }
    }

    /**
     * 超时订单数量按部门分组
     *
     * @param orgId
     * @param userId
     * @param departmentIdList
     * @param balanceLimitDays
     * @param acceptLimitDay
     * @param timeOutEnums
     * @return
     */
    private Map<Integer, Integer> getOverTimeCountGroupByDepartmentId(Integer orgId, Integer userId, List<Integer> departmentIdList, int balanceLimitDays, int acceptLimitDay, TimeOutEnums timeOutEnums){
        TimeOutOrderParamsDTO paramsDTO = new TimeOutOrderParamsDTO();
        paramsDTO.setUserId(orgId);
        paramsDTO.setBuyerUserId(userId);
        paramsDTO.setDepartmentIds(departmentIdList);
        paramsDTO.setPageSize(0);
        paramsDTO.setOverTimeType(timeOutEnums.getType());
        return this.queryTimeOutOrderCountByDepartmentId(paramsDTO, balanceLimitDays, acceptLimitDay);
    }

}
