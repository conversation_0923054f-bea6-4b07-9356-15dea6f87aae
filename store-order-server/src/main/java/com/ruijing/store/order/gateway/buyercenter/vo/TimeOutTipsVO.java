package com.ruijing.store.order.gateway.buyercenter.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: li<PERSON>yu
 * @createTime: 2023-11-30 16:11
 * @description:
 **/
public class TimeOutTipsVO implements Serializable {

    private static final long serialVersionUID = 3723523059088651477L;

    @RpcModelProperty(value = "超时类型", enumLink = "com.ruijing.store.order.api.base.enums.TimeOutBusinessType")
    private Integer type;

    @RpcModelProperty("提示信息")
    private String message;

    /**
     * 排序用
     */
    private Integer sort;

    public Integer getType() {
        return type;
    }

    public TimeOutTipsVO setType(Integer type) {
        this.type = type;
        return this;
    }

    public String getMessage() {
        return message;
    }

    public TimeOutTipsVO setMessage(String message) {
        this.message = message;
        return this;
    }

    public Integer getSort() {
        return sort;
    }

    public TimeOutTipsVO setSort(Integer sort) {
        this.sort = sort;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", TimeOutTipsVO.class.getSimpleName() + "[", "]")
                .add("type=" + type)
                .add("message='" + message + "'")
                .add("sort=" + sort)
                .toString();
    }
}
