package com.ruijing.store.order.rpc.util;

import com.reagent.order.base.order.enums.product.OrderProductTransactionStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;

import java.util.Objects;

/**
 * @author: <PERSON>wenyu
 * @create: 2024-04-23 14:43
 * @description:
 */
public class OrderUniqueBarcodeUtils {

    /**
     * 根据订单状态获取交易状态（没有考虑退货状态）
     * @param orderStatus 订单状态
     * @return 交易状态
     */
    public static OrderProductTransactionStatusEnum orderStatus2ProductTransactionStatus(Integer orderStatus){
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.get(orderStatus);
        switch (Objects.requireNonNull(orderStatusEnum)){
            case DeckingFail:
            case WaitingForDockingConfirm:
            case WaitingForDelivery:
            case WaitingForConfirm:
            case PurchaseApplyToCancel:
            case SupplierApplyToCancel:
                return OrderProductTransactionStatusEnum.WAITING_FOR_DELIVERY;
            case WaitingForReceive:
                return OrderProductTransactionStatusEnum.WAITING_FOR_RECEIVE;
            case ORDER_SPLIT_UP:
            case Close:
                return OrderProductTransactionStatusEnum.CANCELED;
            default:
                return OrderProductTransactionStatusEnum.RECEIVED;
        }
    }
}
