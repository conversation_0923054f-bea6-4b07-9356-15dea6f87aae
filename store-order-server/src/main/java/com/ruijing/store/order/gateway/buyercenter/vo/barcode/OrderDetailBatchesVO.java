package com.ruijing.store.order.gateway.buyercenter.vo.barcode;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.util.List;
import java.util.StringJoiner;

/**
 * @author: <PERSON><PERSON>yu
 * @create: 2024-05-10 10:00
 * @description:
 */
public class OrderDetailBatchesVO {

    @RpcModelProperty("订单明细id")
    private Integer detailId;

    @RpcModelProperty("商品名")
    private String productName;

    @RpcModelProperty("货号")
    private String productCode;

    @RpcModelProperty("规格")
    private String spec;

    @RpcModelProperty("品牌")
    private String brand;

    @RpcModelProperty("cas号")
    private String casNo;

    @RpcModelProperty("二级分类id")
    private Integer secondCategoryId;

    @RpcModelProperty("二级分类名")
    private String secondCategoryName;

    @RpcModelProperty("批次条形码")
    private List<BatchesBarCodeVO> batches;

    public Integer getDetailId() {
        return detailId;
    }

    public OrderDetailBatchesVO setDetailId(Integer detailId) {
        this.detailId = detailId;
        return this;
    }

    public String getProductName() {
        return productName;
    }

    public OrderDetailBatchesVO setProductName(String productName) {
        this.productName = productName;
        return this;
    }

    public String getProductCode() {
        return productCode;
    }

    public OrderDetailBatchesVO setProductCode(String productCode) {
        this.productCode = productCode;
        return this;
    }

    public String getSpec() {
        return spec;
    }

    public OrderDetailBatchesVO setSpec(String spec) {
        this.spec = spec;
        return this;
    }

    public String getBrand() {
        return brand;
    }

    public OrderDetailBatchesVO setBrand(String brand) {
        this.brand = brand;
        return this;
    }

    public String getCasNo() {
        return casNo;
    }

    public OrderDetailBatchesVO setCasNo(String casNo) {
        this.casNo = casNo;
        return this;
    }

    public Integer getSecondCategoryId() {
        return secondCategoryId;
    }

    public OrderDetailBatchesVO setSecondCategoryId(Integer secondCategoryId) {
        this.secondCategoryId = secondCategoryId;
        return this;
    }

    public String getSecondCategoryName() {
        return secondCategoryName;
    }

    public OrderDetailBatchesVO setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
        return this;
    }

    public List<BatchesBarCodeVO> getBatches() {
        return batches;
    }

    public OrderDetailBatchesVO setBatches(List<BatchesBarCodeVO> batches) {
        this.batches = batches;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderDetailBatchesVO.class.getSimpleName() + "[", "]")
                .add("detailId=" + detailId)
                .add("productName='" + productName + "'")
                .add("productCode='" + productCode + "'")
                .add("spec='" + spec + "'")
                .add("brand='" + brand + "'")
                .add("casNo='" + casNo + "'")
                .add("secondCategoryId=" + secondCategoryId)
                .add("secondCategoryName='" + secondCategoryName + "'")
                .add("batches=" + batches)
                .toString();
    }
}
