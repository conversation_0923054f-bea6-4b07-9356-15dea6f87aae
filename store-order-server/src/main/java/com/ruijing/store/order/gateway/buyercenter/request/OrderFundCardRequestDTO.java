package com.ruijing.store.order.gateway.buyercenter.request;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.StringJoiner;

/**
 * @description: 经费
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/22 16:30
 **/
public class OrderFundCardRequestDTO implements Serializable {

    private static final long serialVersionUID = -1223401941569305214L;

    /**
     * 经费卡id
     */
    @RpcModelProperty("经费卡id，有二级经费卡必填")
    private String cardId;

    /**
     * 经费卡号
     */
    @RpcModelProperty("经费卡号，有二级经费卡必填")
    private String cardNo;

    /**
     * 经费卡使用金额，只有眼科在使用
     */
    @RpcModelProperty("经费卡使用金额，非对接卡且是经费卡层级是二级才会用到")
    private BigDecimal useAmount;

    /**
     * 保存的科目信息
     */
    @RpcModelProperty("保存的科目信息")
    private List<FundCardSubjectRequestDTO> saveFundCardSubjectList;

    @RpcModelProperty("经费支出申请单号")
    private String expenseApplyNo;

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public BigDecimal getUseAmount() {
        return useAmount;
    }

    public void setUseAmount(BigDecimal useAmount) {
        this.useAmount = useAmount;
    }

    public List<FundCardSubjectRequestDTO> getSaveFundCardSubjectList() {
        return saveFundCardSubjectList;
    }

    public void setSaveFundCardSubjectList(List<FundCardSubjectRequestDTO> saveFundCardSubjectList) {
        this.saveFundCardSubjectList = saveFundCardSubjectList;
    }

    public String getExpenseApplyNo() {
        return expenseApplyNo;
    }

    public void setExpenseApplyNo(String expenseApplyNo) {
        this.expenseApplyNo = expenseApplyNo;
    }

    @Override
    public String toString() {
        return "OrderFundCardRequestDTO{" +
                "cardId='" + cardId + '\'' +
                ", cardNo='" + cardNo + '\'' +
                ", useAmount=" + useAmount +
                ", saveFundCardSubjectList=" + saveFundCardSubjectList +
                ", expenseApplyNo='" + expenseApplyNo + '\'' +
                '}';
    }
}
