package com.ruijing.store.order.base.orderlog.model;

import java.util.Date;

public class OrderLogDO {
    /**
    * id
    */
    private Long id;

    /**
    * 订单id
    */
    private Long orderId;

    /**
    * detailId,若是detal操作，需保存detailId，默认0
    */
    private Long orderDetailId;

    /**
    * 订单操作，默认0
    */
    private Integer operation;

    /**
    * 操作人id
    */
    private Long userId;

    /**
    * 用户类型，采购0/供应商1
    */
    private Integer userType;

    /**
    * 用户名
    */
    private String userName;

    /**
    * 备注信息，如评价
    */
    private String note;

    /**
    * 从表信息
    */
    private Integer extra;

    /**
    * 创建时间
    */
    private Date creationTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Long orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Integer getExtra() {
        return extra;
    }

    public void setExtra(Integer extra) {
        this.extra = extra;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderId=").append(orderId);
        sb.append(", orderDetailId=").append(orderDetailId);
        sb.append(", operation=").append(operation);
        sb.append(", userId=").append(userId);
        sb.append(", userType=").append(userType);
        sb.append(", userName=").append(userName);
        sb.append(", note=").append(note);
        sb.append(", extra=").append(extra);
        sb.append(", creationTime=").append(creationTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}