package com.ruijing.store.order.business.service.orderexport;

import com.ruijing.fundamental.cat.common.collections.New;
import com.ruijing.store.order.api.base.delivery.enums.DeliveryProxySourceTypeEnum;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.store.order.api.base.enums.DeliveryStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.base.excel.dto.OrderPrintDetailDTO;
import com.ruijing.store.order.base.excel.dto.ProductPrintDetailDTO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderMasterVO;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2022/5/23 18:04
 */
public interface ExportHeaderAndBody {

    /**
     * hms 商品 表头
     * @return
     */
    default List<List<String>> exportProductHeaderHMS() {
        return New.list(ExportHeaderName.orderNo,
                ExportHeaderName.purchaseGroup,
                ExportHeaderName.buyer,
                ExportHeaderName.contactMan,
                ExportHeaderName.supplier,
                ExportHeaderName.toSupplierNote,
                ExportHeaderName.orderDate,
                ExportHeaderName.orderAmount,
                ExportHeaderName.successFulReturnAmount,
                ExportHeaderName.status,
                ExportHeaderName.goodsName,
                ExportHeaderName.goodsBrand,
                ExportHeaderName.goodsQuantity,
                ExportHeaderName.goodsCode,
                ExportHeaderName.goodsPrice,
                ExportHeaderName.amount,
                ExportHeaderName.successFulReturnQuantity,
                ExportHeaderName.successFulReturnProductAmount,
                ExportHeaderName.soldUnit,
                ExportHeaderName.spec,
                ExportHeaderName.goodsType,
                ExportHeaderName.firstCategory,
                ExportHeaderName.secondCategory,
                ExportHeaderName.thirdCategory);
    }

    /**
     * www 商品 头
     * @return
     */
    default List<List<String>> exportProductHeaderWWW() {
        return New.list(ExportHeaderName.orderNo,
                ExportHeaderName.purchaseGroup,
                ExportHeaderName.buyer,
                ExportHeaderName.contactMan,
                ExportHeaderName.supplier,
                ExportHeaderName.toSupplierNote,
                ExportHeaderName.orderDate,
                ExportHeaderName.orderAmount,
                ExportHeaderName.successFulReturnAmount,
                ExportHeaderName.status,
                ExportHeaderName.goodsName,
                ExportHeaderName.goodsBrand,
                ExportHeaderName.goodsQuantity,
                ExportHeaderName.goodsCode,
                ExportHeaderName.goodsPrice,
                ExportHeaderName.amount,
                ExportHeaderName.successFulReturnQuantity,
                ExportHeaderName.successFulReturnProductAmount,
                ExportHeaderName.soldUnit,
                ExportHeaderName.spec,
                ExportHeaderName.goodsType,
                ExportHeaderName.firstCategory,
                ExportHeaderName.secondCategory,
                ExportHeaderName.thirdCategory);
    }

    /**
     * hms 订单 头
     * @return
     */
    default List<List<String>> exportOrderHeaderHMS() {
        return New.list(ExportHeaderName.orderNo,
                ExportHeaderName.purchaseGroup,
                ExportHeaderName.buyer,
                ExportHeaderName.contactMan,
                ExportHeaderName.receivePhone,
                ExportHeaderName.receiveAddr,
                ExportHeaderName.supplier,
                ExportHeaderName.toSupplierNote,
                ExportHeaderName.orderDate,
                ExportHeaderName.orderAmount,
                ExportHeaderName.successFulReturnAmount,
                ExportHeaderName.status,
                ExportHeaderName.firstCardCode,
                ExportHeaderName.firstCardName,
                ExportHeaderName.secondCardCode,
                ExportHeaderName.secondCardName,
                ExportHeaderName.thirdCardCode,
                ExportHeaderName.thirdCardName);
    }

    /**
     * www 订单 头
     * @return
     */
    default List<List<String>> exportOrderHeaderWWW() {
        return New.list(
                ExportHeaderName.orderNo,
                ExportHeaderName.purchaseGroup,
                ExportHeaderName.buyer,
                ExportHeaderName.contactMan,
                ExportHeaderName.receivePhone,
                ExportHeaderName.receiveAddr,
                ExportHeaderName.supplier,
                ExportHeaderName.toSupplierNote,
                ExportHeaderName.orderDate,
                ExportHeaderName.orderAmount,
                ExportHeaderName.successFulReturnAmount,
                ExportHeaderName.status,
                ExportHeaderName.firstCardCode,
                ExportHeaderName.firstCardName,
                ExportHeaderName.secondCardCode,
                ExportHeaderName.secondCardName,
                ExportHeaderName.thirdCardCode,
                ExportHeaderName.thirdCardName);
    }

    /**
     * www 商品 数据
     * @param productPrintDetailDTOList
     * @return
     */
    default List<List<Object>> exportProductDataWWW(List<ProductPrintDetailDTO> productPrintDetailDTOList) {
        int size = productPrintDetailDTOList.size();
        List<List<Object>> resultList = new ArrayList<>(size);
        for (ProductPrintDetailDTO productPrint : productPrintDetailDTOList) {
            List<Object> curLine = new ArrayList<>();
            curLine.add(productPrint.getOrderNo());
            curLine.add(productPrint.getDepartmentName());
            curLine.add(productPrint.getPurchaseName());
            curLine.add(productPrint.getReceiver());
            curLine.add(productPrint.getSuppName());
            curLine.add(productPrint.getRemark());
            curLine.add(productPrint.getOrderDate());
            curLine.add(productPrint.getOrderPrice());
            curLine.add(productPrint.getSuccessfulReturnOrderAmount());
            curLine.add(productPrint.getStatusName());
            curLine.add(productPrint.getProductName());
            curLine.add(productPrint.getBrand());
            curLine.add(productPrint.getCount());
            curLine.add(productPrint.getProductCode());
            curLine.add(productPrint.getUnitPrice());
            curLine.add(productPrint.getTotalPrice());
            curLine.add(productPrint.getSuccessfulReturnQuantity());
            curLine.add(productPrint.getSuccessfulReturnAmount());
            curLine.add(productPrint.getUnit());
            curLine.add(productPrint.getSpecifications());
            curLine.add(productPrint.getTopCategoryName());
            curLine.add(productPrint.getFirstCategoryName());
            curLine.add(productPrint.getSecondCategoryName());
            curLine.add(productPrint.getThirdCategoryName());
            resultList.add(curLine);
        }
        return resultList;
    }

    /**
     * hms 商品 数据
     * @param productPrintDetailDTOList
     * @return
     */
    default List<List<Object>> exportProductDataHMS(List<ProductPrintDetailDTO> productPrintDetailDTOList) {
        int size = productPrintDetailDTOList.size();
        List<List<Object>> resultList = new ArrayList<>(size);
        for (ProductPrintDetailDTO productPrint : productPrintDetailDTOList) {
            List<Object> curLine = new ArrayList<>();
            curLine.add(productPrint.getOrderNo());
            curLine.add(productPrint.getDepartmentName());
            curLine.add(productPrint.getPurchaseName());
            curLine.add(productPrint.getReceiver());
            curLine.add(productPrint.getSuppName());
            curLine.add(productPrint.getRemark());
            curLine.add(productPrint.getOrderDate());
            curLine.add(productPrint.getOrderPrice());
            curLine.add(productPrint.getSuccessfulReturnOrderAmount());
            curLine.add(productPrint.getStatusName());
            curLine.add(productPrint.getProductName());
            curLine.add(productPrint.getBrand());
            curLine.add(productPrint.getCount());
            curLine.add(productPrint.getProductCode());
            curLine.add(productPrint.getUnitPrice());
            curLine.add(productPrint.getTotalPrice());
            curLine.add(productPrint.getSuccessfulReturnQuantity());
            curLine.add(productPrint.getSuccessfulReturnAmount());
            curLine.add(productPrint.getUnit());
            curLine.add(productPrint.getSpecifications());
            curLine.add(productPrint.getTopCategoryName());
            curLine.add(productPrint.getFirstCategoryName());
            curLine.add(productPrint.getSecondCategoryName());
            curLine.add(productPrint.getThirdCategoryName());
            resultList.add(curLine);
        }
        return resultList;
    }

    /**
     * hms 订单 数据
     * @param orderPrintDetailDTOList
     * @return
     */
    default List<List<Object>> exportOrderDataHMS(List<OrderPrintDetailDTO> orderPrintDetailDTOList) {
        // 这种方式构建比easyexcel更加灵活，可依据需求加入单位个性化需求，必须注意顺序
        List<List<Object>> resultList = new ArrayList<List<Object>>(orderPrintDetailDTOList.size());
        for (OrderPrintDetailDTO orderPrint : orderPrintDetailDTOList) {
            List<Object> curLine = new ArrayList<>();
            // 0
            curLine.add(orderPrint.getOrderNo());
            // 1
            curLine.add(orderPrint.getDepartmentName());
            // 2
            curLine.add(orderPrint.getPurchaseName());
            // 3
            curLine.add(orderPrint.getReceiver());
            // 31
            curLine.add(orderPrint.getReceiverPhone());
            // 32
            curLine.add(orderPrint.getReceiveAddress());
            // 4
            curLine.add(orderPrint.getSuppName());
            // 5
            curLine.add(orderPrint.getRemark());
            // 6
            curLine.add(orderPrint.getOrderDate());
            // 7
            curLine.add(orderPrint.getOrderPrice());
            // 71
            curLine.add(orderPrint.getSuccessfulReturnAmount());
            // 8
            curLine.add(orderPrint.getStatusName());
            // 9
            curLine.add(orderPrint.getFirstLevelCardCode());
            // 10
            curLine.add(orderPrint.getFirstLevelCardName());
            // 11
            curLine.add(orderPrint.getSecondLevelCardCode());
            // 12
            curLine.add(orderPrint.getSecondLevelCardName());
            // 13
            curLine.add(orderPrint.getThirdLevelCardCode());
            // 14
            curLine.add(orderPrint.getThirdLevelCardName());

            resultList.add(curLine);
        }
        return resultList;
    }

    /**
     * www 订单 数据
     * @param orderPrintDetailDTOList
     * @return
     */
    default List<List<Object>> exportOrderDataWWW(List<OrderPrintDetailDTO> orderPrintDetailDTOList) {
        // 这种方式构建比easyexcel更加灵活，可依据需求加入单位个性化需求，必须注意顺序
        List<List<Object>> resultList = new ArrayList<List<Object>>(orderPrintDetailDTOList.size());
        for (OrderPrintDetailDTO orderPrint : orderPrintDetailDTOList) {
            List<Object> curLine = new ArrayList<>();
            // 0
            curLine.add(orderPrint.getOrderNo());
            // 1
            curLine.add(orderPrint.getDepartmentName());
            // 2
            curLine.add(orderPrint.getPurchaseName());
            // 3
            curLine.add(orderPrint.getReceiver());
            // 31
            curLine.add(orderPrint.getReceiverPhone());
            // 32
            curLine.add(orderPrint.getReceiveAddress());
            // 4
            curLine.add(orderPrint.getSuppName());
            // 5
            curLine.add(orderPrint.getRemark());
            // 6
            curLine.add(orderPrint.getOrderDate());
            // 7
            curLine.add(orderPrint.getOrderPrice());
            // 71
            curLine.add(orderPrint.getSuccessfulReturnAmount());
            // 8
            curLine.add(orderPrint.getStatusName());
            // 9
            curLine.add(orderPrint.getFirstLevelCardCode());
            // 10
            curLine.add(orderPrint.getFirstLevelCardName());
            // 11
            curLine.add(orderPrint.getSecondLevelCardCode());
            // 12
            curLine.add(orderPrint.getSecondLevelCardName());
            // 13
            curLine.add(orderPrint.getThirdLevelCardCode());
            // 14
            curLine.add(orderPrint.getThirdLevelCardName());

            resultList.add(curLine);
        }
        return resultList;
    }

    /**
     * 导出代配送订单列表 表头
     * @param orderPrintDetailDTOList
     * @return
     */
    default List<List<String>> exportDeliveryProxyHeaderOMS() {
        // 这种方式构建比easyexcel更加灵活，可依据需求加入单位个性化需求，必须注意顺序
        return New.list(ExportHeaderName.orderNo,
                        ExportHeaderName.orgName,
                        ExportHeaderName.purchaseGroup,
                        ExportHeaderName.buyer,
                        ExportHeaderName.contactMan,
                        ExportHeaderName.receivePhone,
                        ExportHeaderName.receiveAddr,
                        ExportHeaderName.supplier,
                        ExportHeaderName.orderDate,
                        ExportHeaderName.sendOutDate,
                        ExportHeaderName.orderAmount,
                        ExportHeaderName.orderStatus,
                        ExportHeaderName.deliveryProxyType,
                        ExportHeaderName.deliveryStatus,
                        ExportHeaderName.sortedUser,
                        ExportHeaderName.deliveryUser,
                        ExportHeaderName.deliveredTime
                );
    }

    /**
     * 导出代配送订单列表 数据体
     * @param orderPrintDetailDTOList
     * @return
     */
    default List<List<Object>> exportDeliveryProxyDataOMS(List<OrderInfoVO> orderPrintDetailDTOList) {
        // 这种方式构建比easyexcel更加灵活，可依据需求加入单位个性化需求，必须注意顺序
        List<List<Object>> resultList = new ArrayList<List<Object>>(orderPrintDetailDTOList.size());
        for (OrderInfoVO orderInfo : orderPrintDetailDTOList) {
            OrderMasterVO orderMaster = orderInfo.getOrder();
            String buyerContactMan = orderMaster.getBuyerContactMan() == null ? "" : orderMaster.getBuyerContactMan();
            String buyerTelephone = orderMaster.getBuyerTelephone() == null ? "" : orderMaster.getBuyerTelephone();
            List<Object> curLine = New.list();
            curLine.add(orderMaster.getOrderNo());
            curLine.add(orderMaster.getOrgName());
            curLine.add(orderMaster.getDepartmentName());
            curLine.add(orderMaster.getBuyerName());
            curLine.add(buyerContactMan);
            curLine.add(buyerTelephone);
            curLine.add(buyerContactMan + " " + buyerTelephone + " " + orderMaster.getDeliveryAddress());
            curLine.add(orderMaster.getSupplierName());
            curLine.add(orderMaster.getOrderDate());
            curLine.add(orderMaster.getDeliveryDate());
            curLine.add(orderMaster.getTotalPrice());
            // 状态名
            OrderStatusEnum orderStatusEnum = OrderStatusEnum.get(orderMaster.getStatus());
            curLine.add(orderStatusEnum == null ? "" : orderStatusEnum.getName());
            DeliveryProxySourceTypeEnum deliveryProxySourceTypeEnum = DeliveryProxySourceTypeEnum.getByValue(orderMaster.getDeliveryProxySourceType());
            curLine.add(deliveryProxySourceTypeEnum != null ? deliveryProxySourceTypeEnum.getDescription() : StringUtils.EMPTY);
            curLine.add(DeliveryStatusEnum.getDescriptionByValue(orderInfo.getDeliveryStatus()));
            curLine.add(orderInfo.getSortedUser());
            curLine.add(orderInfo.getDeliveryUser());
            curLine.add(orderInfo.getDeliveredTime() == null ? "" : DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, orderInfo.getDeliveredTime()) );
            resultList.add(curLine);
        }
        return resultList;
    }
}
