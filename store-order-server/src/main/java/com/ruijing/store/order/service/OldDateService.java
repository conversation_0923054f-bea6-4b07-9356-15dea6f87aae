package com.ruijing.store.order.service;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.baseconfig.api.enums.ConfigCodeEnum;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.OldDateConfigBO;
import com.ruijing.store.order.rpc.client.OrganizationClient;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @author: liwenyu
 * @createTime: 2023-11-27 16:54
 * @description: 旧单判断服务
 **/
@Service
public class OldDateService {

    /**
     * 单位-是否开启政采目录缓存
     */
    private final Cache<Integer, OldDateConfigBO> orgCodeOldDateConfigMap = CacheBuilder.newBuilder().expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    private final OldDateConfigBO EMPTY_CONFIG = new OldDateConfigBO();

    @Resource
    private OrganizationClient organizationClient;

    @Resource
    private SysConfigClient sysConfigClient;

    public OldDateConfigBO getOldDateConfig(Integer orgId){
        OldDateConfigBO oldDateConfigBO = orgCodeOldDateConfigMap.getIfPresent(orgId);
        if(oldDateConfigBO == EMPTY_CONFIG){
            // 已经获取过就是没有，直接返回null
            return null;
        }
        if(oldDateConfigBO != null){
            // 有则直接返回
            return oldDateConfigBO;
        }

        String orgCode = organizationClient.findSimpleOrgDTOById(orgId).getCode();

        oldDateConfigBO = OldDateConfigBO.getInstance(orgCode, null);
        if(oldDateConfigBO != null){
            // 先不查OMS，看看是不是写死的
            orgCodeOldDateConfigMap.put(orgId, oldDateConfigBO);
            return oldDateConfigBO;
        }
        // 不是写死的，获取OMS配置
        List<String> configCodeList = New.list(ConfigCodeEnum.OLD_DATA_PROCESS_TIME.name(),
                ConfigCodeEnum.OLD_DATA_PROCESS_BUSINESS_SCOPE.name(),
                ConfigCodeEnum.OLD_DATA_PROCESS_OPEN.name());

        List<BaseConfigDTO> baseConfigList = sysConfigClient.getValueByOrgCodeAndConfigCode(orgCode, configCodeList);
        oldDateConfigBO = OldDateConfigBO.getInstance(orgCode, baseConfigList);
        if(oldDateConfigBO != null){
            orgCodeOldDateConfigMap.put(orgId, oldDateConfigBO);
            return oldDateConfigBO;
        }
        // 没有的话写入一个标识，表面没有旧单配置
        orgCodeOldDateConfigMap.put(orgId, EMPTY_CONFIG);
        return null;
    }

    public boolean isOldDate(Integer orgId, Date orderDate, Integer fundStatus, Integer orderSpecies){
        if(orderDate == null){
            return false;
        }
        OldDateConfigBO oldDateConfigBO = this.getOldDateConfig(orgId);
        return oldDateConfigBO != null && oldDateConfigBO.getIsOldOrder(orderDate, fundStatus, orderSpecies);
    }
}
