package com.ruijing.store.order.base.core.bo;

import com.ruijing.store.user.api.dto.DepartmentDTO;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/9 15:43
 * @Description
 **/
public class LoginUserInfoBO implements Serializable {

    private Integer orgId;

    private Integer userId;

    private String userGuid;

    private String email;

    private String mobile;

    private Integer rootDepartmentId;

    private List<Integer> deptIdList;

    private String orgCode;

    private List<DepartmentDTO> deptList;

    private String jobNumber;

    private String userName;

    /**
     * 机构名称
     */
    private String orgName;

    public Integer getOrgId() {
        return orgId;
    }

    public LoginUserInfoBO setOrgId(Integer orgId) {
        this.orgId = orgId;
        return this;
    }

    public Integer getUserId() {
        return userId;
    }

    public LoginUserInfoBO setUserId(Integer userId) {
        this.userId = userId;
        return this;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public LoginUserInfoBO setUserGuid(String userGuid) {
        this.userGuid = userGuid;
        return this;
    }

    public String getEmail() {
        return email;
    }

    public LoginUserInfoBO setEmail(String email) {
        this.email = email;
        return this;
    }

    public String getMobile() {
        return mobile;
    }

    public LoginUserInfoBO setMobile(String mobile) {
        this.mobile = mobile;
        return this;
    }

    public Integer getRootDepartmentId() {
        return rootDepartmentId;
    }

    public LoginUserInfoBO setRootDepartmentId(Integer rootDepartmentId) {
        this.rootDepartmentId = rootDepartmentId;
        return this;
    }

    public List<Integer> getDeptIdList() {
        return deptIdList;
    }

    public LoginUserInfoBO setDeptIdList(List<Integer> deptIdList) {
        this.deptIdList = deptIdList;
        return this;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public List<DepartmentDTO> getDeptList() {
        return deptList;
    }

    public void setDeptList(List<DepartmentDTO> deptList) {
        this.deptList = deptList;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public String getUserName() {
        return userName;
    }

    public LoginUserInfoBO setUserName(String userName) {
        this.userName = userName;
        return this;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("LoginUserInfoBO{");
        sb.append("orgId=").append(orgId);
        sb.append(", userId=").append(userId);
        sb.append(", userGuid='").append(userGuid).append('\'');
        sb.append(", email='").append(email).append('\'');
        sb.append(", mobile='").append(mobile).append('\'');
        sb.append(", rootDepartmentId=").append(rootDepartmentId);
        sb.append(", deptIdList=").append(deptIdList);
        sb.append(", orgCode='").append(orgCode).append('\'');
        sb.append(", deptList=").append(deptList);
        sb.append(", jobNumber='").append(jobNumber).append('\'');
        sb.append(", userName='").append(userName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
