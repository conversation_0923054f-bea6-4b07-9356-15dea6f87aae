package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020/12/22 13:02
 * @Description
 **/
@RpcModel
public class OrderRelatedConfigVO implements Serializable {

    private static final long serialVersionUID = 862107986273024629L;

    /**
     * 经费卡是否必填 0：非必填 1：必填
     */
    @RpcModelProperty(value = "经费卡是否必填 0：非必填 1：必填", description = "MUST_HAVE_FUNDCARD")
    private String mustHaveFundCardConfig;

    /**
     * 验收审批配置 0：非验收审批  1：验收审批
     */
    @RpcModelProperty(value = "验收审批配置 0：费验收审批  1：验收审批", description = "ORG_ACCEPTANCE_APPROVAL_CONFIG")
    private String acceptanceApprovalConfig;

    /**
     * 签署采购合同：0 - 无需签署合同，1 - 提醒签署合同
     */
    @RpcModelProperty(value = "签署采购合同：0 - 无需签署合同，1 - 提醒签署合同", description = "REQUIRE_SIGN_PROCUREMENT_CONTRACT")
    private String requireContractConfig;

    /**
     * 订单采购合同金额
     */
    @RpcModelProperty(value = "订单采购合同金额", description = "ORDER_CONTRACT_THRESHOLD")
    private String orderContractThresholdConfig;

    public String getMustHaveFundCardConfig() {
        return mustHaveFundCardConfig;
    }

    public OrderRelatedConfigVO setMustHaveFundCardConfig(String mustHaveFundCardConfig) {
        this.mustHaveFundCardConfig = mustHaveFundCardConfig;
        return this;
    }

    public String getAcceptanceApprovalConfig() {
        return acceptanceApprovalConfig;
    }

    public OrderRelatedConfigVO setAcceptanceApprovalConfig(String acceptanceApprovalConfig) {
        this.acceptanceApprovalConfig = acceptanceApprovalConfig;
        return this;
    }

    public String getRequireContractConfig() {
        return requireContractConfig;
    }

    public OrderRelatedConfigVO setRequireContractConfig(String requireContractConfig) {
        this.requireContractConfig = requireContractConfig;
        return this;
    }

    public String getOrderContractThresholdConfig() {
        return orderContractThresholdConfig;
    }

    public OrderRelatedConfigVO setOrderContractThresholdConfig(String orderContractThresholdConfig) {
        this.orderContractThresholdConfig = orderContractThresholdConfig;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderRelatedConfigVO{");
        sb.append("mustHaveFundCardConfig='").append(mustHaveFundCardConfig).append('\'');
        sb.append(", acceptanceApprovalConfig='").append(acceptanceApprovalConfig).append('\'');
        sb.append(", requireContractConfig='").append(requireContractConfig).append('\'');
        sb.append(", orderContractThresholdConfig='").append(orderContractThresholdConfig).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
