package com.ruijing.store.order.base.core.translator;

import com.reagent.local.deploy.api.order.dto.SyncOrderDetailMessageDTO;
import com.reagent.local.deploy.api.order.dto.SyncOrderMessageDTO;
import com.reagent.order.dto.request.ThirdPartOrderDetailDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailTimeOutDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.other.dto.GenerateOrderDetailDTO;
import com.ruijing.store.order.api.base.other.dto.OrderDetailPrintDTO;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderDetailVO;
import com.ruijing.store.order.util.DictionaryUtils;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: OrderDetail转换类
 * @author: zhuk
 * @create: 2019-07-08 10:42
 **/
public class OrderDetailTranslator {

    /**
     * 订单详情DTO 转 DO
     * @param orderDetailDTO
     * @return
     */
    public static OrderDetailDO dtoToOrderDetailDO(OrderDetailDTO orderDetailDTO){

        OrderDetailDO orderDetailDO = new OrderDetailDO();
        orderDetailDO.setId(orderDetailDTO.getId());
        orderDetailDO.setFmasterid(orderDetailDTO.getFmasterid());
        orderDetailDO.setFbiddate(orderDetailDTO.getFbiddate());
        orderDetailDO.setFdetailno(orderDetailDTO.getFdetailno());
        orderDetailDO.setCategoryid(orderDetailDTO.getCategoryid());
        orderDetailDO.setFclassification(orderDetailDTO.getFclassification());
        orderDetailDO.setFgoodcode(orderDetailDTO.getFgoodcode());
        orderDetailDO.setProductCode(orderDetailDTO.getProductCode());
        orderDetailDO.setFgoodname(orderDetailDTO.getFgoodname());
        orderDetailDO.setFbrand(orderDetailDTO.getFbrand());
        orderDetailDO.setFspec(orderDetailDTO.getFspec());
        orderDetailDO.setFunit(orderDetailDTO.getFunit());
        orderDetailDO.setFquantity(orderDetailDTO.getFquantity());
        orderDetailDO.setFbidprice(orderDetailDTO.getFbidprice());
        orderDetailDO.setFbidamount(orderDetailDTO.getFbidamount());
        orderDetailDO.setFpicpath(orderDetailDTO.getFpicpath());
        orderDetailDO.setFremainquantity(orderDetailDTO.getFremainquantity());
        orderDetailDO.setFbrandid(orderDetailDTO.getFbrandid());
        orderDetailDO.setTsuppmerpassid(orderDetailDTO.getTsuppmerpassid());
        orderDetailDO.setFcancelquantity(orderDetailDTO.getFcancelquantity());
        orderDetailDO.setFcancelamount(orderDetailDTO.getFcancelamount());
        orderDetailDO.setProductSn(orderDetailDTO.getProductSn());
        orderDetailDO.setReturnStatus(orderDetailDTO.getReturnStatus());
        orderDetailDO.setReturnAmount(orderDetailDTO.getReturnAmount());
        orderDetailDO.setOriginalAmount(orderDetailDTO.getOriginalAmount());
        orderDetailDO.setOriginalPrice(orderDetailDTO.getOriginalPrice());
        orderDetailDO.setModifyPrice(orderDetailDTO.getModifyPrice());
        orderDetailDO.setDeliveryTime(orderDetailDTO.getDeliveryTime());
        orderDetailDO.setRemainderPrice(orderDetailDTO.getRemainderPrice());
        orderDetailDO.setNegotiatedPrice(orderDetailDTO.getNegotiatedPrice());
        orderDetailDO.setSysuCategoryId(orderDetailDTO.getSysuCategoryId());
        orderDetailDO.setCategoryDirectoryId(orderDetailDTO.getCategoryDirectoryId());
        orderDetailDO.setUpdateTime(orderDetailDTO.getUpdateTime());
        orderDetailDO.setCarryFee(orderDetailDTO.getCarryFee());

        orderDetailDO.setFirstCategoryId(orderDetailDTO.getFirstCategoryId());
        orderDetailDO.setFirstCategoryName(orderDetailDTO.getFirstCategoryName());
        orderDetailDO.setSecondCategoryId(orderDetailDTO.getSecondCategoryId());
        orderDetailDO.setSecondCategoryName(orderDetailDTO.getSecondCategoryName());
        orderDetailDO.setFeeTypeTag(orderDetailDTO.getFeeTypeTag());
        orderDetailDO.setCategoryTag(orderDetailDTO.getCategoryTag());
        orderDetailDO.setDangerousTypeId(orderDetailDTO.getDangerousTypeId());
        orderDetailDO.setDangerousTypeName(orderDetailDTO.getDangerousTypeName());
        orderDetailDO.setRegulatoryTypeId(orderDetailDTO.getRegulatoryTypeId());
        orderDetailDO.setRegulatoryTypeName(orderDetailDTO.getRegulatoryTypeName());
        orderDetailDO.setCasno(orderDetailDTO.getCasno());
        orderDetailDO.setSuppId(orderDetailDTO.getSuppId());
        orderDetailDO.setSuppName(orderDetailDTO.getSuppName());
        orderDetailDO.setSuppCode(orderDetailDTO.getSuppCode());
        orderDetailDO.setBrandEname(orderDetailDTO.getBrandEname());
        return orderDetailDO;
    }

    /**
     * 订单详情DTO 转 DO
     * @param orderDetailDTO
     * @return
     */
    public static OrderDetailDO generateDtoToOrderDetailDO(GenerateOrderDetailDTO orderDetailDTO){

        OrderDetailDO orderDetailDO = new OrderDetailDO();
        orderDetailDO.setId(orderDetailDTO.getId());
        orderDetailDO.setFmasterid(orderDetailDTO.getFmasterid());
        orderDetailDO.setFbiddate(orderDetailDTO.getFbiddate());
        orderDetailDO.setFdetailno(orderDetailDTO.getFdetailno());
        orderDetailDO.setCategoryid(orderDetailDTO.getCategoryid());
        orderDetailDO.setFclassification(orderDetailDTO.getFclassification());
        orderDetailDO.setFgoodcode(orderDetailDTO.getFgoodcode());
        orderDetailDO.setProductCode(orderDetailDTO.getProductCode());
        orderDetailDO.setFgoodname(orderDetailDTO.getFgoodname());
        orderDetailDO.setFbrand(orderDetailDTO.getFbrand());
        orderDetailDO.setFspec(orderDetailDTO.getFspec());
        orderDetailDO.setFunit(orderDetailDTO.getFunit());
        orderDetailDO.setFquantity(orderDetailDTO.getFquantity());
        orderDetailDO.setFbidprice(orderDetailDTO.getFbidprice());
        orderDetailDO.setFbidamount(orderDetailDTO.getFbidamount());
        orderDetailDO.setFpicpath(orderDetailDTO.getFpicpath());
        orderDetailDO.setFremainquantity(orderDetailDTO.getFremainquantity());
        orderDetailDO.setFbrandid(orderDetailDTO.getFbrandid());
        orderDetailDO.setTsuppmerpassid(orderDetailDTO.getTsuppmerpassid());
        orderDetailDO.setFcancelquantity(orderDetailDTO.getFcancelquantity());
        orderDetailDO.setFcancelamount(orderDetailDTO.getFcancelamount());
        orderDetailDO.setProductSn(orderDetailDTO.getProductSn());
        orderDetailDO.setReturnStatus(orderDetailDTO.getReturnStatus());
        orderDetailDO.setReturnAmount(orderDetailDTO.getReturnAmount());
        orderDetailDO.setOriginalAmount(orderDetailDTO.getOriginalAmount());
        orderDetailDO.setOriginalPrice(orderDetailDTO.getOriginalPrice());
        orderDetailDO.setModifyPrice(orderDetailDTO.getModifyPrice());
        orderDetailDO.setDeliveryTime(orderDetailDTO.getDeliveryTime());
        orderDetailDO.setRemainderPrice(orderDetailDTO.getRemainderPrice());
        orderDetailDO.setNegotiatedPrice(orderDetailDTO.getNegotiatedPrice());
        orderDetailDO.setSysuCategoryId(orderDetailDTO.getSysuCategoryId());
        orderDetailDO.setCategoryDirectoryId(orderDetailDTO.getCategoryDirectoryId());
        orderDetailDO.setUpdateTime(orderDetailDTO.getUpdateTime());
        orderDetailDO.setCarryFee(orderDetailDTO.getCarryFee());

        orderDetailDO.setFirstCategoryId(orderDetailDTO.getFirstCategoryId());
        orderDetailDO.setFirstCategoryName(orderDetailDTO.getFirstCategoryName());
        orderDetailDO.setSecondCategoryId(orderDetailDTO.getSecondCategoryId());
        orderDetailDO.setSecondCategoryName(orderDetailDTO.getSecondCategoryName());
        orderDetailDO.setFeeTypeTag(orderDetailDTO.getFeeTypeTag());
        orderDetailDO.setCategoryTag(orderDetailDTO.getCategoryTag());
        orderDetailDO.setDangerousTypeId(orderDetailDTO.getDangerousTypeId());
        orderDetailDO.setDangerousTypeName(orderDetailDTO.getDangerousTypeName());
        orderDetailDO.setRegulatoryTypeId(orderDetailDTO.getRegulatoryTypeId());
        orderDetailDO.setRegulatoryTypeName(orderDetailDTO.getRegulatoryTypeName());
        orderDetailDO.setCasno(orderDetailDTO.getCasno());
        orderDetailDO.setSuppId(orderDetailDTO.getSuppId());
        orderDetailDO.setSuppName(orderDetailDTO.getSuppName());
        orderDetailDO.setSuppCode(orderDetailDTO.getSuppCode());
        orderDetailDO.setBrandEname(orderDetailDTO.getBrandEname());
        return orderDetailDO;
    }

    public static GenerateOrderDetailDTO orderDetailDTO2GenerateDTO(OrderDetailDTO orderDetailDTO){
        GenerateOrderDetailDTO generateDTO = new GenerateOrderDetailDTO();
        generateDTO.setId(orderDetailDTO.getId());
        generateDTO.setFmasterid(orderDetailDTO.getFmasterid());
        generateDTO.setFbiddate(orderDetailDTO.getFbiddate());
        generateDTO.setFdetailno(orderDetailDTO.getFdetailno());
        generateDTO.setCategoryid(orderDetailDTO.getCategoryid());
        generateDTO.setFclassification(orderDetailDTO.getFclassification());
        generateDTO.setFgoodcode(orderDetailDTO.getFgoodcode());
        generateDTO.setProductCode(orderDetailDTO.getProductCode());
        generateDTO.setFgoodname(orderDetailDTO.getFgoodname());
        generateDTO.setFbrand(orderDetailDTO.getFbrand());
        generateDTO.setFspec(orderDetailDTO.getFspec());
        generateDTO.setFunit(orderDetailDTO.getFunit());
        generateDTO.setFquantity(orderDetailDTO.getFquantity());
        generateDTO.setFbidprice(orderDetailDTO.getFbidprice());
        generateDTO.setFbidamount(orderDetailDTO.getFbidamount());
        generateDTO.setFpicpath(orderDetailDTO.getFpicpath());
        generateDTO.setFremainquantity(orderDetailDTO.getFremainquantity());
        generateDTO.setFbrandid(orderDetailDTO.getFbrandid());
        generateDTO.setTsuppmerpassid(orderDetailDTO.getTsuppmerpassid());
        generateDTO.setFcancelquantity(orderDetailDTO.getFcancelquantity());
        generateDTO.setFcancelamount(orderDetailDTO.getFcancelamount());
        generateDTO.setProductSn(orderDetailDTO.getProductSn());
        generateDTO.setReturnStatus(orderDetailDTO.getReturnStatus());
        generateDTO.setReturnAmount(orderDetailDTO.getReturnAmount());
        generateDTO.setOriginalAmount(orderDetailDTO.getOriginalAmount());
        generateDTO.setOriginalPrice(orderDetailDTO.getOriginalPrice());
        generateDTO.setModifyPrice(orderDetailDTO.getModifyPrice());
        generateDTO.setDeliveryTime(orderDetailDTO.getDeliveryTime());
        generateDTO.setRemainderPrice(orderDetailDTO.getRemainderPrice());
        generateDTO.setNegotiatedPrice(orderDetailDTO.getNegotiatedPrice());
        generateDTO.setSysuCategoryId(orderDetailDTO.getSysuCategoryId());
        generateDTO.setCategoryDirectoryId(orderDetailDTO.getCategoryDirectoryId());
        generateDTO.setUpdateTime(orderDetailDTO.getUpdateTime());
        generateDTO.setCarryFee(orderDetailDTO.getCarryFee());
        generateDTO.setFirstCategoryId(orderDetailDTO.getFirstCategoryId());
        generateDTO.setFirstCategoryName(orderDetailDTO.getFirstCategoryName());
        generateDTO.setSecondCategoryId(orderDetailDTO.getSecondCategoryId());
        generateDTO.setSecondCategoryName(orderDetailDTO.getSecondCategoryName());
        generateDTO.setFeeTypeTag(orderDetailDTO.getFeeTypeTag());
        generateDTO.setCategoryTag(orderDetailDTO.getCategoryTag());
        generateDTO.setDangerousTypeId(orderDetailDTO.getDangerousTypeId());
        generateDTO.setDangerousTypeName(orderDetailDTO.getDangerousTypeName());
        generateDTO.setRegulatoryTypeId(orderDetailDTO.getRegulatoryTypeId());
        generateDTO.setRegulatoryTypeName(orderDetailDTO.getRegulatoryTypeName());
        generateDTO.setCasno(orderDetailDTO.getCasno());
        generateDTO.setSuppId(orderDetailDTO.getSuppId());
        generateDTO.setSuppName(orderDetailDTO.getSuppName());
        generateDTO.setSuppCode(orderDetailDTO.getSuppCode());
        generateDTO.setBrandEname(orderDetailDTO.getBrandEname());
        generateDTO.setOrderDetailExtraDTOList(orderDetailDTO.getOrderDetailExtraDTOList());
        generateDTO.setAttribute(orderDetailDTO.getAttribute());
        return generateDTO;
    }

    /**
     * 订单详情DO 转 DTO
     * @param orderDetailDO
     * @return
     */
    public static OrderDetailDTO orderDetailDOToTorderDetailDTO(OrderDetailDO orderDetailDO){

        OrderDetailDTO orderDetailDTO = new OrderDetailDTO();
        orderDetailDTO.setId(orderDetailDO.getId());
        orderDetailDTO.setFmasterid(orderDetailDO.getFmasterid());
        orderDetailDTO.setFbiddate(orderDetailDO.getFbiddate());
        orderDetailDTO.setCategoryid(orderDetailDO.getCategoryid());
        orderDetailDTO.setFclassification(orderDetailDO.getFclassification());
        orderDetailDTO.setFgoodcode(orderDetailDO.getFgoodcode());
        orderDetailDTO.setProductCode(orderDetailDO.getProductCode());
        orderDetailDTO.setFgoodname(orderDetailDO.getFgoodname());
        orderDetailDTO.setFbrand(orderDetailDO.getFbrand());
        orderDetailDTO.setFspec(orderDetailDO.getFspec());
        orderDetailDTO.setFunit(orderDetailDO.getFunit());
        orderDetailDTO.setFquantity(orderDetailDO.getFquantity());
        orderDetailDTO.setFbidprice(orderDetailDO.getFbidprice());
        orderDetailDTO.setFbidamount(orderDetailDO.getFbidamount());
        orderDetailDTO.setFpicpath(orderDetailDO.getFpicpath());
        orderDetailDTO.setFremainquantity(orderDetailDO.getFremainquantity());
        orderDetailDTO.setFbrandid(orderDetailDO.getFbrandid());
        orderDetailDTO.setTsuppmerpassid(orderDetailDO.getTsuppmerpassid());
        orderDetailDTO.setFcancelquantity(orderDetailDO.getFcancelquantity());
        orderDetailDTO.setFcancelamount(orderDetailDO.getFcancelamount());
        orderDetailDTO.setProductSn(orderDetailDO.getProductSn());
        orderDetailDTO.setReturnStatus(orderDetailDO.getReturnStatus());
        orderDetailDTO.setReturnAmount(orderDetailDO.getReturnAmount());
        orderDetailDTO.setOriginalAmount(orderDetailDO.getOriginalAmount());
        orderDetailDTO.setOriginalPrice(orderDetailDO.getOriginalPrice());
        orderDetailDTO.setModifyPrice(orderDetailDO.getModifyPrice());
        orderDetailDTO.setDeliveryTime(orderDetailDO.getDeliveryTime());
        orderDetailDTO.setRemainderPrice(orderDetailDO.getRemainderPrice());
        orderDetailDTO.setNegotiatedPrice(orderDetailDO.getNegotiatedPrice());
        orderDetailDTO.setSysuCategoryId(orderDetailDO.getSysuCategoryId());
        orderDetailDTO.setCategoryDirectoryId(orderDetailDO.getCategoryDirectoryId());
        orderDetailDTO.setUpdateTime(orderDetailDO.getUpdateTime());
        orderDetailDTO.setCarryFee(orderDetailDO.getCarryFee());

        orderDetailDTO.setFirstCategoryId(orderDetailDO.getFirstCategoryId());
        orderDetailDTO.setFirstCategoryName(orderDetailDO.getFirstCategoryName());
        orderDetailDTO.setSecondCategoryId(orderDetailDO.getSecondCategoryId());
        orderDetailDTO.setSecondCategoryName(orderDetailDO.getSecondCategoryName());
        orderDetailDTO.setFeeTypeTag(orderDetailDO.getFeeTypeTag());
        orderDetailDTO.setCategoryTag(orderDetailDO.getCategoryTag());
        orderDetailDTO.setDangerousTypeId(orderDetailDO.getDangerousTypeId());
        orderDetailDTO.setDangerousTypeName(orderDetailDO.getDangerousTypeName());
        orderDetailDTO.setRegulatoryTypeId(orderDetailDO.getRegulatoryTypeId());
        orderDetailDTO.setRegulatoryTypeName(orderDetailDO.getRegulatoryTypeName());
        orderDetailDTO.setCasno(orderDetailDO.getCasno());
        orderDetailDTO.setSuppId(orderDetailDO.getSuppId());
        orderDetailDTO.setSuppName(orderDetailDO.getSuppName());
        orderDetailDTO.setSuppCode(orderDetailDO.getSuppCode());
        orderDetailDTO.setBrandEname(orderDetailDO.getBrandEname());
        orderDetailDTO.setFdetailno(orderDetailDO.getFdetailno());
        return orderDetailDTO;
    }

    /**
     * 订单详情DO 转 DTO
     * @param orderDetailDO
     * @return
     */
    public static GenerateOrderDetailDTO orderDetailDO2GenerateDTO(OrderDetailDO orderDetailDO){

        GenerateOrderDetailDTO orderDetailDTO = new GenerateOrderDetailDTO();
        orderDetailDTO.setId(orderDetailDO.getId());
        orderDetailDTO.setFmasterid(orderDetailDO.getFmasterid());
        orderDetailDTO.setFbiddate(orderDetailDO.getFbiddate());
        orderDetailDTO.setCategoryid(orderDetailDO.getCategoryid());
        orderDetailDTO.setFclassification(orderDetailDO.getFclassification());
        orderDetailDTO.setFgoodcode(orderDetailDO.getFgoodcode());
        orderDetailDTO.setProductCode(orderDetailDO.getProductCode());
        orderDetailDTO.setFgoodname(orderDetailDO.getFgoodname());
        orderDetailDTO.setFbrand(orderDetailDO.getFbrand());
        orderDetailDTO.setFspec(orderDetailDO.getFspec());
        orderDetailDTO.setFunit(orderDetailDO.getFunit());
        orderDetailDTO.setFquantity(orderDetailDO.getFquantity());
        orderDetailDTO.setFbidprice(orderDetailDO.getFbidprice());
        orderDetailDTO.setFbidamount(orderDetailDO.getFbidamount());
        orderDetailDTO.setFpicpath(orderDetailDO.getFpicpath());
        orderDetailDTO.setFremainquantity(orderDetailDO.getFremainquantity());
        orderDetailDTO.setFbrandid(orderDetailDO.getFbrandid());
        orderDetailDTO.setTsuppmerpassid(orderDetailDO.getTsuppmerpassid());
        orderDetailDTO.setFcancelquantity(orderDetailDO.getFcancelquantity());
        orderDetailDTO.setFcancelamount(orderDetailDO.getFcancelamount());
        orderDetailDTO.setProductSn(orderDetailDO.getProductSn());
        orderDetailDTO.setReturnStatus(orderDetailDO.getReturnStatus());
        orderDetailDTO.setReturnAmount(orderDetailDO.getReturnAmount());
        orderDetailDTO.setOriginalAmount(orderDetailDO.getOriginalAmount());
        orderDetailDTO.setOriginalPrice(orderDetailDO.getOriginalPrice());
        orderDetailDTO.setModifyPrice(orderDetailDO.getModifyPrice());
        orderDetailDTO.setDeliveryTime(orderDetailDO.getDeliveryTime());
        orderDetailDTO.setRemainderPrice(orderDetailDO.getRemainderPrice());
        orderDetailDTO.setNegotiatedPrice(orderDetailDO.getNegotiatedPrice());
        orderDetailDTO.setSysuCategoryId(orderDetailDO.getSysuCategoryId());
        orderDetailDTO.setCategoryDirectoryId(orderDetailDO.getCategoryDirectoryId());
        orderDetailDTO.setUpdateTime(orderDetailDO.getUpdateTime());
        orderDetailDTO.setCarryFee(orderDetailDO.getCarryFee());

        orderDetailDTO.setFirstCategoryId(orderDetailDO.getFirstCategoryId());
        orderDetailDTO.setFirstCategoryName(orderDetailDO.getFirstCategoryName());
        orderDetailDTO.setSecondCategoryId(orderDetailDO.getSecondCategoryId());
        orderDetailDTO.setSecondCategoryName(orderDetailDO.getSecondCategoryName());
        orderDetailDTO.setFeeTypeTag(orderDetailDO.getFeeTypeTag());
        orderDetailDTO.setCategoryTag(orderDetailDO.getCategoryTag());
        orderDetailDTO.setDangerousTypeId(orderDetailDO.getDangerousTypeId());
        orderDetailDTO.setDangerousTypeName(orderDetailDO.getDangerousTypeName());
        orderDetailDTO.setRegulatoryTypeId(orderDetailDO.getRegulatoryTypeId());
        orderDetailDTO.setRegulatoryTypeName(orderDetailDO.getRegulatoryTypeName());
        orderDetailDTO.setCasno(orderDetailDO.getCasno());
        orderDetailDTO.setSuppId(orderDetailDO.getSuppId());
        orderDetailDTO.setSuppName(orderDetailDO.getSuppName());
        orderDetailDTO.setSuppCode(orderDetailDO.getSuppCode());
        orderDetailDTO.setBrandEname(orderDetailDO.getBrandEname());
        orderDetailDTO.setFdetailno(orderDetailDO.getFdetailno());
        return orderDetailDTO;
    }

    /**
     * DO类转超时订单详情DTO
     * @param orderDetailDO
     * @return
     */
    public static OrderDetailTimeOutDTO detailDoToDetailTimeOutDto(OrderDetailDO orderDetailDO) {
        if (orderDetailDO == null) {
            return null;
        }
        OrderDetailTimeOutDTO dto = new OrderDetailTimeOutDTO();
        dto.setGoodName(orderDetailDO.getFgoodname());
        dto.setBrand(orderDetailDO.getFbrand());
        dto.setGoodCode(orderDetailDO.getFgoodcode());
        dto.setSpec(orderDetailDO.getFspec());
        dto.setQuantity(orderDetailDO.getFquantity());
        dto.setId(orderDetailDO.getId());
        dto.setPrice(orderDetailDO.getFbidprice());

        return dto;
    }

    /**
     * DO类 批量 转超时订单详情DTO 字典
     * @param orderDetailDOList
     * @return
     */
    public static Map<Integer, List<OrderDetailTimeOutDTO>> detailDoToDetailTimeOutDto(List<OrderDetailDO> orderDetailDOList) {
        Map<Integer, List<OrderDetailDO>> masterToDetailMap = orderDetailDOList.stream().collect(Collectors.groupingBy(OrderDetailDO::getFmasterid));
        Map<Integer, List<OrderDetailTimeOutDTO>> masterToDetailTime = new HashMap<>(masterToDetailMap.size());

        if (CollectionUtils.isNotEmpty(orderDetailDOList)) {
            for (Map.Entry<Integer, List<OrderDetailDO>> doEntry : masterToDetailMap.entrySet()) {
                // 获取订单id对应的DO详情 masterId -> List<OrderDetailDO>
                List<OrderDetailDO> detailDOList = doEntry.getValue();
                // 设置订单id对应DTO详情 masterId -> List<OrderDetailTimeOutDTO>
                masterToDetailTime.put(doEntry.getKey(), detailDOList.stream().map(OrderDetailTranslator::detailDoToDetailTimeOutDto).collect(Collectors.toList()));
            }
        }

        return masterToDetailTime;
    }

    /**
     * 订单详情DO 转 订单商品打印详情
     * @param orderDetailItem
     * @return
     */
    public static OrderDetailPrintDTO doToOrderDetailPrintDTO(OrderDetailDO orderDetailItem) {
        OrderDetailPrintDTO orderDetailPrint = new OrderDetailPrintDTO();
        orderDetailPrint.setSpecifications(orderDetailItem.getFspec());
        orderDetailPrint.setProductName(orderDetailItem.getFgoodname());
        orderDetailPrint.setFirstCategoryId(orderDetailItem.getFirstCategoryId());
        orderDetailPrint.setFirstCategoryName(orderDetailItem.getFirstCategoryName());
        orderDetailPrint.setSecondCategoryName(orderDetailItem.getSecondCategoryName());
        orderDetailPrint.setBrandName(orderDetailItem.getFbrand());
        orderDetailPrint.setProductCode(orderDetailItem.getFgoodcode());
        orderDetailPrint.setCasNo(orderDetailItem.getCasno());
        BigDecimal returnQuantity = orderDetailItem.getFcancelquantity();
        BigDecimal returnAmount = orderDetailItem.getFcancelquantity().multiply(orderDetailItem.getFbidprice());
        BigDecimal curQuantity = orderDetailItem.getFquantity().subtract(returnQuantity);
        orderDetailPrint.setQuantity(curQuantity);
        orderDetailPrint.setPrice(orderDetailItem.getFbidprice());
        orderDetailPrint.setUnit(orderDetailItem.getFunit());
        orderDetailPrint.setTotalAmount(orderDetailItem.getFbidamount().subtract(returnAmount));
        orderDetailPrint.setRemainderPrice(orderDetailItem.getRemainderPrice());
        orderDetailPrint.setProductId(orderDetailItem.getProductSn());
        orderDetailPrint.setDetailId(orderDetailItem.getId());
        // 需要设置下分类标签，待顶级分类冗余字段推进上线后可以删掉这个逻辑
        orderDetailPrint.setCategoryName(orderDetailItem.getCategoryTag());
        orderDetailPrint.setFeeType(orderDetailItem.getFeeTypeTag());
        return orderDetailPrint;
    }

    /**
     * 订单详情DO 转 本地部署订单商品打印详情
     * @param item
     * @param orderDetailDOList
     */
    public static SyncOrderMessageDTO doToSyncOrderDetailMessageDTO(SyncOrderMessageDTO item,
                                                                    List<OrderDetailDO> orderDetailDOList,
                                                                    Map<String, Integer> detailIdDangerousTypeMap) {
        if (CollectionUtils.isEmpty(orderDetailDOList)) {
            return null;
        }

        List<SyncOrderDetailMessageDTO> syncOrderDetailMessageDTOList = orderDetailDOList.stream().map(detail -> doToSyncOrderDetailMessageDTO(detail, detailIdDangerousTypeMap.get(detail.getId().toString()))).collect(Collectors.toList());
        item.setOrderDetailList(syncOrderDetailMessageDTOList);
        return item;
    }

    public static SyncOrderDetailMessageDTO doToSyncOrderDetailMessageDTO(OrderDetailDO orderDetailDO, Integer dangerousType) {
        SyncOrderDetailMessageDTO item = new SyncOrderDetailMessageDTO();
        item.setId(orderDetailDO.getId());
        item.setMasterId(orderDetailDO.getFmasterid());
        item.setProductName(orderDetailDO.getFgoodname());
        // 设置正确的危化品标签
        item.setDangerousType(dangerousType);
        item.setBrand(orderDetailDO.getFbrand());
        item.setProductCode(orderDetailDO.getFgoodcode());
        item.setSpec(orderDetailDO.getFspec());
        item.setPrice(orderDetailDO.getFbidprice());
        item.setOriginalPrice(orderDetailDO.getOriginalPrice());
        item.setCoupon(orderDetailDO.getRemainderPrice());
        item.setQuantity(orderDetailDO.getFquantity());
        item.setUnit(orderDetailDO.getFunit());
        item.setTotalAmount(orderDetailDO.getFbidamount());

        return item;
    }

    /**
     * 订单详情转换对接管理平台详情DTO
     * @param detail
     * @return
     */
    public static ThirdPartOrderDetailDTO doToThirdPartOrderDetailDTO(OrderDetailDO detail) {
        ThirdPartOrderDetailDTO detailItem = new ThirdPartOrderDetailDTO();
        detailItem.setId(detail.getId());
        detailItem.setMasterId(detail.getFmasterid());
        detailItem.setCategoryId(detail.getCategoryid());
        detailItem.setClassification(detail.getFclassification());
        detailItem.setGoodsCode(detail.getFgoodcode());
        detailItem.setGoodsName(detail.getFgoodname());
        detailItem.setBrand(detail.getFbrand());
        detailItem.setSpec(detail.getFspec());
        detailItem.setUnit(detail.getFunit());
        detailItem.setQuantity(detail.getFquantity());
        detailItem.setPrice(detail.getFbidprice());
        detailItem.setTotalPrice(detail.getFbidamount());
        detailItem.setPicPath(detail.getFpicpath());
        detailItem.setReturnQuantity(detail.getFcancelquantity());
        detailItem.setProductId(detail.getProductSn());
        detailItem.setReturnStatus(detail.getReturnStatus());
        detailItem.setReturnTotalPrice(detail.getReturnAmount());
        detailItem.setOriginalTotalPrice(detail.getOriginalAmount());
        detailItem.setOriginalPrice(detail.getOriginalPrice());
        detailItem.setDeliveryTime(detail.getDeliveryTime());
        detailItem.setCarryFee(detail.getCarryFee());
        detailItem.setFirstCategoryId(detail.getFirstCategoryId());
        detailItem.setFirstCategoryName(detail.getFirstCategoryName());
        detailItem.setSecondCategoryId(detail.getSecondCategoryId());
        detailItem.setSecondCategoryName(detail.getSecondCategoryName());
        detailItem.setCategoryTag(detail.getCategoryTag());
        detailItem.setDangerousTypeId(detail.getDangerousTypeId());
        detailItem.setDangerousTypeName(detail.getDangerousTypeName());
        detailItem.setRegulatoryTypeId(detail.getRegulatoryTypeId());
        detailItem.setRegulatoryTypeName(detail.getRegulatoryTypeName());
        detailItem.setCasNo(detail.getCasno());
        detailItem.setSuppId(detail.getSuppId());
        detailItem.setSuppName(detail.getSuppName());
        detailItem.setSuppCode(detail.getSuppCode());
        detailItem.setBrandEName(detail.getBrandEname());

        return detailItem;
    }

    public static List<com.reagent.research.fundcard.dto.OrderDetailDTO> orderDetail2FundCardOrderDetail(List<OrderDetailDO> detailList){
        return OrderDetailTranslator.orderDetail2FundCardOrderDetail(detailList, false);
    }

    /**
     * 订单商品转为经费卡详情，根据ignoreReturn判断是否要减去退货的
     * @param detailList 订单商品
     * @param ignoreReturn 是否忽略退货，是的话直接取订单原数量、总价，否则会减去
     * @return 经费卡商品详情
     */
    public static List<com.reagent.research.fundcard.dto.OrderDetailDTO> orderDetail2FundCardOrderDetail(List<OrderDetailDO> detailList, boolean ignoreReturn){
        List<com.reagent.research.fundcard.dto.OrderDetailDTO> fundCardDetails = new ArrayList<>();
        for (OrderDetailDO detail : detailList) {
            com.reagent.research.fundcard.dto.OrderDetailDTO fundCardDetail = new com.reagent.research.fundcard.dto.OrderDetailDTO();
            fundCardDetail.setId(detail.getId().toString());
            fundCardDetail.setGoodsId(detail.getProductSn().toString());
            fundCardDetail.setGoodsName(detail.getFgoodname());
            fundCardDetail.setPrice(detail.getFbidprice());
            if(ignoreReturn){
                fundCardDetail.setQuantity(detail.getFquantity());
                fundCardDetail.setTotalPrice(detail.getFbidamount());
            }else {
                fundCardDetail.setQuantity(detail.getFquantity().subtract(detail.getFcancelquantity()));
                fundCardDetail.setTotalPrice(detail.getFbidamount().subtract(BigDecimal.valueOf(detail.getReturnAmount())));
            }
            fundCardDetail.setFirstGoodsCategoryId(detail.getFirstCategoryId() == null ? null : detail.getFirstCategoryId().toString());
            fundCardDetail.setFirstGoodsCategoryName(detail.getFirstCategoryName());
            fundCardDetail.setSecondGoodsCategoryId(detail.getSecondCategoryId() == null ? null : detail.getSecondCategoryId().toString());
            fundCardDetail.setSecondGoodsCategoryName(detail.getSecondCategoryName());
            // categoryId为最末级分类，如果它不等于一级字段也不等于二级字段，则为三级分类
            boolean haveThirdCategory = detail.getCategoryid() != null
                    && !detail.getCategoryid().equals(detail.getFirstCategoryId())
                    && !detail.getCategoryid().equals(detail.getSecondCategoryId());
            Integer thirdCategoryId = haveThirdCategory ? detail.getCategoryid() : null;
            fundCardDetail.setThirdGoodsCategoryId(thirdCategoryId == null ? null : thirdCategoryId.toString());
            String fclassification = detail.getFclassification();
            String thirdCategoryName = Objects.equals(detail.getSecondCategoryName(), fclassification) || Objects.equals(detail.getFirstCategoryName(), fclassification)? null : fclassification;
            if(Objects.nonNull(thirdCategoryName)){
                fundCardDetail.setThirdGoodsCategoryName(thirdCategoryName);
            }
            fundCardDetail.setSpecification(detail.getFspec());
            fundCardDetail.setExtraDTOs(New.list());
            fundCardDetails.add(fundCardDetail);
        }
        return fundCardDetails;
    }

    /**
     * 将 OrderDetailSearchDTO 转换为 OrderDetailDO
     *
     * @param searchDTO 订单详情搜索DTO
     * @return 订单详情DO
     */
    public static OrderDetailDO toOrderDetailDO(OrderDetailSearchDTO searchDTO) {
        if (Objects.isNull(searchDTO)) {
            return null;
        }

        OrderDetailDO detailDO = new OrderDetailDO();
        detailDO.setId(searchDTO.getDetailId());
        detailDO.setFgoodcode(searchDTO.getFgoodcode());
        detailDO.setFgoodname(searchDTO.getFgoodname());
        detailDO.setProductSn(searchDTO.getProductId());
        detailDO.setFbrandid(searchDTO.getFbrandid());
        detailDO.setFbrand(searchDTO.getFbrand());
        detailDO.setFbidprice(Objects.isNull(searchDTO.getFbidprice()) ? null : BigDecimal.valueOf(searchDTO.getFbidprice()));
        detailDO.setFquantity(Objects.isNull(searchDTO.getFquantity()) ? null : BigDecimal.valueOf(searchDTO.getFquantity()));
        detailDO.setReturnStatus(searchDTO.getReturnStatus());
        detailDO.setOriginalAmount(Objects.isNull(searchDTO.getOriginalAmount()) ? null : BigDecimal.valueOf(searchDTO.getOriginalAmount()));
        detailDO.setFspec(searchDTO.getFspec());
        detailDO.setOriginalPrice(Objects.isNull(searchDTO.getOriginalPrice()) ? null : BigDecimal.valueOf(searchDTO.getOriginalPrice()));
        detailDO.setCategoryid(searchDTO.getCategoryId());
        detailDO.setFclassification(searchDTO.getCategoryName());
        detailDO.setCategoryDirectoryId(searchDTO.getCategoryDirectoryId());
        detailDO.setFbidamount(Objects.isNull(searchDTO.getFbidamount()) ? null : BigDecimal.valueOf(searchDTO.getFbidamount()));
        detailDO.setCasno(searchDTO.getCasNo());
        detailDO.setDangerousTypeId(searchDTO.getDangerousType());
        detailDO.setDangerousTypeName(searchDTO.getDangerousTypeName());
        detailDO.setRegulatoryTypeId(searchDTO.getRegulatoryType());
        detailDO.setRegulatoryTypeName(searchDTO.getRegulatoryTypeName());
        detailDO.setFunit(searchDTO.getFunit());
        detailDO.setFcancelquantity(Objects.isNull(searchDTO.getFcancelquantity()) ? null : BigDecimal.valueOf(searchDTO.getFcancelquantity()));
        detailDO.setReturnAmount(searchDTO.getReturnAmount());
        detailDO.setFirstCategoryId(searchDTO.getFirstCategoryId());
        detailDO.setFirstCategoryName(searchDTO.getFirstCategoryName());
        detailDO.setSecondCategoryId(searchDTO.getSecondCategoryId());
        detailDO.setSecondCategoryName(searchDTO.getSecondCategoryName());
        detailDO.setCategoryTag(searchDTO.getCategoryTag());
        detailDO.setFeeTypeTag(searchDTO.getFeeTypeTag());
        detailDO.setFpicpath(searchDTO.getPicPath());
        detailDO.setModifyPrice(Objects.isNull(searchDTO.getModifyPrice()) ? null : searchDTO.getModifyPrice() == 1);
        detailDO.setSuppId(searchDTO.getSuppId());
        detailDO.setRemainderPrice(searchDTO.getRemainderPrice());
        detailDO.setProductCode(searchDTO.getProductCode());

        return detailDO;
    }

    /**
     * 批量将 OrderDetailSearchDTO 转换为 OrderDetailDO
     *
     * @param searchDTOList 订单详情搜索DTO列表
     * @return 订单详情DO列表
     */
    public static List<OrderDetailDO> toOrderDetailDOList(List<OrderDetailSearchDTO> searchDTOList) {
        if (CollectionUtils.isEmpty(searchDTOList)) {
            return New.emptyList();
        }

        List<OrderDetailDO> detailDOList = New.listWithCapacity(searchDTOList.size());
        for (OrderDetailSearchDTO searchDTO : searchDTOList) {
            OrderDetailDO detailDO = toOrderDetailDO(searchDTO);
            if (Objects.nonNull(detailDO)) {
                detailDOList.add(detailDO);
            }
        }

        return detailDOList;
    }

    /**
     * 将订单详情列表按订单ID分组
     *
     * @param detailDOList 订单详情DO列表
     * @return 按订单ID分组的订单详情DO映射
     */
    public static Map<Integer, List<OrderDetailDO>> groupDetailsByOrderId(List<OrderDetailDO> detailDOList) {
        if (CollectionUtils.isEmpty(detailDOList)) {
            return New.emptyMap();
        }

        return detailDOList.stream().collect(Collectors.groupingBy(OrderDetailDO::getFmasterid));
    }


    /**
     * 填充商品规格信息
     *
     * @param orderDetailVO
     * @param orderDetailDTO
     */
    public static void fillSpecificationDetails(OrderDetailVO orderDetailVO, OrderDetailDTO orderDetailDTO) {
        orderDetailVO.setPackingSpec(orderDetailDTO.getPackingSpec());
        orderDetailVO.setPurity(orderDetailDTO.getPurity());
        orderDetailVO.setMedicalDeviceRegisCertNumber(orderDetailDTO.getMedicalDeviceRegisCertNumber());
        orderDetailVO.setCompletionCycle(orderDetailDTO.getCompletionCycle());
        orderDetailVO.setPress(orderDetailDTO.getPress());
        orderDetailVO.setProductSpec(orderDetailDTO.getProductSpec());
        orderDetailVO.setModelNumber(orderDetailDTO.getModelNumber());
    }
}
