package com.ruijing.store.order.gateway.file.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.order.whitehole.eventbase.enums.file.FileBusinessTypeEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.AttachmentDTO;

import java.io.Serializable;
import java.util.List;

/**
 * @author: chenzhanliang
 * @createTime: 2024-04-16 18:03
 * @description:
 **/
@RpcModel("文件上传-增量上传文件信息")
public class OrderFileUploadRequestDTO implements Serializable {
    private static final long serialVersionUID = -2808317604363597414L;

    @RpcModelProperty("订单id-必传")
    private Integer orderId;

    @RpcModelProperty("上传文件-必传")
    private List<AttachmentDTO> attachmentList;

    @RpcModelProperty(value = "文件业务类型-必传", enumClass = FileBusinessTypeEnum.class)
    private Integer fileBusinessType;

    /**
     * 文件类型 FileBusinessTypeEnum
     */
    @RpcModelProperty("上传验收视频")
    private List<AttachmentDTO> videoAttachmentList;

    public Integer getFileBusinessType() {
        return fileBusinessType;
    }

    public OrderFileUploadRequestDTO setFileBusinessType(Integer fileBusinessType) {
        this.fileBusinessType = fileBusinessType;
        return this;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public OrderFileUploadRequestDTO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public List<AttachmentDTO> getAttachmentList() {
        return attachmentList;
    }

    public OrderFileUploadRequestDTO setAttachmentList(List<AttachmentDTO> attachmentList) {
        this.attachmentList = attachmentList;
        return this;
    }

    public List<AttachmentDTO> getVideoAttachmentList() {
        return videoAttachmentList;
    }

    public OrderFileUploadRequestDTO setVideoAttachmentList(List<AttachmentDTO> videoAttachmentList) {
        this.videoAttachmentList = videoAttachmentList;
        return this;
    }

    @Override
    public String toString() {
        return "OrderFileUploadRequestDTO{" +
                "orderId=" + orderId +
                ", attachmentList=" + attachmentList +
                ", fileBusinessType=" + fileBusinessType +
                ", videoAttachmentList=" + videoAttachmentList +
                '}';
    }
}
