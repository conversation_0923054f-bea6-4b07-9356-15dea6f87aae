package com.ruijing.store.order.gateway.oms.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2022/12/7 10:57
 * @description
 */
@RpcModel("oms数据修正记录")
public class OmsFixDataLogVO implements Serializable {

    private static final long serialVersionUID = 2970595412455895895L;

    @RpcModelProperty("日志id")
    private Integer id;

    @RpcModelProperty("操作时间")
    private Long operationTime;

    @RpcModelProperty("操作人")
    private String operationName;

    @RpcModelProperty("单位名")
    private String orgName;

    @RpcModelProperty("订单号")
    private String number;

    @RpcModelProperty("钉钉审批编号")
    private String dingTalkApprovalNumber;

    @RpcModelProperty("具体内容")
    private String content;

    @RpcModelProperty("之前的状态")
    private String prevStatus;

    @RpcModelProperty("原因")
    private String reason;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Long operationTime) {
        this.operationTime = operationTime;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getDingTalkApprovalNumber() {
        return dingTalkApprovalNumber;
    }

    public void setDingTalkApprovalNumber(String dingTalkApprovalNumber) {
        this.dingTalkApprovalNumber = dingTalkApprovalNumber;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getPrevStatus() {
        return prevStatus;
    }

    public void setPrevStatus(String prevStatus) {
        this.prevStatus = prevStatus;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OmsFixDataLogVO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("operationTime=" + operationTime)
                .add("operationName='" + operationName + "'")
                .add("orgName='" + orgName + "'")
                .add("number='" + number + "'")
                .add("dingTalkApprovalNumber='" + dingTalkApprovalNumber + "'")
                .add("content='" + content + "'")
                .add("prevStatus='" + prevStatus + "'")
                .add("reason='" + reason + "'")
                .toString();
    }
}
