package com.ruijing.store.order.gateway.clinical;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderProductContractReqDTO;
import com.ruijing.store.order.business.service.orgondemand.ClinicalOrderService;
import com.ruijing.store.order.gateway.buyercenter.vo.contract.OrderProductContractVO;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/16 18:06
 * @Description
 **/
@MSharpService(isGateway = "true")
@RpcMapping("/clinicalOrderManage")
@RpcApi(value = "临床-订单")
public class ClinicalOrderGWController {

    @Resource
    private ClinicalOrderService clinicalOrderService;

    @RpcMethod("订单管理-我的订单-获取订单及商品相关合同信息")
    @RpcMapping("/getOrderProductContractList")
    public RemoteResponse<List<OrderProductContractVO>> getOrderProductContractList( OrderProductContractReqDTO req) {
        List<Integer> orderIdList = req.getOrderIdList();
        List<OrderProductContractVO> orderDetailMethods = clinicalOrderService.listOrderProductByOrderIds(orderIdList);
        return RemoteResponse.<List<OrderProductContractVO>>custom().setSuccess().setData(orderDetailMethods);
    }
}
