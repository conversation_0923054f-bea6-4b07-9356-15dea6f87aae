package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.reagent.order.base.log.dto.DataOperationLogDTO;
import com.reagent.order.base.log.request.DataOperationLogListRequest;
import com.reagent.order.base.log.request.DataOperationLogQueryRequest;
import com.reagent.order.base.log.service.DataOperationLogService;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/12/8 17:55
 * @description
 */
@ServiceClient
public class DataOperationLogRpcClient {

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    private DataOperationLogService dataOperationLogService;

    @ServiceLog(description = "插入OMS修正数据日志", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public void insert(DataOperationLogDTO dataOperationLogDTO){
        RemoteResponse<Boolean> response = dataOperationLogService.insertLog(dataOperationLogDTO);
        Preconditions.isTrue(response.isSuccess() && response.getData(), "插入OMS修正数据日志失败");
    }

    @ServiceLog(description = "插入OMS修正数据日志", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public void insertList(List<DataOperationLogDTO> dataOperationLogDTOList){
        RemoteResponse<Boolean> response = dataOperationLogService.batchInsertLog(dataOperationLogDTOList);
        Preconditions.isTrue(response.isSuccess() && response.getData(), "插入OMS修正数据日志失败");
    }

    @ServiceLog(description = "获取OMS修正数据日志", operationType = OperationType.READ, serviceType = ServiceType.RPC_CLIENT)
    public PageableResponse<List<DataOperationLogDTO>> getDataOperationLog(DataOperationLogListRequest request){
        PageableResponse<List<DataOperationLogDTO>> pageableResponse = dataOperationLogService.listByParams(request);
        Preconditions.isTrue(pageableResponse.isSuccess(), "获取OMS日志数据失败");
        return pageableResponse;
    }

    @ServiceLog(description = "批量查询修正日志", operationType = OperationType.READ, serviceType = ServiceType.RPC_CLIENT)
    public List<DataOperationLogDTO> listByOrderNosAndTypes(DataOperationLogQueryRequest request) {
        if (Objects.isNull(request) || CollectionUtils.isEmpty(request.getOrderNos())) {
            return New.emptyList();
        }
        List<String> orderNos = request.getOrderNos();
        // 分批处理，每批200条
        List<DataOperationLogDTO> result = New.list();
        List<List<String>> partitionList = Lists.partition(orderNos, 200);
        for (List<String> partition : partitionList) {
            DataOperationLogQueryRequest batchRequest = new DataOperationLogQueryRequest();
            batchRequest.setOrderNos(New.list(partition));
            batchRequest.setOperationTypes(request.getOperationTypes());
            RemoteResponse<List<DataOperationLogDTO>> response = dataOperationLogService.listByOrderNosAndTypes(batchRequest);
            Preconditions.isTrue(response.isSuccess(), response.getMsg());
            if (CollectionUtils.isNotEmpty(response.getData())) {
                result.addAll(response.getData());
            }
        }

        return result;
    }

}
