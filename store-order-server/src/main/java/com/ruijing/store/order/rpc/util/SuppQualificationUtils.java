package com.ruijing.store.order.rpc.util;

import com.ruijing.shop.crm.api.pojo.dto.QualificationDTO;
import com.ruijing.shop.crm.api.support.enums.EntQualificationStatusEnums;

import java.util.Calendar;
import java.util.Date;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @date 2022/11/29 9:34
 * @description 商家资质工具类
 */
public class SuppQualificationUtils {

    /**
     * 申请注销，审批中
     */
    public static final Predicate<QualificationDTO> CANCEL_AUDITING = qualificationDTO -> EntQualificationStatusEnums.CANCEL_AUDITING.getValue().toString().equals(qualificationDTO.getStatus());

    /**
     * 申请注销，审批不通过
     */
    public static final Predicate<QualificationDTO> CANCEL_REFUSED = qualificationDTO -> EntQualificationStatusEnums.CANCEL_REFUSED.getValue().toString().equals(qualificationDTO.getStatus());

    /**
     * 注销完成
     */
    public static final Predicate<QualificationDTO> CANCELED = qualificationDTO -> EntQualificationStatusEnums.CANCELED.getValue().toString().equals(qualificationDTO.getStatus());

    /**
     * 获取公示期完成的最晚日期（注销完成时间早于该时间的为公示期已完成）
     * @return 获取公示期截止日期
     */
    public static Date getNoticePeriodEndDate(){
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -30);
        return calendar.getTime();
    }

    /**
     * 获取注销通过（处于注销30天公示期）
     */
    public static Predicate<QualificationDTO> getCancelPass(Date cancelCompleteDate){
        return CANCELED.and(qualificationDTO -> qualificationDTO.getApproveTime().after(cancelCompleteDate));
    }

    /**
     * 注销后（30天公示期已满）
     */
    public static Predicate<QualificationDTO> getAfterCancel(Date cancelCompleteDate){
        return CANCELED.and(qualificationDTO -> qualificationDTO.getApproveTime().before(cancelCompleteDate));
    }
}
