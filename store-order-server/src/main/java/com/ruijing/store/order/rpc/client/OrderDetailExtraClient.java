package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.reagent.order.base.order.dto.request.OrderDetailExtraListRequestDTO;
import com.reagent.order.base.order.service.OrderDetailBatchesRPCService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO;
import com.ruijing.store.order.base.core.translator.OrderDetailExtraTranslator;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description OrderDetailExtra客户端
 * @date 2023/7/18 14:41
 */
@ServiceClient
public class OrderDetailExtraClient {

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    private OrderDetailBatchesRPCService orderDetailBatchesRPCService;

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE, description = "插入订单详情额外信息")
    public void batchInsertOrderDetailExtra(List<OrderDetailExtraDTO> orderDetailExtraDTOList) {
        if(CollectionUtils.isEmpty(orderDetailExtraDTOList)){
            return;
        }
        RemoteResponse<Boolean> remoteResponse = orderDetailBatchesRPCService.batchInsertOrderDetailExtra(orderDetailExtraDTOList.stream().map(OrderDetailExtraTranslator::toDTOForGalaxy).collect(Collectors.toList()));
        Preconditions.isTrue(remoteResponse.isSuccess(), "插入订单详情额外信息失败");
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.READ, description = "查询订单详情额外信息")
    public List<OrderDetailExtraDTO> listOrderDetailExtra(List<Integer> orderIdList, List<Integer> orderDetailIdList) {
        if (CollectionUtils.isEmpty(orderIdList) && CollectionUtils.isEmpty(orderDetailIdList)) {
            return New.emptyList();
        }

        List<OrderDetailExtraDTO> resultList = New.list();
        int batchSize = 200;

        // 分批处理订单ID
        if (CollectionUtils.isNotEmpty(orderIdList)) {
            List<List<Integer>> batchOrderIdLists = Lists.partition(orderIdList, batchSize);
            for (List<Integer> batchOrderIds : batchOrderIdLists) {
                OrderDetailExtraListRequestDTO requestDTO = new OrderDetailExtraListRequestDTO();
                requestDTO.setOrderIdList(New.list(batchOrderIds));
                RemoteResponse<List<com.reagent.order.base.order.dto.OrderDetailExtraDTO>> response =
                        orderDetailBatchesRPCService.listOrderDetailExtra(requestDTO);
                Preconditions.isTrue(response.isSuccess(), response.getMsg());
                if (CollectionUtils.isNotEmpty(response.getData())) {
                    resultList.addAll(response.getData().stream()
                            .map(OrderDetailExtraTranslator::fromGalaxyDTO)
                            .collect(Collectors.toList()));
                }
            }
        }

        // 分批处理订单详情ID
        if (CollectionUtils.isNotEmpty(orderDetailIdList)) {
            List<List<Integer>> batchDetailIdLists = Lists.partition(orderDetailIdList, batchSize);
            for (List<Integer> batchDetailIds : batchDetailIdLists) {
                OrderDetailExtraListRequestDTO requestDTO = new OrderDetailExtraListRequestDTO();
                requestDTO.setOrderDetailIdList(New.list(batchDetailIds));
                RemoteResponse<List<com.reagent.order.base.order.dto.OrderDetailExtraDTO>> response =
                        orderDetailBatchesRPCService.listOrderDetailExtra(requestDTO);
                Preconditions.isTrue(response.isSuccess(), response.getMsg());
                if (CollectionUtils.isNotEmpty(response.getData())) {
                    resultList.addAll(response.getData().stream()
                            .map(OrderDetailExtraTranslator::fromGalaxyDTO)
                            .collect(Collectors.toList()));
                }
            }
        }

        return resultList;
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE, description = "删除订单详情额外信息")
    public void deleteOrderDetailExtra(List<Integer> orderIdList, List<Integer> orderDetailIdList) {
        OrderDetailExtraListRequestDTO orderDetailExtraListRequestDTO = new OrderDetailExtraListRequestDTO();
        orderDetailExtraListRequestDTO.setOrderIdList(orderIdList);
        orderDetailExtraListRequestDTO.setOrderDetailIdList(orderDetailIdList);
        RemoteResponse<Boolean> remoteResponse = orderDetailBatchesRPCService.deleteOrderDetailExtra(orderDetailExtraListRequestDTO);
        Preconditions.isTrue(remoteResponse.isSuccess(), "删除订单详情额外信息失败");
    }

}
