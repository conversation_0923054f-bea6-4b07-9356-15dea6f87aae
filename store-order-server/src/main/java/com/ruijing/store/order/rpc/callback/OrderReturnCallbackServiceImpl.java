package com.ruijing.store.order.rpc.callback;

import com.reagent.research.api.dto.CallbackRequest;
import com.reagent.research.financial.docking.dto.order.OrderReturnResult;
import com.reagent.tpi.tpiclient.api.order.v2.OrderReturnCallbackService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.rpc.client.OrderOtherLogClient;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * @description: 第三方对接平台退货回调实现
 * @author: zhong<PERSON><PERSON><PERSON>
 * @create: 2021/3/2 16:51
 **/
@MSharpService
public class OrderReturnCallbackServiceImpl implements OrderReturnCallbackService {

    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    @Resource
    private OrderOtherLogClient orderOtherLogClient;

    @Resource
    private DockingExtraService dockingExtraService;

    @Override
    public RemoteResponse<Boolean> handleOrderReturnResult(CallbackRequest<OrderReturnResult> request) {
        LOGGER.info("订单退货回调：" + JsonUtils.toJsonIgnoreNull(request));
        OrderReturnResult requestData = request.getData();
        String orgCode = request.getOrgCode();
        String callBackResult = request.isSuccess() ? "成功" : "失败";
        // 目前通知对方退货处理回调只需记录一下财务操作日志
        String orderNo = requestData.getOrderNo();
        orderOtherLogClient.createOrderDockingLog(orderNo, request.getOrgCode(), StringUtils.EMPTY, JsonUtils.toJsonIgnoreNull(request), "订单退货回调:" + callBackResult, StringUtils.EMPTY);
        // 对接状态信息
        saveDockingInfoStrategy(request, orgCode, orderNo);

        return RemoteResponse.<Boolean>custom().setSuccess().setData(true);
    }

    void saveDockingInfoStrategy(CallbackRequest<OrderReturnResult> request, String orgCode, String orderNo) {
        if (DockingConstant.GUANG_XI_ZHONG_LIU.equals(orgCode) && !request.isSuccess()) {
            dockingExtraService.saveOrUpdateDockingExtra(orderNo, orderNo, request.isSuccess(), request.getMsg());
        }
    }
}
