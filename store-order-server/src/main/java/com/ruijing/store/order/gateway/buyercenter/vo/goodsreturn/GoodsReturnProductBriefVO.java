package com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/1/27 15:55
 * @Description
 **/
public class GoodsReturnProductBriefVO implements Serializable {

    private static final long serialVersionUID = -6537174187787910885L;

    /**
     * 订单商品id
     */
    @RpcModelProperty("订单商品id")
    private Integer detailId;

    /**
     * 商品名称
     */
    @RpcModelProperty("商品名称")
    private String goodsName;

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnProductBriefVO{");
        sb.append("detailId='").append(detailId).append('\'');
        sb.append(", goodsName='").append(goodsName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
