package com.ruijing.store.order.gateway.buyercenter.request.goodsreturn;

import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.List;

@Model("扫码退货入参")
public class BarCodeGoodsReturnRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty("扫码退货订单列表")
    private List<OrderBarCodeReturnRequest> goodsReturnOrderList;

    public List<OrderBarCodeReturnRequest> getGoodsReturnOrderList() {
        return goodsReturnOrderList;
    }

    public void setGoodsReturnOrderList(List<OrderBarCodeReturnRequest> goodsReturnOrderList) {
        this.goodsReturnOrderList = goodsReturnOrderList;
    }

    @Override
    public String toString() {
        return "BarCodeGoodsReturnRequest{" +
                "goodsReturnOrderList=" + goodsReturnOrderList +
                '}';
    }
}