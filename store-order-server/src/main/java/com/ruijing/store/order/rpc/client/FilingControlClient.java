package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.store.filing.dto.FilingCertifyDetailRecordDTO;
import com.ruijing.store.filing.dto.FilingCertifyRecordRequestDTO;
import com.ruijing.store.filing.service.FilingControlBaseExternalService;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: Liwenyu
 * @create: 2024-03-07 11:55
 * @description:
 */
@ServiceClient
public class FilingControlClient {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @MSharpReference(remoteAppkey = "store-filing-service")
    private FilingControlBaseExternalService filingControlBaseExternalService;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @ServiceLog(description = "取消订单时解冻备案管控", operationType = OperationType.WRITE)
    public void unfreezeFilingControlWhenCancel(OrderMasterDO orderMasterDO){
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
        if(CollectionUtils.isEmpty(orderDetailDOList)){
            return;
        }
        FilingCertifyRecordRequestDTO filingCertifyRecordRequestDTO = new FilingCertifyRecordRequestDTO();
        filingCertifyRecordRequestDTO.setOrgId(orderMasterDO.getFuserid());
        List<FilingCertifyDetailRecordDTO> filingCertifyRecordDTOList = orderDetailDOList.stream().map(detail->{
            FilingCertifyDetailRecordDTO filingCertifyRecordDTO = new FilingCertifyDetailRecordDTO();
            filingCertifyRecordDTO.setSerialNumber(orderMasterDO.getForderno());
            filingCertifyRecordDTO.setProductId(detail.getProductSn());
            filingCertifyRecordDTO.setQuantity(detail.getFquantity().intValue());
            return filingCertifyRecordDTO;
        }).collect(Collectors.toList());
        filingCertifyRecordRequestDTO.setFcRecordList(filingCertifyRecordDTOList);
        this.unfreezeFilingControl(filingCertifyRecordRequestDTO);
    }

    @ServiceLog(description = "退货成功时解冻备案管控", operationType = OperationType.WRITE)
    public void unfreezeFilingControlWhenReturnSuccess(OrderMasterDO orderMasterDO){
        List<GoodsReturn> goodsReturns = goodsReturnMapper.findByOrderId(orderMasterDO.getId());
        Map<String, List<BigDecimal>> productIdCountMap = goodsReturns.stream().filter(goodsReturn -> GoodsReturnStatusEnum.SUCCESS.getCode().equals(goodsReturn.getGoodsReturnStatus()))
                .map(goodsReturn -> GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturn.getGoodsReturnDetailJSON()))
                .flatMap(List::stream)
                .collect(Collectors.groupingBy(GoodsReturnInfoDetailVO::getProductId, Collectors.mapping(GoodsReturnInfoDetailVO::getQuantity, Collectors.toList())));
        List<FilingCertifyDetailRecordDTO> filingCertifyRecordDTOList = new ArrayList<>(productIdCountMap.size());
        for(Map.Entry<String, List<BigDecimal>> entry : productIdCountMap.entrySet()){
            FilingCertifyDetailRecordDTO filingCertifyRecordDTO = new FilingCertifyDetailRecordDTO();
            filingCertifyRecordDTO.setProductId(Long.parseLong(entry.getKey()));
            filingCertifyRecordDTO.setQuantity(entry.getValue().stream().reduce(BigDecimal.ZERO, BigDecimal::add).intValue());
            filingCertifyRecordDTO.setSerialNumber(orderMasterDO.getForderno());
            filingCertifyRecordDTOList.add(filingCertifyRecordDTO);
        }
        FilingCertifyRecordRequestDTO recordRequestDTO = new FilingCertifyRecordRequestDTO();
        recordRequestDTO.setOrgId(orderMasterDO.getFuserid());
        recordRequestDTO.setFcRecordList(filingCertifyRecordDTOList);
        this.unfreezeFilingControl(recordRequestDTO);
    }



    private void unfreezeFilingControl(FilingCertifyRecordRequestDTO filingCertifyRecordRequestDTO){
        try{
            RemoteResponse<Boolean> response = filingControlBaseExternalService.unfreezeFilingControl(filingCertifyRecordRequestDTO);
            Preconditions.isTrue(response.isSuccess(), response.getMsg());
        } catch (Exception e){
            logger.error("解冻备案管控失败", e);
        }
    }
}
