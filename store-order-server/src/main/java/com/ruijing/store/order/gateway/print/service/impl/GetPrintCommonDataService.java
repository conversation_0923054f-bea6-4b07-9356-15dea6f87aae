package com.ruijing.store.order.gateway.print.service.impl;

import com.reagent.bid.api.rpc.dto.BidApprovalLogDTO;
import com.reagent.bid.api.rpc.enums.OperationEnum;
import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.approval.api.dto.PurchaseApprovalLogDTO;
import com.ruijing.store.electronicsign.api.dto.ElectronicSignOperationRecordDTO;
import com.ruijing.store.electronicsign.api.dto.OperationListDTO;
import com.ruijing.store.electronicsign.api.enums.BusinessTypeEnum;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignatureOperationEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.api.base.orderapprovallog.dto.OrderApprovalRequestDTO;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.api.base.other.dto.OrderPurchaseApprovalLogDTO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.translator.PurchaseApprovalLogTranslator;
import com.ruijing.store.order.rpc.client.BidClient;
import com.ruijing.store.order.rpc.client.ElectronicSignServiceClient;
import com.ruijing.store.order.rpc.client.PurchaseApprovalLogClient;
import com.ruijing.store.order.util.BarCodeUtils;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.order.util.QrCodeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2023/2/28 17:07
 * @description
 */
@Service
public class GetPrintCommonDataService {

    private final String CAT_TYPE = getClass().getSimpleName();

    @Resource
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @Resource
    private BidClient bidClient;

    @Resource
    private ElectronicSignServiceClient electronicSignServiceClient;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    /**
     * 获取订单号条形码
     * @param barCodeContent 订单号
     * @return 订单号条形码数据
     */
    public String getBarCode(String barCodeContent){
        try {
            return BarCodeUtils.getBase64Img(barCodeContent);
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "getBarCode", "生成订单号条形码失败\n入参:" + barCodeContent, e);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 获取二维码数据
     * @param qrCodeContent 内容
     * @return 二维码
     */
    public String getQrCode(String qrCodeContent){
        // 二维码或条形码
        try {
            return QrCodeUtil.getBase64(qrCodeContent, 120, 120);
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "packageOrderInfo", "单据打印，生成订单号二维码失败\n入参:" + qrCodeContent, e);
        }
        return StringUtils.EMPTY;
    }
    
    
    /**
     * 获取采购或竞价日志
     *
     * @param orderList              订单数据
     * @return 竞价/采购日志
     */
    public Map<Integer, List<OrderPurchaseApprovalLogDTO>> getPurchaseOrBidLog(List<OrderMasterDO> orderList) {
        return this.getPurchaseOrBidLog(orderList, null, false);
    }

    /**
     * 获取采购或竞价日志
     *
     * @param orderList              订单数据
     * @param filterBidOperationList 指定的竞价操作
     * @param getAfterTheLastReject  是否仅获取最后一次审批通过和其他共通部分的日志
     * @return 竞价/采购日志
     */
    public Map<Integer, List<OrderPurchaseApprovalLogDTO>> getPurchaseOrBidLog(List<OrderMasterDO> orderList, List<String> filterBidOperationList, boolean getAfterTheLastReject) {
        Map<Integer, List<OrderPurchaseApprovalLogDTO>> resultMap = new HashMap<>(orderList.size());
        // 查采购单
        Map<Integer, List<Integer>> buyAppIdOrderIdListMap = orderList.stream()
                .filter(orderMasterDO -> OrderTypeEnum.PURCHASE_ORDER.getCode().equals(orderMasterDO.getOrderType()))
                .collect(Collectors.groupingBy(OrderMasterDO::getFtbuyappid, Collectors.mapping(OrderMasterDO::getId, Collectors.toList())));
        if (!buyAppIdOrderIdListMap.isEmpty()) {
            this.fillOrderPurchaseApprovalLog(resultMap, buyAppIdOrderIdListMap);
        }
        // 查竞价
        Map<String, List<Integer>> bidNoOrderIdListMap = orderList.stream()
                .filter(orderMasterDO -> OrderTypeEnum.BID_ORDER.getCode().equals(orderMasterDO.getOrderType()))
                .collect(Collectors.groupingBy(OrderMasterDO::getBidOrderId, Collectors.mapping(OrderMasterDO::getId, Collectors.toList())));
        if (!bidNoOrderIdListMap.isEmpty()) {
            Map<String, List<BidApprovalLogDTO>> bidNoLogMap = bidClient.batchFindApprovalLogInfo(New.list(bidNoOrderIdListMap.keySet()));
            bidNoLogMap.forEach((bidNo, logList) -> {
                List<Integer> orderIdList = bidNoOrderIdListMap.get(bidNo);
                orderIdList.forEach(orderId -> resultMap.put(orderId, this.translateAndSortBidApprovalLog(logList, filterBidOperationList, getAfterTheLastReject)));
            });
        }
        return resultMap;
    }

    /**
     * 采购单日志装填
     * @param targetMap 被装填到的映射
     * @param buyAppIdOrderIdListMap 采购单id与订单id的映射
     */
    private void fillOrderPurchaseApprovalLog(Map<Integer, List<OrderPurchaseApprovalLogDTO>> targetMap, Map<Integer, List<Integer>> buyAppIdOrderIdListMap) {
        List<PurchaseApprovalLogDTO> purchaseApprovalLogList = purchaseApprovalLogClient.getApprovalLogByIdList(New.set(buyAppIdOrderIdListMap.keySet()));
        // 查采购单电子签名
        List<String> appIdList = buyAppIdOrderIdListMap.keySet().stream().map(Objects::toString).collect(toList());
        List<ElectronicSignOperationRecordDTO> appElecSignList = this.getElectronicSignList(BusinessTypeEnum.APPLICATION, New.list(ElectronicSignatureOperationEnum.SUBMIT_THE_PURCHASING_APPLICATION, ElectronicSignatureOperationEnum.PURCHASING_APPROVAL), appIdList);
        Map<String, String> businessIdPhotoMap = DictionaryUtils.toMap(appElecSignList, ElectronicSignOperationRecordDTO::getInteractionId, ElectronicSignOperationRecordDTO::getSignPhoto);
        purchaseApprovalLogList.forEach(item -> {
            List<Integer> orderIdList = buyAppIdOrderIdListMap.get(item.getApplicationId());
            orderIdList.forEach(orderId -> {
                List<OrderPurchaseApprovalLogDTO> logList = targetMap.computeIfAbsent(orderId, k -> New.list());
                OrderPurchaseApprovalLogDTO log = PurchaseApprovalLogTranslator.dtoToOrderOrderPurchaseLogDTO(item);
                log.setElectronicSignUrl(businessIdPhotoMap.get(item.getId().toString()));
                logList.add(log);
            });
        });
    }

    private List<ElectronicSignOperationRecordDTO> getElectronicSignList(BusinessTypeEnum businessTypeEnum, List<ElectronicSignatureOperationEnum> electronicSignatureOperationEnumList, List<String> businessIdList) {
        // 获取验收审批电子签名
        OperationListDTO operationListDTO = new OperationListDTO();
        operationListDTO.setBusinessType(businessTypeEnum);
        operationListDTO.setOperation(electronicSignatureOperationEnumList);
        operationListDTO.setBusinessIdList(businessIdList);
        return electronicSignServiceClient.getElectronicSignData(operationListDTO);
    }

    /**
     * 获取竞价日志
     *
     * @param filterBidOperationList 指定的竞价操作
     * @param getAfterTheLastReject  是否仅获取初审终审最后一次审批通过和其他共通部分的日志
     * @return 日志记录
     */
    private List<OrderPurchaseApprovalLogDTO> translateAndSortBidApprovalLog(List<BidApprovalLogDTO> bidApprovalLogList, List<String> filterBidOperationList, boolean getAfterTheLastReject) {
        if (getAfterTheLastReject) {
            // 初审相关的枚举（可重复部分）
            final List<String> firstBidEnumList = New.list(OperationEnum.SUBMIT_BEGIN_APPROVAL.getName(), OperationEnum.BEGIN_APPROVAL_PASS.getName(), OperationEnum.FIRST_AUTO_PASS.getName(), OperationEnum.BEGIN_APPROVAL_REJECT.getName(), OperationEnum.FIRST_SKIP_APPROVAL.getName());
            // 终审相关的枚举（可重复部分）
            final List<String> finalBidEnumList = New.list(OperationEnum.SUBMIT_FINAL_APPROVAL.getName(), OperationEnum.FINAL_APPROVAL_PASS.getName(), OperationEnum.FINAL_AUTO_PASS.getName(), OperationEnum.FINAL_APPROVAL_REJECT.getName(), OperationEnum.FINAL_SKIP_APPROVAL.getName());
            // 获取最后一次通过的初审日志
            List<BidApprovalLogDTO> firstBidApprovalLogList = bidApprovalLogList.stream().filter(log -> firstBidEnumList.contains(log.getOperationName())).sorted(Comparator.comparing(BidApprovalLogDTO::getLogId)).collect(toList());
            List<BidApprovalLogDTO> validFirstBidApprovalLogList = this.getBidLogAfterTheLastReject(firstBidApprovalLogList, OperationEnum.BEGIN_APPROVAL_REJECT.getName());
            // 获取最后一次通过的终审日志
            List<BidApprovalLogDTO> finalBidApprovalLogList = bidApprovalLogList.stream().filter(log -> finalBidEnumList.contains(log.getOperationName())).sorted(Comparator.comparing(BidApprovalLogDTO::getLogId)).collect(toList());
            List<BidApprovalLogDTO> validFinalBidApprovalLogList = this.getBidLogAfterTheLastReject(finalBidApprovalLogList, OperationEnum.FINAL_APPROVAL_REJECT.getName());
            // 其他非重复部分，累计起来
            bidApprovalLogList = bidApprovalLogList.stream().filter(log -> !firstBidEnumList.contains(log.getOperationName()) && !finalBidEnumList.contains(log.getOperationName())).collect(toList());
            bidApprovalLogList.addAll(validFirstBidApprovalLogList);
            bidApprovalLogList.addAll(validFinalBidApprovalLogList);
        }
        // 如果有指定查询的竞价操作，则只返回相应的竞价操作
        Stream<BidApprovalLogDTO> stream = CollectionUtils.isNotEmpty(filterBidOperationList) ? bidApprovalLogList.stream().filter(log -> filterBidOperationList.contains(log.getOperationName())) : bidApprovalLogList.stream();
        return stream.sorted(Comparator.comparing(BidApprovalLogDTO::getLogId))
                .map(PurchaseApprovalLogTranslator::dtoToOrderOrderBidLogDTO).collect(Collectors.toList());
    }

    /**
     * 获取最后一次审批不通过后的的日志。 从最后一条日志向前遍历找到最后一次审批不通过，从它开始向下的即都为最后一次审批通过的日志了
     *
     * @param logList   日志
     * @param rejectKey 拒绝的标记
     * @return 拒绝后的日志
     */
    private List<BidApprovalLogDTO> getBidLogAfterTheLastReject(List<BidApprovalLogDTO> logList, String rejectKey) {
        int lastRejectIndex = -1;
        for (int i = logList.size() - 1; i > -1; i--) {
            if (rejectKey.equals(logList.get(i).getOperationName())) {
                lastRejectIndex = i;
                break;
            }
        }
        // 截取成功的日志返回
        return New.list(logList.subList(lastRejectIndex + 1, logList.size()));
    }

    /**
     * 获取订单id-验收审批日志映射
     * @param orgId 单位id
     * @param orderIdList 订单id列表
     * @return 映射
     */
    public Map<Integer, List<OrderApprovalLogDTO>> getOrderIdApprovalLogMap(int orgId, List<Integer> orderIdList) {
        // 获取验收/验收审批日志
        List<OrderApprovalLogDTO> orderApprovalLogList;
        OrderApprovalRequestDTO orderApprovalRequestDTO = new OrderApprovalRequestDTO();
        orderApprovalRequestDTO.setOrderIdList(orderIdList);
        orderApprovalRequestDTO.setTypeList(New.list(OrderApprovalEnum.PASS.getValue(), OrderApprovalEnum.REJECT.getValue(), OrderApprovalEnum.RECEIPT.getValue(), OrderApprovalEnum.NO_NEED_ACCEPT_APPROVAL.getValue()));
        if(orgId == OrgEnum.YANG_JIANG_SHI_REN_MIN_YI_YUAUN.getValue()){
            orderApprovalRequestDTO.setTypeList(New.list(OrderApprovalEnum.PASS.getValue(), OrderApprovalEnum.REJECT.getValue(), OrderApprovalEnum.NO_NEED_ACCEPT_APPROVAL.getValue()));
        }
        final List<Integer> getLastPassAcceptApproveLogOrgList = New.list(OrgEnum.FO_SHAN_SHI_FU_YOU_BAO_JIAN_YUAN.getValue(), OrgEnum.XIA_MEN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getValue());

        if (getLastPassAcceptApproveLogOrgList.contains(orgId)) {
            // 佛山市妇幼保健院特殊需求，如为竞价仅返回提交竞价初审、初审通过、初选通过、终审通过的竞价日志
            // 获取最后一手有效的验收审批日志
            orderApprovalLogList = orderApprovalLogService.getLastPassAcceptApproveLog(orderApprovalRequestDTO);
        } else {
            orderApprovalLogList = orderApprovalLogService.findByOrderIdListAndStatus(orderApprovalRequestDTO);
        }
        // 获取电子签名
        orderApprovalLogService.fillLogWithElectronicSign(orderApprovalLogList, New.list(ElectronicSignatureOperationEnum.ORDER_RECEIVE, ElectronicSignatureOperationEnum.ACCEPTANCE_APPROVAL));
        return DictionaryUtils.groupBy(orderApprovalLogList, OrderApprovalLogDTO::getOrderId);
    }
}
