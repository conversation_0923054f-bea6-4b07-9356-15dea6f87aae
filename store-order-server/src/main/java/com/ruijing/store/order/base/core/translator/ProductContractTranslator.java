package com.ruijing.store.order.base.core.translator;


import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.contract.dto.PriceContractProductDTO;
import com.ruijing.store.contract.dto.reagentProduct.ReagentProductDTO;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.gateway.buyercenter.vo.contract.ProductContractVO;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @description: 商品合同 转换类
 * @author: z<PERSON><PERSON><PERSON>
 * @create: 2021-06-29 11:26
 **/
public class ProductContractTranslator {


    /**
     * 根据订单明细列表及商品合同Map生成对应的ProductContractVO
     *
     * @param orderDetailDOS              订单明细列表
     * @param productSnToPriceContractMap 订单商品明细对应的合同信息（使用productSn对应关系）
     * @return
     */
    public static List<ProductContractVO> orderDetailToContractVo(List<OrderDetailDO> orderDetailDOS, Map<Long, ReagentProductDTO>  productSnToPriceContractMap) {
        if (CollectionUtils.isEmpty(orderDetailDOS)) {
            return null;
        }
        List<ProductContractVO> productContractVOS = New.listWithCapacity(orderDetailDOS.size());
        ReagentProductDTO contractProductDTO;
        for (OrderDetailDO orderDetail : orderDetailDOS) {
            contractProductDTO = productSnToPriceContractMap.get(orderDetail.getProductSn());
            productContractVOS.add(ProductContractTranslator.orderDetailToSingleContractVo(orderDetail, contractProductDTO));
        }
        return productContractVOS;
    }


    /**
     * 根据订单明细及合同信息生成单个合同商品信息返回体
     * @param orderDetail   订单明细信息
     * @param contractProductDTO 订单明细商品对应的PriceContractProductDTO（使用productSn对应关系）
     * @return
     */
    public static ProductContractVO orderDetailToSingleContractVo(OrderDetailDO orderDetail, ReagentProductDTO contractProductDTO) {
        ProductContractVO productContractVO = new ProductContractVO();
        // 设置订单明细信息
        BigDecimal fquantity = orderDetail.getFquantity();
        BigDecimal originalPrice = orderDetail.getOriginalPrice();
        productContractVO.setOrderDetailId(orderDetail.getId());
        productContractVO.setQuantity(fquantity);
        productContractVO.setOriginalPrice(originalPrice);
        productContractVO.setTotalPrice(fquantity.multiply(originalPrice));
        productContractVO.setProductSn(orderDetail.getProductSn());
        productContractVO.setGoodname(orderDetail.getFgoodname());
        productContractVO.setFspec(orderDetail.getFspec());
        // 设置合同信息
        if (contractProductDTO != null) {
            productContractVO.setMethodology(contractProductDTO.getMethodology());
            productContractVO.setBrandName(contractProductDTO.getBrandName());
            productContractVO.setOriginPlace(contractProductDTO.getOriginPlace());
            productContractVO.setUnit(contractProductDTO.getUnit());
            productContractVO.setRemark(contractProductDTO.getRemark());
        }
        return productContractVO;
    }
}
