<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.minor.mapper.ProductDescriptionSnapshotMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.minor.model.ProductDescriptionSnapshotDO">
    <!--@mbg.generated-->
    <!--@Table t_product_description_snapshot-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="VARCHAR" property="businessId" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, business_id, business_type, product_id, description
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_product_description_snapshot
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_product_description_snapshot
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.minor.model.ProductDescriptionSnapshotDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_product_description_snapshot (business_id, business_type, product_id, 
      description)
    values (#{businessId,jdbcType=VARCHAR}, #{businessType,jdbcType=INTEGER}, #{productId,jdbcType=BIGINT}, 
      #{description,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.minor.model.ProductDescriptionSnapshotDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_product_description_snapshot
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="description != null">
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.minor.model.ProductDescriptionSnapshotDO">
    <!--@mbg.generated-->
    update t_product_description_snapshot
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.minor.model.ProductDescriptionSnapshotDO">
    <!--@mbg.generated-->
    update t_product_description_snapshot
    set business_id = #{businessId,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=INTEGER},
      product_id = #{productId,jdbcType=BIGINT},
      description = #{description,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

<!--auto generated by MybatisCodeHelper on 2020-11-03-->
  <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_product_description_snapshot(
        business_id,
        business_type,
        product_id,
        description
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.businessId,jdbcType=VARCHAR},
            #{element.businessType,jdbcType=INTEGER},
            #{element.productId,jdbcType=BIGINT},
            #{element.description,jdbcType=LONGVARCHAR}
            )
        </foreach>
    </insert>

<!--auto generated by MybatisCodeHelper on 2021-01-06-->
  <select id="findProductDescByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_product_description_snapshot
        <where>
            <if test="businessId != null">
                and business_id=#{businessId,jdbcType=VARCHAR}
            </if>
            <if test="businessType != null">
                and business_type=#{businessType,jdbcType=INTEGER}
            </if>
            <if test="productId != null">
                and product_id=#{productId,jdbcType=BIGINT}
            </if>
        </where>
    </select>
</mapper>