package com.ruijing.store.order.rpc.client;

import com.reagent.order.api.OrderEventStatusRpcService;
import com.reagent.order.dto.request.OrderEventStatusRequestDTO;
import com.reagent.order.dto.response.OrderEventStatusResponseDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/20 16:17
 * @description
 */
@ServiceClient
public class OrderPushEventStatusClient {

    @MSharpReference(remoteAppkey = "order-thunder-service")
    private OrderEventStatusRpcService orderEventStatusRpcService;

    /**
     * 查询订单推送状态
     * @param orderEventStatusRequestDto 订单事件请求
     * @return 订单事件类型
     */
    public List<OrderEventStatusResponseDTO> listEventPushStatus(OrderEventStatusRequestDTO orderEventStatusRequestDto){
        Preconditions.notNull(orderEventStatusRequestDto, "查询的入参不可为空");
        Preconditions.notEmpty(orderEventStatusRequestDto.getOrderNoList(), "查询参数的订单号列表不可为空");
        RemoteResponse<List<OrderEventStatusResponseDTO>> response = orderEventStatusRpcService.listEventPushStatus(orderEventStatusRequestDto);
        return response.getData();
    }
}
