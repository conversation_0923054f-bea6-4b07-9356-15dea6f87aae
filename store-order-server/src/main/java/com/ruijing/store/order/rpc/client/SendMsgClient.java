package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.message.api.dto.WeComTextCardDTO;
import com.ruijing.message.api.service.WeComBizService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.cms.api.dto.SendingPersonalAndDefaultDTO;
import com.ruijing.store.cms.api.enums.GroupTypeEnum;
import com.ruijing.store.cms.api.enums.SendingBusinessEnum;
import com.ruijing.store.cms.api.enums.SendingWayEnum;
import com.ruijing.store.cms.api.request.SendingSettingParam;
import com.ruijing.store.cms.api.service.SendingPersonalSettingService;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: liwenyu
 * @createTime: 2023-03-20 09:29
 * @description:
 **/
@ServiceClient
public class SendMsgClient {

    @MSharpReference(remoteAppkey = "msharp-message-service")
    private WeComBizService weComBizService;

    @MSharpReference(remoteAppkey="store-cms-service")
    private SendingPersonalSettingService sendingPersonalSettingService;

    @ServiceLog(description = "获取启用了指定类型消息推送的用户", serviceType = ServiceType.RPC_CLIENT)
    public List<String> getOrgEnableMsgPushGuidList(List<String> userGuidList, Integer orgId, SendingBusinessEnum sendingBusinessEnum, SendingWayEnum sendingWayEnum){
        String orgIdStr = orgId.toString();
        List<SendingSettingParam> param = userGuidList.stream().map(userGuid->{
            SendingSettingParam sendingSettingParam = new SendingSettingParam();
            sendingSettingParam.setGuid(userGuid);
            sendingSettingParam.setGroupId(orgIdStr);
            sendingSettingParam.setGroupType(GroupTypeEnum.ORGANIZATION.getValue());
            sendingSettingParam.setBusiness(sendingBusinessEnum.getValue());
            sendingSettingParam.setWay(sendingWayEnum.getValue().byteValue());
            return sendingSettingParam;
        }).collect(Collectors.toList());
        RemoteResponse<List<SendingPersonalAndDefaultDTO>> response = sendingPersonalSettingService.getSendingSettingByList(param);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData().stream().filter(config->config.getEnable() == 1).map(SendingPersonalAndDefaultDTO::getGuid).collect(Collectors.toList());
    }

    @ServiceLog(description = "发企业微信消息", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public void sendWeComMsg(WeComTextCardDTO weComTextCardDTO){
        RemoteResponse<Void> response = weComBizService.sendTextCard(weComTextCardDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }
}
