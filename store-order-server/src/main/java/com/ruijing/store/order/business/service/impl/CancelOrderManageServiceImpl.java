package com.ruijing.store.order.business.service.impl;

import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.log.enums.OrderDockingResultEnum;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.enums.product.OrderProductInventoryStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductTransactionStatusEnum;
import com.reagent.order.dto.request.ThirdPartOrderMasterDTO;
import com.reagent.order.enums.OrderEventTypeEnum;
import com.reagent.order.enums.OuterBuyerDockingTypeEnum;
import com.reagent.tpi.tpiclient.message.req.order.OrderReq;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.whitehole.database.dto.eventlog.OpUserTypeEnum;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.shop.crm.api.pojo.dto.QualificationDTO;
import com.ruijing.shop.srm.api.support.enums.ShopGasBottleUnbindEnum;
import com.ruijing.store.apply.dto.ApplicationDetailDTO;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.dto.application.ApplyRefBusinessPriceDTO;
import com.ruijing.store.apply.dto.application.ApplyRefBusinessPriceQueryDTO;
import com.ruijing.store.apply.enums.application.ApplyManageOperationEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.docking.dto.DockingExtraDTO;
import com.ruijing.store.order.api.base.docking.enums.DockingTypeEnum;
import com.ruijing.store.order.api.base.enums.*;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.ApplyCancelOrderReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.CancelOrderReqDTO;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.OrderMasterTranslator;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.base.minor.mapper.RefCouponPurchaserDOMapper;
import com.ruijing.store.order.base.minor.mapper.RefInvoiceOrderMapper;
import com.ruijing.store.order.base.minor.model.RefCouponPurchaserDO;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.handler.OrderMessageHandler;
import com.ruijing.store.order.business.handler.WeChatMessageHandler;
import com.ruijing.store.order.business.service.CancelOrderManageService;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.business.service.OrderMasterForTPIService;
import com.ruijing.store.order.business.service.RiskRuleService;
import com.ruijing.store.order.business.service.orgondemand.ClinicalOrderService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.*;
import com.ruijing.store.order.other.service.ProductStockService;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.rpc.util.SuppQualificationUtils;
import com.ruijing.store.order.service.ApplicationBaseService;
import com.ruijing.store.order.util.CommonValueUtils;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.user.api.dto.department.DepartmentThirdPartyDTO;
import com.ruijing.store.warehouse.service.WarehouseStockOccupyService;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDTO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@Service
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
public class CancelOrderManageServiceImpl implements CancelOrderManageService {

    // 经讨论约定这个1540 为供应商特别的标志
    private final static Integer PROMISE_SUPPLIER_FLAG = 1540;

    /**
     * 系统ID
     */
    private final String SYSTEM_OPERATOR_ID = "-1" ;

    private final static String CAT_TYPE = "OrderManageServiceImpl";

    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 在订单填写发票
     */
    private static final String SAVE_INVOICE_IN_ORDER = "10";

    /**
     * thunder是否监听生成订单状态，如果是则关闭生成订单推送的开关
     */
    @PearlValue(key = "order.thunder.listen.cancelOrder", defaultValue = "false")
    private Boolean enableMqPushCancelOrder;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private OrderEmailHandler orderEmailHandler;

    @Resource
    private WeChatMessageHandler weChatMessageHandler;

    @Resource
    private CacheClient cacheClient;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Resource
    private OrderMasterForTPIService orderMasterForTPIService;

    @Resource
    private ApplicationBaseService applicationBaseService;

    @Resource
    private ClinicalOrderService clinicalOrderService;

    @Resource
    private OrderManageService orderManageService;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private RefInvoiceOrderMapper refInvoiceOrderMapper;

    @Resource
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @Resource
    private RefCouponPurchaserDOMapper refCouponPurchaserDOMapper;

    @Resource
    private InvoiceClient invoiceClient;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Autowired
    private UserClient userClient;

    @Resource
    private DepartmentRpcClient departmentRpcClient;

    @Autowired
    private OrderOtherLogClient orderOtherLogClient;

    @Resource
    private DockingExtraService dockingExtraService;

    @Resource
    private HuaNongServiceClient huaNongServiceClient;

    @Resource
    private OrderUniqueBarCodeRPCClient orderUniqueBarCodeRPCClient;

    @Resource
    private ThirdPartOrderRPCClient thirdPartOrderRPCClient;

    @Resource
    private BizWareHouseClient bizWareHouseClient;

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @Resource
    private SuppClient suppClient;

    @Resource
    private RiskRuleService riskRuleService;

    @Resource
    private OrderMessageHandler orderMessageHandler;

    @Resource
    private WarehouseStockOccupyService warehouseStockOccupyService;

    @Resource
    private FilingControlClient filingControlClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private GasBottleClient gasBottleClient;

    @Resource
    private ProductStockService productStockService;


    /**
     * 取消订单/申请取消订单入口
     *
     * @param applyCancelOrderReqDTO
     */
    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public void cancelOrder(ApplyCancelOrderReqDTO applyCancelOrderReqDTO) {
        BusinessErrUtil.notNull(applyCancelOrderReqDTO, "取消订单入参为null");
        BusinessErrUtil.notNull(applyCancelOrderReqDTO.getOrderId(), "订单id不能为空！");
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(applyCancelOrderReqDTO.getOrderId());
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.CANCELLED_ORDER_NOT_EXIST);
        // 2022/04/06加 华农屏蔽取消订单入口，并让采购人到管理平台操作
        BusinessErrUtil.isTrue(!OrgEnum.HUA_NAN_NONG_YE_DA_XUE.getCode().equals(orderMasterDO.getFusercode()), ExecptionMessageEnum.CONTACT_PURCHASER_TO_CANCEL);
        // -1 为系统取消人id
        final String CANCEL_MAN_ID = "-1";
        BusinessErrUtil.isTrue(applyCancelOrderReqDTO.getFcancelmanid().equals(orderMasterDO.getFbuyerid().toString()) || CANCEL_MAN_ID.equals(applyCancelOrderReqDTO.getFcancelmanid()), ExecptionMessageEnum.NON_PURCHASER_CANNOT_CANCEL);
        this.handleCancelOrder(applyCancelOrderReqDTO,orderMasterDO,true);
    }

    /**
     * 由thunder服务发起请求进行订单取消
     * <AUTHOR>
     * @param applyCancelOrderReqDTO  调用参数
     */
    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public void cancelOrderByThunder(ApplyCancelOrderReqDTO applyCancelOrderReqDTO) {
        BusinessErrUtil.notNull(applyCancelOrderReqDTO, "取消订单入参为null");
        BusinessErrUtil.notNull(applyCancelOrderReqDTO.getOrderId() != null, "订单id不能为空！");
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(applyCancelOrderReqDTO.getOrderId());
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.CANCELLED_ORDER_NOT_EXIST);
        // -1 为系统取消人id
        final String CANCEL_MAN_ID = "-1";
        final String CANCEL_MAN = "系统";
        Integer opUserType = OpUserTypeEnum.SYS.getValue();
        applyCancelOrderReqDTO.setFcancelmanid(CANCEL_MAN_ID);
        applyCancelOrderReqDTO.setFcancelman(CANCEL_MAN);
        // 暨南大学特殊处理
        if(OrgEnum.JI_NAN_DA_XUE.getValue() == orderMasterDO.getFuserid()){
            List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
            boolean isDangerous = orderDetailDOList.stream().anyMatch(orderDetailDO -> CategoryConstant.DANGEROUS_ID == orderDetailDO.getFirstCategoryId() ||
                    CategoryConstant.CONVENTIONAL_CHEMICAL_REAGENTS == orderDetailDO.getSecondCategoryId());
            if (isDangerous){
                applyCancelOrderReqDTO.setFcancelmanid(String.valueOf(orderMasterDO.getFbuyerid()));
                applyCancelOrderReqDTO.setFcancelman(orderMasterDO.getFbuyername());
                opUserType = OpUserTypeEnum.ORG_USER.getValue();
            }
        }
        applyCancelOrderReqDTO.setOpUserType(opUserType);
        this.handleCancelOrder(applyCancelOrderReqDTO,orderMasterDO,false);
    }

    /**
     * 同意取消订单
     *
     * @param cancelOrderReqDTO
     */
    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public void agreeCancelOrder(CancelOrderReqDTO cancelOrderReqDTO) {
        try {
            cacheClient.controlRepeatOperation(OrderOperationConstant.BUYER_CENTER_ORDER_OP + cancelOrderReqDTO.getOrderMasterId().toString(), 3*60);
            this.agreeCancelOrderCore(cancelOrderReqDTO);
        } catch (Exception e) {
            throw e;
        } finally {
            cacheClient.removeCache(OrderOperationConstant.BUYER_CENTER_ORDER_OP + cancelOrderReqDTO.getOrderMasterId().toString());
        }
    }

    /**
     * 同意取消订单核心代码
     * @param cancelOrderReqDTO 取消参数
     */
    private void agreeCancelOrderCore(CancelOrderReqDTO cancelOrderReqDTO){
        final String SYSTEM_AUTO_CANCEL_ID = "-1";
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(cancelOrderReqDTO.getOrderMasterId());
        Preconditions.notNull(orderMasterDO, "订单查询返回为null");
        Preconditions.notNull(orderMasterDO.getFbuyerid(), "购买者id为空");
        Preconditions.notNull(cancelOrderReqDTO.getCancelManId(), "同意取消者id为空");
        BusinessErrUtil.isTrue(orderMasterDO.getFbuyerid().toString().equals(cancelOrderReqDTO.getCancelManId()) || SYSTEM_AUTO_CANCEL_ID.equals(cancelOrderReqDTO.getCancelManId()) || PROMISE_SUPPLIER_FLAG.equals(Integer.parseInt(cancelOrderReqDTO.getCancelManId())), ExecptionMessageEnum.CANNOT_AGREE_CANCEL_OTHERS_ORDER);
        BusinessErrUtil.isTrue(OrderStatusEnum.SupplierApplyToCancel.value.equals(orderMasterDO.getStatus()) || OrderStatusEnum.PurchaseApplyToCancel.value.equals(orderMasterDO.getStatus()), ExecptionMessageEnum.ORDER_NOT_SUPPLIER_CANCEL_STATUS);

        // 1.通用取消订单逻辑
        generalCancelOrder(orderMasterDO,true);

        // 2.修改订单状态 为关闭
        logger.info("agreeCancelOrder 参数1{}", orderMasterDO.toString());
        updateCancelOrder(orderMasterDO.getId(), OrderStatusEnum.Close.getValue(), null, null, null, new Date(), null);

        // 3.删除供应商填写的发票
        invoiceClient.deleteInvoiceByOrderIds(New.list(cancelOrderReqDTO.getOrderMasterId()));

        // 4.采购人同意取消订单给供应商发送邮件、微信消息
        orderMessageHandler.sendPurchaserAgreeCancelOrderEmailToSupplier(orderMasterDO, cancelOrderReqDTO.getCancelManId());

        // 5.释放经费
        orderManageService.orderFundCardUnFreeze(orderMasterDO);

        // 7.记录操作日志
        Integer approveStatus = Integer.parseInt(cancelOrderReqDTO.getCancelManId()) == PROMISE_SUPPLIER_FLAG ? OrderApprovalEnum.SUPPLIER_AGREE_CANCEL_ORDER.getValue() : OrderApprovalEnum.BUYER_AGREE_CANCEL_ORDER.getValue();
        addOrderApprovalLog(orderMasterDO.getId(), null,null, approveStatus, Integer.parseInt(cancelOrderReqDTO.getCancelManId()));
    }


    /**
     * 保存订单日志
     * @param orderId
     * @param photo
     * @param reason
     * @param approveStatus
     * @param operatorId
     */
    private void addOrderApprovalLog(Integer orderId, String photo, String reason, Integer approveStatus, Integer operatorId){
        OrderApprovalLog orderApprovalLog = new OrderApprovalLog();
        orderApprovalLog.setOrderId(orderId);
        orderApprovalLog.setReason(reason);
        orderApprovalLog.setPhoto(photo);
        orderApprovalLog.setApproveStatus(approveStatus);
        orderApprovalLog.setOperatorId(operatorId);
        orderApprovalLogMapper.insertSelective(orderApprovalLog);
    }

    /**
     * 取消订单 待确认的订单
     * @param
     * @return
     */
    private void cancelOrderForWaitingConfirm(OrderMasterDO orderMasterDO, ApplyCancelOrderReqDTO applyCancelOrderReqDTO, boolean callThirdPlatform) {

        String cancelMan = applyCancelOrderReqDTO.getFcancelman();
        String cancelManId = applyCancelOrderReqDTO.getFcancelmanid();
        String cancelReason = applyCancelOrderReqDTO.getFcancelreason();
        Integer opUserType = applyCancelOrderReqDTO.getOpUserType();
        Preconditions.notNull(cancelManId, "取消人id为空");
        Preconditions.notNull(orderMasterDO.getFbuyerid(), "购买者id为空");
        BusinessErrUtil.isTrue(orderMasterDO.getFbuyerid().toString().equals(cancelManId) || SYSTEM_OPERATOR_ID.equals(cancelManId), ExecptionMessageEnum.CANNOT_CANCEL_OTHERS_ORDER);

        // 1.通用取消订单逻辑
        generalCancelOrder(orderMasterDO,callThirdPlatform);

        // 2.更新取消订单状态
        Date date = new Date();
        OrderMasterDO updated = updateCancelOrder(orderMasterDO.getId(), OrderStatusEnum.Close.getValue(), cancelMan, cancelManId, date, date, cancelReason);

        // 3.记录取消订单日志
        addCancelOrderLog(updated, OrderApprovalEnum.CANCEL.getValue(), cancelReason == null ? OrderOperateLogConstant.BUYER_CANCEL_ORDER : cancelReason, opUserType);

        // 4.取消订单给供应商发送邮件、微信消息
        orderMasterDO.setFcanceldate(date);
        orderMessageHandler.sendOrderCancelEmailToSupplier(orderMasterDO, cancelManId);
        orderMessageHandler.cancelNotConfirmWechatToSupp(orderMasterDO);
        // 5.删除供应商填写的发票
        invoiceClient.deleteInvoiceByOrderIds(New.list(orderMasterDO.getId()));

        // 6.释放经费
        orderManageService.orderFundCardUnFreeze(orderMasterDO);
    }


    /**
     *  通用采购人逻辑
     *      待确认（采购人取消订单） 及 同意取消订单
     * @param orderMasterDO 数据体
     * @param callThirdPlatform 是否调用第三方平台推送
     */
    private void generalCancelOrder(OrderMasterDO orderMasterDO, boolean callThirdPlatform){

        // 1.释放合同金额
        clinicalOrderService.releaseReagentProductAnnualUsage(OrderApprovalEnum.CANCEL, orderMasterDO, null);

        // 2.采购金额统计
        applicationBaseService.updateApplyManageProductUsage(orderMasterDO, ApplyManageOperationEnum.CLOSE_ORDER.getValue(), null, null);

        // 3.更新订单状态到第三方平台
        if (callThirdPlatform) {
            // 华农旧代码暂时放在这里
            if (OrgEnum.HUA_NAN_NONG_YE_DA_XUE.getCode().equals(orderMasterDO.getFusercode())) {
                huaNongCancelOrder(orderMasterDO, orderMasterDO.getFrefuseCancelReason());
            }
            pushOrderToThirdPlatform(orderMasterDO);
        }

        // 4.返回库存
        productStockService.addSku(orderMasterDO.getFsuppid(),orderMasterDO);

        // 5.如果使用了优惠券，需要恢复优惠券
        recoveryCoupon(orderMasterDO);

        // 6.取消订单成功，返还限额
        riskRuleService.amountLimitBackForOrderCancel(orderMasterDO);

        // 7.释放预占库存
        warehouseStockOccupyService.releaseAll(orderMasterDO.getId(), orderMasterDO.getForderno());

        // 8.备案管控解冻
        filingControlClient.unfreezeFilingControlWhenCancel(orderMasterDO);

        // 9.将一物一码状态设置为已取消
        this.updateBarcodeStatusWhenCancelComplete(orderMasterDO.getForderno());

        // 10.气瓶解绑
        this.unbindGasBottleWhenCancelComplete(orderMasterDO.getId(), orderMasterDO.getForderno(), orderMasterDO.getFuserid());
    }



    /**
     * 推送订单信息到第三方管理平台
     * @param orderMasterDO     订单快照
     */
    private void pushOrderToThirdPlatform(OrderMasterDO orderMasterDO) {
        // 新单位走order—thunder-service接口
        if (dockingConfigCommonService.getIfNeedOldPush(orderMasterDO, New.list(OuterBuyerDockingTypeEnum.ORDER_PUSH))) {
            if(enableMqPushCancelOrder){
                return;
            }
            Runnable task = () -> {
                ThirdPartOrderMasterDTO thirdPartOrder = OrderMasterTranslator.doToThirdPartOrderDTO(orderMasterDO);
                // 订单确认推送管理平台
                thirdPartOrderRPCClient.pushSingleOrderInfo(thirdPartOrder, OrderEventTypeEnum.CANCEL_ORDER, String.valueOf(DockingConstant.SYSTEM_OPERATOR_ID), DockingConstant.SYSTEM_OPERATOR_NAME);
            };
            AsyncExecutor
                    .listenableRunAsync(task)
                    .addFailureCallback(ex -> {
                        logger.error("订单号:" + orderMasterDO.getForderno() + "取消第三方订单状态失败：" + ex);
                        Cat.logError(CAT_TYPE, "pushSingleOrderInfo", "取消第三方订单状态失败：", ex);
                        dockingExtraService.saveOrUpdateDockingExtra(orderMasterDO.getForderno(), orderMasterDO.getForderno(), false, ex.getMessage());
                    });
        } else {
            // 旧单位走老接口
            orderMasterForTPIService.updateThirdPlatformOrder(OrderStatusEnum.Close.getValue(), orderMasterDO.getForderno());
        }
    }

    @Override
    public void cancelOfflineOrder(CancelOrderReqDTO cancelOrderReqDTO) {
        Integer orderId = cancelOrderReqDTO.getOrderMasterId();
        OrderMasterDO orderMaster = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMaster, ExecptionMessageEnum.CANCEL_FAILED_NO_ORDER);
        final Integer status = orderMaster.getStatus();
        Integer orderStatus = status;
        BusinessErrUtil.isTrue(orderMaster.getFbuyerid().toString().equals(cancelOrderReqDTO.getCancelManId()), ExecptionMessageEnum.CANNOT_CANCEL_OTHERS_ORDER);
        BusinessErrUtil.isTrue(OrderStatusEnum.WaitingForReceive.getValue().equals(orderStatus) ||
                OrderStatusEnum.WaitingForStatement_1.getValue().equals(orderStatus) ||
                OrderStatusEnum.OrderReceiveApproval.getValue().equals(orderStatus), ExecptionMessageEnum.ORDER_STATUS_ABNORMAL_CANNOT_CANCEL);

        List<BaseOrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(New.list(orderId), OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue());
        if(CollectionUtils.isNotEmpty(orderExtraDTOList) && CommonValueUtils.TRUE_NUMBER_STR.equals(orderExtraDTOList.get(0).getExtraValue())){
            List<OrderUniqueBarCodeDTO> orderUniqueBarCodeDTOList = orderUniqueBarCodeRPCClient.findByOrderNo(orderMaster.getForderno(), New.list(UniqueBarCodeTypeEnum.ORG.getCode()));

            BusinessErrUtil.isTrue(orderUniqueBarCodeDTOList.stream().noneMatch(orderUniqueBarCodeDTO -> OrderProductInventoryStatusEnum.COMPLETE_INBOUND.getCode() == orderUniqueBarCodeDTO.getInventoryStatus()
                    || OrderProductInventoryStatusEnum.INBOUNDING.getCode() == orderUniqueBarCodeDTO.getInventoryStatus()
            ), ExecptionMessageEnum.PRODUCT_IN_OR_STORED);
        }


        // 校验完毕后设置缓存，防止过快操作两次
        cacheClient.controlRepeatOperation(OrderOperationConstant.BUYER_CENTER_ORDER_OP + orderId.toString(),3 * 60);

        // 1.解冻经费
        orderManageService.orderFundCardUnFreeze(orderMaster);

        // 2.采购金额统计
        applicationBaseService.updateApplyManageProductUsage(orderMaster, ApplyManageOperationEnum.CLOSE_ORDER.getValue(), null, null);

        // 3.更改订单状态
        Date date = new Date();
        OrderMasterDO updateItem = updateCancelOrder(orderId, OrderStatusEnum.Close.getValue(), cancelOrderReqDTO.getCancelMan(), cancelOrderReqDTO.getCancelManId(), date, date, cancelOrderReqDTO.getCancelReason());

        logger.info("取消线下单订单信息：{}", updateItem);
        cacheClient.removeCache(OrderOperationConstant.BUYER_CENTER_ORDER_OP + orderId);

        // 4.记录取消订单日志
        addCancelOrderLog(updateItem, OrderApprovalEnum.CANCEL.getValue(), updateItem.getFcancelreason() == null ? "采购人取消订单" : updateItem.getFcancelreason());

        // 5.取消订单后删除待结算单记录或删除入库单数据
        AsyncExecutor.listenableRunAsync(() -> {
            final int inventoryStatus = orderMaster.getInventoryStatus().intValue();
            if (InventoryStatusEnum.WAITING_FOR_REVIEW.getCode() == inventoryStatus || InventoryStatusEnum.FAILED.getCode() == inventoryStatus) {
                final List<BizWarehouseEntryDTO> entryByOrderNoList = bizWareHouseClient.findEntryByOrderNoList(New.list(orderMaster.getForderno()));
                entryByOrderNoList.forEach(entry -> bizWareHouseClient.invalidateEntryByEntryNo(entry.getEntryNo()));
            }
            if (OrderStatusEnum.WaitingForStatement_1.getValue().equals(status)) {
                statementPlatformClient.deleteWaitingStatementByOrderId(New.list(orderId));
            }
        }).addFailureCallback(throwable -> {
            logger.error("删除待结算单记录失败：" + throwable);
            Cat.logError(CAT_TYPE, "deleteWaitingStatementOrders", "删除待结算单记录失败：", throwable);
        });

        // 6.取消订单成功，更新一物一码状态
        this.updateBarcodeStatusWhenCancelComplete(orderMaster.getForderno());

        // 遵医定制,线下单取消后删除发票
        if (Objects.equals(OrgEnum.ZHUN_YI_YI_KE_DA_XUE_FU_SHU_YI_YUAN.getValue(), orderMaster.getFuserid())) {
            invoiceClient.deleteInvoiceByOrderIds(New.list(orderId));
        }

    }

    /**
     * 待发货订单 申请取消订单
     * @param orderMasterDO
     */
    private void applyCancelOrder(OrderMasterDO orderMasterDO, ApplyCancelOrderReqDTO applyCancelOrderReqDTO) {
        String cancelMan = applyCancelOrderReqDTO.getFcancelman();
        String cancelManId = applyCancelOrderReqDTO.getFcancelmanid();
        String  cancelReason = applyCancelOrderReqDTO.getFcancelreason();
        Integer status = applyCancelOrderReqDTO.getStatus();
        Integer opUserType = applyCancelOrderReqDTO.getOpUserType();

        Preconditions.notNull(cancelManId, "取消人id为空");
        Preconditions.notNull(orderMasterDO.getFbuyerid(), "购买者id为空");
        BusinessErrUtil.isTrue(orderMasterDO.getFbuyerid().toString().equals(cancelManId) || SYSTEM_OPERATOR_ID.equals(cancelManId), ExecptionMessageEnum.CANNOT_CANCEL_OTHERS_ORDER);
        cancelReason = OrderOperateLogConstant.BUYER_CANCEL_REASON_PREFIX + cancelReason;

        // 更新取消订单状态
        OrderMasterDO updateOrder = updateCancelOrder(orderMasterDO.getId(), status, cancelMan, cancelManId, new Date(), null, cancelReason);
        //记录申请取消日志
        addCancelOrderLog(updateOrder, OrderApprovalEnum.CANCEL.getValue(), cancelReason, opUserType);
        //发送邮件
        OrderMasterDO orderMaster = orderMasterMapper.selectByPrimaryKey(orderMasterDO.getId());
        //给供应商发邮件、微信消息
        orderMessageHandler.sendPurchaserApplyCancelEmailToSupplier(orderMaster);
        orderMessageHandler.cancelWaitingDeliveryWechatToSupp(orderMaster);
    }

    /**
     * 华农调用基理 取消订单
     * @param  orderMasterDO 订单Id
     * @param  cancelReason 取消原因
     * @return  取消是否成功 true 成功 false 失败
     */
    @Deprecated
    private void huaNongCancelOrder(OrderMasterDO orderMasterDO, String cancelReason){
        // 华农兼容基里逻辑，基里是推送采购单，需判断采购单生成时间作为旧单时间
        ApplicationMasterDTO applicationMasterDTO = applicationBaseClient.getApplicationMasterByApplyId(orderMasterDO.getFtbuyappid(), true);
        // 生成采购单时间晚于新对接事件，不推送基里
        if (applicationMasterDTO.getCreateTime().after(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, OrderDateConstant.HUA_NAN_NONG_YE_DA_XUE_OLD_DOCKING_TIME))) {
            return;
        }
        String prefixEx = "华农取消订单--";
        String message = "";

        // 采购单号
        String applyNumber = applicationMasterDTO.getApplyNumber();

        //获取华农用户信息
        UserBaseInfoDTO userInfo = userClient.getUserInfo(orderMasterDO.getFbuyerid(), orderMasterDO.getFuserid());
        BusinessErrUtil.notNull(userInfo, ExecptionMessageEnum.PURCHASER_INFO_NOT_FOUND);
        String jobnumber = userInfo.getJobnumber();
        Preconditions.notNull(jobnumber, "采购人工号信息为空，锐竞系统获取不到第三方人员信息！");
        List<DepartmentThirdPartyDTO> departmentThirdPartyDTOS = departmentRpcClient
                .findByUserIdAndOrgIdAndDepName(orderMasterDO.getFusercode(), jobnumber, orderMasterDO.getFbuydepartment());
        if (CollectionUtils.isEmpty(departmentThirdPartyDTOS)){
            message = "在锐竞系统中找不到对应的华农用户";
            orderOtherLogClient.createOrderDockingLog(orderMasterDO.getForderno(), OrgConst.HUA_NAN_NONG_YE_DA_XUE,
                    null, prefixEx+message + ",采购单号为:" + applyNumber,
                    "华农取消订单", OrderDockingResultEnum.FAIL.result);
            BusinessErrUtil.isTrue(false, ExecptionMessageEnum.NO_HUANONG_USER_IN_RUIJING);
        }
        DepartmentThirdPartyDTO huaNonInfo = departmentThirdPartyDTOS.get(0);

        // 根据采购单号查询对应的 DockingExtra
        DockingExtraDTO dockingExtra = new DockingExtraDTO();
        dockingExtra.setInfo(applyNumber);
        dockingExtra.setType(DockingTypeEnum.Purchase.getValue());
        List<DockingExtraDTO> dockingExtraList = dockingExtraService.findDockingExtra(dockingExtra);
        if (CollectionUtils.isEmpty(dockingExtraList)){
            message = "在锐竞系统中找不到对应订单信息";
            logger.info(message);
            orderOtherLogClient.createOrderDockingLog(orderMasterDO.getForderno(),
                    OrgConst.HUA_NAN_NONG_YE_DA_XUE,null, prefixEx+message + ",采购单号为:" + applyNumber,
                    "华农取消订单", OrderDockingResultEnum.FAIL.result);
            BusinessErrUtil.isTrue(false, ExecptionMessageEnum.NO_ORDER_INFO_IN_RUIJING);
        }
        dockingExtra = dockingExtraList.get(0);

        OrderReq hnOrderReq = new OrderReq();
        hnOrderReq.setExtraOrderNo(dockingExtra.getExtraInfo());//基里订单号
        hnOrderReq.setOrgCode(OrgConst.HUA_NAN_NONG_YE_DA_XUE);
        hnOrderReq.setUserId(huaNonInfo.getJobNumber());
        hnOrderReq.setGroupId(String.valueOf(huaNonInfo.getDepartmentId()));
        hnOrderReq.setReason(org.apache.commons.lang.StringUtils.isBlank(cancelReason)? " ":cancelReason);//订单取消原因
        hnOrderReq.setCtime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(applicationMasterDTO.getCreateTime()));
        /* 计算价格 */
        BigDecimal price = getPrice(applicationMasterDTO);
        hnOrderReq.setPrice(price.setScale(2,BigDecimal.ROUND_UP).toPlainString());

        try {
            huaNongServiceClient.cancelOrder(hnOrderReq,orderMasterDO.getForderno(),applyNumber);
        }catch (Exception ex){
            logger.error("华农取消订单失败原因：{}",ex);
            ex.printStackTrace();
            Cat.logError(ex);
            orderOtherLogClient.createOrderDockingLog(orderMasterDO.getForderno(),
                    OrgConst.HUA_NAN_NONG_YE_DA_XUE,null, prefixEx+"华农调用取消订单接口异常， 请看cat日志：" + ex.getMessage() + ",采购单号为:" + applyNumber,
                    "华农取消订单", OrderDockingResultEnum.FAIL.result);
            BusinessErrUtil.isTrue(false, ExecptionMessageEnum.HUANONG_CANCEL_ORDER_EXCEPTION, ex.getMessage());
        }
    }


    /**
     * 拒绝取消订单
     * @param cancelOrderReqDTO
     * @return
     */
    @Override
    public void refuseCancelOrder(CancelOrderReqDTO cancelOrderReqDTO) {
        Integer orderMasterId = cancelOrderReqDTO.getOrderMasterId();
        String refuseReason = cancelOrderReqDTO.getRefuseReason();
        //拒绝取消订单 原因前缀,用于区别是采购人拒绝取消，还是供应商拒绝取消
        Integer cancelManId = Integer.parseInt(cancelOrderReqDTO.getCancelManId());
        boolean supplierCancel = cancelManId.equals(PROMISE_SUPPLIER_FLAG);
        final String prefixReason = supplierCancel ? "供应商已拒绝取消订单，取消原因：" : "采购人已拒绝取消订单，取消原因：";
        String reason = prefixReason + refuseReason;
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderMasterId);
        Preconditions.notNull(orderMasterDO, "查询不到订单信息");
        Preconditions.notNull(orderMasterDO.getFbuyerid(), "购买者id为空");
        Preconditions.notNull(cancelOrderReqDTO.getCancelManId(), "拒绝取消者id为空");
        BusinessErrUtil.isTrue(PROMISE_SUPPLIER_FLAG.equals(cancelManId) || orderMasterDO.getFbuyerid().equals(cancelManId), ExecptionMessageEnum.CANNOT_REFUSE_CANCEL_OTHERS_ORDER);
        boolean supplierRefuseCancel = (PROMISE_SUPPLIER_FLAG.equals(cancelManId)) && OrderStatusEnum.PurchaseApplyToCancel.getValue().equals(orderMasterDO.getStatus());
        BusinessErrUtil.isTrue(supplierRefuseCancel || OrderStatusEnum.SupplierApplyToCancel.value.equals(orderMasterDO.getStatus()), ExecptionMessageEnum.ORDER_NOT_CANCEL_REQUEST_STATUS);
        // 暨大特殊逻辑处理
        if(Integer.valueOf(OrgEnum.JI_NAN_DA_XUE.getValue()).equals(orderMasterDO.getFuserid())){
            // 通过日志判断是否采购人在管理平台取消订单
            List<OrderApprovalLog> orderCancelLogList  = orderApprovalLogMapper.findByOrderIdInAndApproveStatusIn(
                    New.list(cancelOrderReqDTO.getOrderMasterId()), New.list(OrderApprovalEnum.CANCEL.getValue()));
            BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(orderCancelLogList), ExecptionMessageEnum.CANCEL_LOG_NOT_FOUND);
            OrderApprovalLog latestOrderCancelApprovalLog = orderCancelLogList
                    .stream()
                    .sorted(Comparator.comparing(OrderApprovalLog::getCreationTime).reversed())
                    .collect(Collectors.toList())
                    .get(0);
            BusinessErrUtil.isTrue(latestOrderCancelApprovalLog.getOpUserType() != OpUserTypeEnum.ORG_USER.getValue(),
                    ExecptionMessageEnum.PURCHASER_CANCEL_OPERATION_NOT_SUPPORT_REFUSE);
        }
        logger.info("拒绝取消订单 refuseCancelOrder 参数订单id{}，拒绝取消原因{}", orderMasterId, refuseReason);
        OrderMasterDO updateDO = new OrderMasterDO();
        //订单状态改为 待发货
        updateDO.setId(orderMasterId);
        // 根据是否有确认时间判断供应商发起取消前的状态，有则是已确认（待发货），无则是待确认
        Integer status = orderMasterDO.getFconfirmdate() != null ? OrderStatusEnum.WaitingForDelivery.getValue() : OrderStatusEnum.WaitingForConfirm.getValue();
        updateDO.setStatus(status);
        //拒绝取消订单原因
        updateDO.setFrefuseCancelReason(reason);
        updateDO.setFrefuseCancelDate(new Date());
        int result = orderMasterMapper.updateByPrimaryKeySelective(updateDO);
        Preconditions.isTrue(result>0, "更新条数为0");

        // 记录操作日志
        Integer approveStatus = Integer.parseInt(cancelOrderReqDTO.getCancelManId()) == (PROMISE_SUPPLIER_FLAG)? OrderApprovalEnum.SUPPLIER_REFUSE_CANCEL_ORDER.getValue() : OrderApprovalEnum.BUYER_REFUSE_CANCEL_ORDER.getValue();
        addOrderApprovalLog(orderMasterDO.getId(), null, reason, approveStatus, Integer.parseInt(cancelOrderReqDTO.getCancelManId()));
    }

    /**
     *
     * @param id 订单id
     * @param status 订单状态
     * @param fcancelman fcancelman
     * @param fcancelmanid 撤消人id
     * @param fcancelreason 撤销原因
     */
    private OrderMasterDO updateCancelOrder(Integer id, Integer status, String fcancelman, String fcancelmanid, Date fcanceldate, Date shutDownDate, String fcancelreason){
        OrderMasterDO updated = new OrderMasterDO();
        updated.setId(id);
        updated.setStatus(status);
        updated.setShutDownDate(shutDownDate);
        updated.setFcanceldate(fcanceldate);
        updated.setFcancelman(fcancelman);
        updated.setFcancelmanid(fcancelmanid);
        updated.setFcancelreason(fcancelreason);
        int result = orderMasterMapper.updateByPrimaryKeySelective(updated);
        Assert.isTrue(result>0, "订单更新条数为0");
        return updated;
    }

    /**
     * 计算价格
     */
    private BigDecimal getPrice(ApplicationMasterDTO applyMaster) {
        BigDecimal price = BigDecimal.ZERO;
        List<ApplicationDetailDTO> details = applyMaster.getDetails();
        for (ApplicationDetailDTO detail : details) {
            price = price.add(detail.getQuantity().multiply(detail.getBidPrice()));
        }
        return price;
    }

    /**
     * 记录 取消订单的 日志
     *
     * @param orderMasterDO
     */
    private void addCancelOrderLog(OrderMasterDO orderMasterDO, Integer orderApprovalEnumValue, String reason) {
        addCancelOrderLog(orderMasterDO, orderApprovalEnumValue, reason, null);
    }


    /**
     * 记录 取消订单的 日志
     *
     * @param orderMasterDO
     */
    private void addCancelOrderLog(OrderMasterDO orderMasterDO, Integer orderApprovalEnumValue, String reason, Integer opUserType) {
        OrderApprovalLog orderApprovalLog = new OrderApprovalLog();
        orderApprovalLog.setOrderId(orderMasterDO.getId());
        orderApprovalLog.setReason(reason);
        orderApprovalLog.setApproveStatus(orderApprovalEnumValue);
        orderApprovalLog.setOperatorId(Integer.valueOf(orderMasterDO.getFcancelmanid()));
        orderApprovalLog.setOpUserType(opUserType);
        orderApprovalLogMapper.insertSelective(orderApprovalLog);
    }

        /**
         * 恢复优惠券
         * @param orderMasterDO
         */
    private void recoveryCoupon(OrderMasterDO orderMasterDO) {
        if (orderMasterDO.getFtbuyappid() != null) {
            ApplyRefBusinessPriceQueryDTO example = new ApplyRefBusinessPriceQueryDTO();
            List<Integer> appIds = new ArrayList();
            appIds.add(orderMasterDO.getFtbuyappid());
            List<Integer> suppIds = new ArrayList();
            suppIds.add(orderMasterDO.getFsuppid());
            example.setQueryWay(ApplyRefBusinessPriceQueryDTO.QueryWayEnum.APP_SUPP_TYPE);
            example.setApplyIds(appIds);
            example.setSuppIds(suppIds);
            example.setBusinessType(BusinessType.Coupon.value);
            List<ApplyRefBusinessPriceDTO> applyRefBusinessPriceDTOS = purchaseApprovalLogClient.listApplyRefBusinessPrice(example);
            if (CollectionUtils.isNotEmpty(applyRefBusinessPriceDTOS)) {
                applyRefBusinessPriceDTOS.forEach(applyRefBusinessPriceDTO ->{
                    RefCouponPurchaserDO refCouponPurchaserDO = new RefCouponPurchaserDO();
                    refCouponPurchaserDO.setId(applyRefBusinessPriceDTO.getBusinessId());
                    refCouponPurchaserDO.setStatus(CouponStatusEnum.Resume.value);
                    refCouponPurchaserDOMapper.updateByPrimaryKeySelective(refCouponPurchaserDO);
                } );
            }
        }
    }

    /**
     * 取消订单内部逻辑
     * @param applyCancelOrderReqDTO 外部调用的参数
     * @param orderMasterDO 订单数据
     * @param callThirdPlatform 是否调用thunder/外部平台接口
     */
    private void handleCancelOrder(ApplyCancelOrderReqDTO applyCancelOrderReqDTO,OrderMasterDO orderMasterDO,boolean callThirdPlatform){
        Integer orderStatus = orderMasterDO.getStatus();
        try {
            // 避免重复操作
            cacheClient.controlRepeatOperation(OrderOperationConstant.BUYER_CENTER_ORDER_OP + orderMasterDO.getId().toString(),3 * 60);
            //判断订单的状态走对应的处理方法
            if (OrderStatusEnum.WaitingForConfirm.getValue().equals(orderStatus) ||
                    OrderStatusEnum.DeckingFail.getValue().equals(orderStatus)) {
                // 待确认/待对接方确认/对接失效
                this.cancelOrderForWaitingConfirm(orderMasterDO, applyCancelOrderReqDTO, callThirdPlatform);
            }
            else if (OrderStatusEnum.WaitingForDelivery.getValue().equals(orderStatus)) {
                BusinessErrUtil.notNull(applyCancelOrderReqDTO.getFcancelreason(), "取消原因不能为空");
                // 待发货
                this.applyCancelOrder(orderMasterDO, applyCancelOrderReqDTO);
                // 处理已注销的供应商
                this.handleCanceledSuppForCancelWhenWaitingDelivery(orderMasterDO, applyCancelOrderReqDTO.getFcancelman());
            }
            else if (OrderStatusEnum.SupplierApplyToCancel.value.equals(orderStatus)) {
                BusinessErrUtil.isTrue(false, ExecptionMessageEnum.SUPPLIER_APPLIED_CANCEL_ORDER);
            }
            else {
                BusinessErrUtil.isTrue(false, ExecptionMessageEnum.ONLY_UNCONFIRMED_OR_UNSHIPPED_CAN_CANCEL);
            }
        } catch (Exception e) {
            throw e;
        } finally {
            // 订单取消操作完成，删除缓存
            cacheClient.removeCache(OrderOperationConstant.BUYER_CENTER_ORDER_OP + orderMasterDO.getId().toString());
        }
    }

    /**
     * 处理待发货时取消下的供应商已注销的情况
     *
     * @param orderMasterDO 订单数据
     * @param cancelManName 申请取消人
     */
    private void handleCanceledSuppForCancelWhenWaitingDelivery(OrderMasterDO orderMasterDO, String cancelManName) {
        List<QualificationDTO> qualificationDTOList = suppClient.getQualificationList(New.list(orderMasterDO.getFsuppid()));
        if (CollectionUtils.isNotEmpty(qualificationDTOList)) {
            if (SuppQualificationUtils.CANCELED.test(qualificationDTOList.get(0))) {
                CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
                cancelOrderReqDTO.setCancelManId(PROMISE_SUPPLIER_FLAG.toString());
                cancelOrderReqDTO.setOrderMasterId(orderMasterDO.getId());
                cancelOrderReqDTO.setCancelMan(cancelManName);
                // 同意取消
                this.agreeCancelOrderCore(cancelOrderReqDTO);
            }
        } else {
            logger.error("获取供应商id为" + orderMasterDO.getFsuppid() + "的供应商资质失败");
        }
    }

    /**
     * 取消订单成功一物一码状态处理
     *
     * @param orderNo               订单号
     */
    private void updateBarcodeStatusWhenCancelComplete(String orderNo){
        AsyncExecutor.runAsync(() ->{
            List<OrderUniqueBarCodeDTO> barCodeDTOList = orderUniqueBarCodeRPCClient.findByOrderNo(orderNo, New.list(UniqueBarCodeTypeEnum.ORG.getCode()));
            List<String> notReturnBarcodeList = barCodeDTOList.stream().filter(item-> OrderProductTransactionStatusEnum.RETURNED.getCode() != item.getTransactionStatus()).map(OrderUniqueBarCodeDTO::getUniBarCode).collect(Collectors.toList());
            orderUniqueBarCodeRPCClient.updateStatusByBarcode(notReturnBarcodeList, OrderProductTransactionStatusEnum.CANCELED.getCode(), null);
        });
    }

    /**
     * 取消订单成功，解绑气瓶
     *
     * @param orderId 订单id
     * @param orderNo 订单号
     * @param orgId
     */
    private void unbindGasBottleWhenCancelComplete(Integer orderId, String orderNo, Integer orgId){
        AsyncExecutor.runAsync(()-> {
            gasBottleClient.unbindGasBottle(orderId, orderNo, orgId, ShopGasBottleUnbindEnum.ORDER_CANCEL);
        });
    }
}
