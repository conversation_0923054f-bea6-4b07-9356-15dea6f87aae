package com.ruijing.store.order.base.minor.service.impl;

import com.ruijing.store.enums.DangerousTagEnum;
import com.ruijing.store.order.base.minor.mapper.DangerousTagDOMapper;
import com.ruijing.store.order.base.minor.model.DangerousTagDO;
import com.ruijing.store.order.base.minor.service.DangerousTagDOService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @description: 获取危化品信息
 * @author: zhangzhifeng
 * @create: 2021/06/21 15:33
 **/
@Service
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
public class DangerousTagDOServiceImpl implements DangerousTagDOService {

    @Resource
    private DangerousTagDOMapper dangerousTagDOMapper;

    /**
     * 根据订单详情id查询危化品信息
     * @param orderDetailIdList   入参
     * @return
     */
    @Override
    public Map<String, DangerousTagDO> selectByOrderDetailIdIn(List<String> orderDetailIdList) {
        // 获取危化品标签
        List<DangerousTagDO> dangerousTagDOS = dangerousTagDOMapper.selectByBusinessIdInAndBusinessType(orderDetailIdList, DangerousTagEnum.ORDER_DETAIL.getValue());
        Map<String, DangerousTagDO> dangerousMap = Optional.ofNullable(dangerousTagDOS)
                .orElse(new ArrayList<>()).stream().collect(Collectors.toMap(
                DangerousTagDO::getBusinessId, Function.identity(), (oldValue, newValue) -> newValue));
        return dangerousMap;
    }
}
