package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.saturn.api.contract.constant.ContractOperationLogConstant;
import com.ruijing.order.saturn.api.contract.dto.ContractOperationLogDTO;
import com.ruijing.order.saturn.api.contract.dto.OrderContractDTO;
import com.ruijing.order.saturn.api.contract.dto.OrderContractQueryDTO;
import com.ruijing.order.saturn.api.contract.dto.OrderContractUpdateDTO;
import com.ruijing.order.saturn.api.contract.enums.ContractOperatorTypeEnum;
import com.ruijing.order.saturn.api.contract.service.OrderContractRPCService;
import com.ruijing.order.saturn.api.contract.vo.OrderContractInfoVO;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * Name: OrderContractClient
 * Description: 电子合同功能，我们生成电子合同并自动盖个章上去（没有法律效力，可能后续不会使用）
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2023/12/6
 */
@Component
public class OrderContractClient {

    @MSharpReference(remoteAppkey = "order-saturn-service")
    private OrderContractRPCService orderContractRPCService;


    /**
     * 新增订单合同
     *
     * @param orderContractDTO 订单合同新增入参
     * @param orgId            采购得单位id
     */
    @ServiceLog(description = "新增订单合同", serviceType = ServiceType.COMMON_SERVICE)
    public void newOrderContract(OrderContractDTO orderContractDTO,
                                 Integer orgId) {
        ContractOperationLogDTO buyerSignLogDTO = new ContractOperationLogDTO();
        buyerSignLogDTO.setOperatorId(Long.valueOf(orgId));
        buyerSignLogDTO.setOperatorName(orderContractDTO.getBuyerName());
        buyerSignLogDTO.setOperatorType(ContractOperatorTypeEnum.SYSTEM.getCode());
        buyerSignLogDTO.setContent(ContractOperationLogConstant.OPERATION_BUYER_SIGN);
        buyerSignLogDTO.setOperationTime(new Date());
        buyerSignLogDTO.setRemark(ContractOperationLogConstant.OPERATION_REMARK_MAP.get(ContractOperationLogConstant.OPERATION_BUYER_SIGN));
        orderContractDTO.setContractOperationLogDTO(buyerSignLogDTO);
        RemoteResponse<Boolean> response = orderContractRPCService.newOrderContract(orderContractDTO);
        Preconditions.isTrue(response.isSuccess(), "调用新增订单合同RPC接口失败");
        Preconditions.isTrue(response.getData(), "新增订单合同失败");
    }


    /**
     * 更新订单合同
     *
     * @param orderContractDTO 订单合同更新入参
     */
    @ServiceLog(description = "更新订单合同", serviceType = ServiceType.COMMON_SERVICE)
    public void updateOrderContract(OrderContractUpdateDTO orderContractDTO) {
        RemoteResponse<Boolean> response = orderContractRPCService.updateOrderContract(orderContractDTO);
        Preconditions.isTrue(response.isSuccess(), "调用更新订单合同RPC接口失败");
        Preconditions.isTrue(response.getData(), "更新订单合同失败");
    }


    /**
     * 批量查询订单合同简易信息
     *
     * @param queryDTO 订单合同查询入参
     * @return OrderContractInfoVO
     */
    @ServiceLog(description = "查询订单合同简易信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderContractInfoVO> getOrderContractInfo(OrderContractQueryDTO queryDTO) {
        RemoteResponse<List<OrderContractInfoVO>> response = orderContractRPCService.getOrderContractInfo(queryDTO);
        Preconditions.isTrue(response.isSuccess(),"更新订单合同信息失败-未找到订单合同！");
        return response.getData();
    }

    /**
     * 更新订单合同状态
     *
     * @param operation 操作
     * @param status    订单状态
     */
    @ServiceLog(description = "更新订单合同状态", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void updateOrderContractStatus(String operation,
                                          String orderNo,
                                          int status) {
        OrderContractQueryDTO queryDTO = new OrderContractQueryDTO();
        queryDTO.setOrderNoList(New.list(orderNo));
        OrderContractInfoVO orderContractInfo = this.getOrderContractInfo(queryDTO).get(0);
        this.updateOrderContract(generateUpdateDTO(operation, status, orderContractInfo));
    }


    /**
     * 批量更新订单合同状态
     *
     * @param operation 操作
     * @param status    订单状态
     */
    @ServiceLog(description = "批量更新订单合同状态", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void updateOrderContractStatusBatch(String operation,
                                               List<String> orderNoList,
                                               int status) {
        List<OrderContractUpdateDTO> updateDTOList = New.list();
        OrderContractQueryDTO queryDTO = new OrderContractQueryDTO();
        queryDTO.setOrderNoList(orderNoList);
        List<OrderContractInfoVO> contractInfoVOList = this.getOrderContractInfo(queryDTO);
        for (OrderContractInfoVO orderContractInfoVO : contractInfoVOList) {
            updateDTOList.add(generateUpdateDTO(operation, status, orderContractInfoVO));
        }
        RemoteResponse<Boolean> response = orderContractRPCService.updateOrderContractBatch(updateDTOList);
        Preconditions.isTrue(response.isSuccess(), "批量更新合同状态失败");
    }


    private OrderContractUpdateDTO generateUpdateDTO(String operation,int status, OrderContractInfoVO orderContractInfo){
        OrderContractUpdateDTO updateDTO = new OrderContractUpdateDTO();
        updateDTO.setId(orderContractInfo.getId());
        updateDTO.setContractNo(orderContractInfo.getContractNo());
        updateDTO.setContractStatus(status);
        ContractOperationLogDTO logDTO = new ContractOperationLogDTO();
        logDTO.setContractNo(orderContractInfo.getContractNo());
        logDTO.setOperatorId(-1L);
        logDTO.setOperatorName(ContractOperatorTypeEnum.SYSTEM.getName());
        logDTO.setOperatorType(ContractOperatorTypeEnum.SYSTEM.getCode());
        logDTO.setContent(operation);
        logDTO.setOperationTime(new Date());
        logDTO.setRemark(ContractOperationLogConstant.OPERATION_REMARK_MAP.get(operation));
        updateDTO.setContractOperationLogDTO(logDTO);
        return updateDTO;
    }
}
