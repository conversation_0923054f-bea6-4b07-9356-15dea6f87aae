package com.ruijing.store.order.gateway.buyercenter.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: chen<PERSON><PERSON><PERSON>g
 * @createTime: 2024-04-17 14:20
 * @description:
 **/
@RpcModel("订单管理-附件信息")
public class OrderAttachmentVO implements Serializable {

    private static final long serialVersionUID = -1626482037838147603L;

    @RpcModelProperty("文件路径")
    private String url;

    @RpcModelProperty("文件名称")
    private String fileName;


    public String getUrl() {
        return url;
    }

    public OrderAttachmentVO setUrl(String url) {
        this.url = url;
        return this;
    }

    public String getFileName() {
        return fileName;
    }

    public OrderAttachmentVO setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderAttachmentVO.class.getSimpleName() + "[", "]")
                .add("url='" + url + "'")
                .add("fileName='" + fileName + "'")
                .toString();
    }
}
