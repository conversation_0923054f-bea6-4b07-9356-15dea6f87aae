package com.ruijing.store.order.search.service;

import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.store.order.api.search.dto.OrderDateHistogramResultDTO;
import com.ruijing.store.order.api.search.dto.StatisticsManagerParamDTO;

import java.util.List;

/**
 * @author: liwenyu
 * @createTime: 2024-02-18 16:00
 * @description:
 **/
public interface OrderAggRelatedService {

    /**
     * 根据aggField和intervalDate进行聚合
     * @param paramDTO 聚合参数
     * @return 聚合结果
     */
    List<OrderDateHistogramResultDTO> aggAmountAndQuantityByEntitiesDateHistogram(StatisticsManagerParamDTO paramDTO);
}
