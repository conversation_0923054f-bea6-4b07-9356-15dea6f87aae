package com.ruijing.store.config.filter;

import com.ruijing.fundamental.remoting.msharp.provider.configure.GatewayFilterConfigurer;
import com.ruijing.fundamental.remoting.msharp.provider.filter.GateWayFilter;
import com.ruijing.fundamental.remoting.msharp.provider.filter.MappedGatewayFilter;
import com.ruijing.fundamental.remoting.msharp.provider.serivce.InterceptorRegistry;
import com.ruijing.order.filter.LogFilter;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * @author: liwenyu
 * @createTime: 2023-05-25 11:41
 * @description:
 **/
@Configuration
public class GateWayFilterConfigRegister implements GatewayFilterConfigurer {

    @Resource
    private LogFilter logFilter;

    @Override
    public void addGatewayFilter(InterceptorRegistry<GateWayFilter, MappedGatewayFilter> registry) {
        registry.addInterceptor(new GateWayFilterConfig()).addUrlPatterns("/**");
    }
}
