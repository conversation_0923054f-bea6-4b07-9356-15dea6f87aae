package com.ruijing.store.goodsreturn.enums;

/**
 * @description: 退货日志操作人类型
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/1/5 18:21
 **/
public enum GoodsReturnOperatorTypeEnum {
    PURCHASE(0, "采购人"),
    SUPPLIER(1, "供应商"),
    SYSTEM_USER(2, "系统用户"),
    ;

    /**
     * 编码
     */
    private Integer code;

    /**
     * 描述
     */
    private String description;

    GoodsReturnOperatorTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
