package com.ruijing.store.goodsreturn.vo;

import com.ruijing.fundamental.api.annotation.ModelProperty;
import com.ruijing.store.order.gateway.buyercenter.vo.other.GasBottleVO;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-05-06 09:52
 * @description:
 */
public class ApplyGoodsReturnOrderDetailVO implements Serializable {

    private static final long serialVersionUID = -212457531417154974L;

    @ModelProperty("商品id")
    private Integer detailId;

    @ModelProperty("绑定的气瓶--订单详情返回")
    private List<GasBottleVO> gasBottles;

    public Integer getDetailId() {
        return detailId;
    }

    public ApplyGoodsReturnOrderDetailVO setDetailId(Integer detailId) {
        this.detailId = detailId;
        return this;
    }

    public List<GasBottleVO> getGasBottles() {
        return gasBottles;
    }

    public ApplyGoodsReturnOrderDetailVO setGasBottles(List<GasBottleVO> gasBottles) {
        this.gasBottles = gasBottles;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ApplyGoodsReturnOrderDetailVO.class.getSimpleName() + "[", "]")
                .add("detailId=" + detailId)
                .add("gasBottles=" + gasBottles)
                .toString();
    }
}
