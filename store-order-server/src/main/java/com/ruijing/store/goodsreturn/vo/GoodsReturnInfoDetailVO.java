package com.ruijing.store.goodsreturn.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.goodsreturn.request.GoodsReturnBarcodeDataDTO;
import com.ruijing.store.order.gateway.buyercenter.vo.other.GasBottleVO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 退货单详情商品信息
 * @author: zhong<PERSON>le<PERSON>
 * @create: 2020/12/30 17:41
 **/
public class GoodsReturnInfoDetailVO implements Serializable {

    private static final long serialVersionUID = 2529989095580024080L;

    @RpcModelProperty("订单商品id")
    private String detailId;

    @RpcModelProperty("商品名称")
    private String goodsName;

    @RpcModelProperty("SPU-商品货号")
    private String goodsCode;

    @RpcModelProperty("规格")
    private String specification;

    @RpcModelProperty("商品图片路径")
    private String goodsPicturePath;

    @RpcModelProperty("危化品标签")
    private String dangerousTag;

    @RpcModelProperty("危化品标签ID")
    private Integer dangerousType;

    @RpcModelProperty("退货商品数量")
    private BigDecimal quantity;

    @RpcModelProperty("退货原因")
    private String returnReason;

    @RpcModelProperty("退货说明")
    private String remark;

    @RpcModelProperty("品牌")
    private String brand;

    @RpcModelProperty("金额")
    private BigDecimal amount;

    @RpcModelProperty("商品id")
    private String productId;

    @RpcModelProperty("商品单位")
    private String unit;

    @RpcModelProperty("商品单价")
    private BigDecimal price;

    @RpcModelProperty("寄回收货人")
    private String returnAcceptMan;

    @RpcModelProperty("寄回收货地址")
    private String returnAcceptAddr;

    @RpcModelProperty("寄回联系电话")
    private String returnAcceptPhone;

    @RpcModelProperty("一并退货的气瓶二维码，非一物一码模式才需要")
    private List<String> returnGasBottleBarcodes;

    @RpcModelProperty("一并退货的气瓶")
    private List<GasBottleVO> returnGasBottles;

    /**
     * 一物一码退货数据，仅有一物一码退货时赋值
     */
    private List<GoodsReturnBarcodeDataDTO> goodsReturnBarcodeDataDTOList;

    @RpcModelProperty("cas号")
    private String casNo;

    @RpcModelProperty("一级分类ID")
    private Integer firstCategoryId;

    @RpcModelProperty("商品平台唯一编码")
    private String productCode;

    @RpcModelProperty("包装规格")
    private String packingSpec;

    @RpcModelProperty("型号")
    private String modelNumber;

    @RpcModelProperty(value = "注册编码", description = "医疗器械注册证书编号")
    private String medicalDeviceRegisCertNumber;

    @RpcModelProperty("完成周期")
    private String completionCycle;

    @RpcModelProperty("出版社")
    private String press;

    @RpcModelProperty("纯度/浓度")
    private String purity;

    @RpcModelProperty("产品规格")
    private String productSpec;

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public GoodsReturnInfoDetailVO setProductCode(String productCode) {
        this.productCode = productCode;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getDangerousTag() {
        return dangerousTag;
    }

    public void setDangerousTag(String dangerousTag) {
        this.dangerousTag = dangerousTag;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public void setReturnReason(String returnReason) {
        this.returnReason = returnReason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getGoodsPicturePath() {
        return goodsPicturePath;
    }

    public void setGoodsPicturePath(String goodsPicturePath) {
        this.goodsPicturePath = goodsPicturePath;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getReturnAcceptMan() {
        return returnAcceptMan;
    }

    public GoodsReturnInfoDetailVO setReturnAcceptMan(String returnAcceptMan) {
        this.returnAcceptMan = returnAcceptMan;
        return this;
    }

    public String getReturnAcceptAddr() {
        return returnAcceptAddr;
    }

    public GoodsReturnInfoDetailVO setReturnAcceptAddr(String returnAcceptAddr) {
        this.returnAcceptAddr = returnAcceptAddr;
        return this;
    }

    public String getReturnAcceptPhone() {
        return returnAcceptPhone;
    }

    public GoodsReturnInfoDetailVO setReturnAcceptPhone(String returnAcceptPhone) {
        this.returnAcceptPhone = returnAcceptPhone;
        return this;
    }

    public List<String> getReturnGasBottleBarcodes() {
        return returnGasBottleBarcodes;
    }

    public GoodsReturnInfoDetailVO setReturnGasBottleBarcodes(List<String> returnGasBottleBarcodes) {
        this.returnGasBottleBarcodes = returnGasBottleBarcodes;
        return this;
    }

    public List<GasBottleVO> getReturnGasBottles() {
        return returnGasBottles;
    }

    public GoodsReturnInfoDetailVO setReturnGasBottles(List<GasBottleVO> returnGasBottles) {
        this.returnGasBottles = returnGasBottles;
        return this;
    }

    public List<GoodsReturnBarcodeDataDTO> getGoodsReturnBarcodeDataDTOList() {
        return goodsReturnBarcodeDataDTOList;
    }

    public GoodsReturnInfoDetailVO setGoodsReturnBarcodeDataDTOList(List<GoodsReturnBarcodeDataDTO> goodsReturnBarcodeDataDTOList) {
        this.goodsReturnBarcodeDataDTOList = goodsReturnBarcodeDataDTOList;
        return this;
    }

    public String getPackingSpec() {
        return packingSpec;
    }

    public GoodsReturnInfoDetailVO setPackingSpec(String packingSpec) {
        this.packingSpec = packingSpec;
        return this;
    }

    public String getModelNumber() {
        return modelNumber;
    }

    public GoodsReturnInfoDetailVO setModelNumber(String modelNumber) {
        this.modelNumber = modelNumber;
        return this;
    }

    public String getMedicalDeviceRegisCertNumber() {
        return medicalDeviceRegisCertNumber;
    }

    public GoodsReturnInfoDetailVO setMedicalDeviceRegisCertNumber(String medicalDeviceRegisCertNumber) {
        this.medicalDeviceRegisCertNumber = medicalDeviceRegisCertNumber;
        return this;
    }

    public String getCompletionCycle() {
        return completionCycle;
    }

    public GoodsReturnInfoDetailVO setCompletionCycle(String completionCycle) {
        this.completionCycle = completionCycle;
        return this;
    }

    public String getPress() {
        return press;
    }

    public GoodsReturnInfoDetailVO setPress(String press) {
        this.press = press;
        return this;
    }

    public String getPurity() {
        return purity;
    }

    public GoodsReturnInfoDetailVO setPurity(String purity) {
        this.purity = purity;
        return this;
    }

    public String getProductSpec() {
        return productSpec;
    }

    public GoodsReturnInfoDetailVO setProductSpec(String productSpec) {
        this.productSpec = productSpec;
        return this;
    }

    public Integer getDangerousType() {
        return dangerousType;
    }

    public GoodsReturnInfoDetailVO setDangerousType(Integer dangerousType) {
        this.dangerousType = dangerousType;
        return this;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public Integer getFirstCategoryId() {
        return firstCategoryId;
    }

    public void setFirstCategoryId(Integer firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
    }

    @Override
    public String toString() {
        return "GoodsReturnInfoDetailVO{" +
                "detailId='" + detailId + '\'' +
                ", goodsName='" + goodsName + '\'' +
                ", goodsCode='" + goodsCode + '\'' +
                ", specification='" + specification + '\'' +
                ", goodsPicturePath='" + goodsPicturePath + '\'' +
                ", dangerousTag='" + dangerousTag + '\'' +
                ", dangerousType=" + dangerousType +
                ", quantity=" + quantity +
                ", returnReason='" + returnReason + '\'' +
                ", remark='" + remark + '\'' +
                ", brand='" + brand + '\'' +
                ", amount=" + amount +
                ", productId='" + productId + '\'' +
                ", unit='" + unit + '\'' +
                ", price=" + price +
                ", returnAcceptMan='" + returnAcceptMan + '\'' +
                ", returnAcceptAddr='" + returnAcceptAddr + '\'' +
                ", returnAcceptPhone='" + returnAcceptPhone + '\'' +
                ", returnGasBottleBarcodes=" + returnGasBottleBarcodes +
                ", returnGasBottles=" + returnGasBottles +
                ", goodsReturnBarcodeDataDTOList=" + goodsReturnBarcodeDataDTOList +
                ", casNo='" + casNo + '\'' +
                ", firstCategoryId=" + firstCategoryId +
                ", productCode='" + productCode + '\'' +
                ", packingSpec='" + packingSpec + '\'' +
                ", modelNumber='" + modelNumber + '\'' +
                ", medicalDeviceRegisCertNumber='" + medicalDeviceRegisCertNumber + '\'' +
                ", completionCycle='" + completionCycle + '\'' +
                ", press='" + press + '\'' +
                ", purity='" + purity + '\'' +
                ", productSpec='" + productSpec + '\'' +
                '}';
    }
}
