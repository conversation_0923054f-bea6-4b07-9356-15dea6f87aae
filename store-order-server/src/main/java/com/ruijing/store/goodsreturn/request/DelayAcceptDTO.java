package com.ruijing.store.goodsreturn.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description：延迟验收DTO类
 * @date ：Created in 2022-06-13 11:16
 */
public class DelayAcceptDTO implements Serializable {

    @RpcModelProperty("退货id")
    private Integer returnId;

    public Integer getReturnId() {
        return returnId;
    }

    public DelayAcceptDTO setReturnId(Integer returnId) {
        this.returnId = returnId;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("DelayAcceptDTO{");
        sb.append("returnId=").append(returnId);
        sb.append('}');
        return sb.toString();
    }
}
