package com.ruijing.store.goodsreturn.request;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/7 11:21
 * @Version 1.0
 * @Description 供应商权限
 */
public class SupplierAuthorityDTO implements Serializable {

    private static final long serialVersionUID = 371101505083972144L;
    /**
     * <AUTHOR>
     * @Param
     * @return
     * @Description //登陆用户
     **/
    private Long userId;

    /**
     * <AUTHOR>
     * @Param
     * @return
     * @Description //登陆用户
     **/
    private String userName;

    /**
     * <AUTHOR>
     * @Param
     * @return
     * @Description //供应商id
     **/
    private Integer supplierId;

    /**
     * <AUTHOR>
     * @Param
     * @return
     * @Description //用户guid
     **/
    private String guid;

    /**
     * <AUTHOR>
     * @Param
     * @return
     * @Description //可操作采购单位
     **/
    private List<Integer> orgIds;

    /**
     * 是否超管
     */
    private boolean isSuperAdmin;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public List<Integer> getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(List<Integer> orgIds) {
        this.orgIds = orgIds;
    }

    public boolean isSuperAdmin() {
        return isSuperAdmin;
    }

    public SupplierAuthorityDTO setSuperAdmin(boolean superAdmin) {
        isSuperAdmin = superAdmin;
        return this;
    }
}
