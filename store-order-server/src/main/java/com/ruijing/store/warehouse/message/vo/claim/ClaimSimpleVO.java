package com.ruijing.store.warehouse.message.vo.claim;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.wms.api.enums.ReceiceStatus;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="申领单简要信息")
public class ClaimSimpleVO implements Serializable {

    private static final long serialVersionUID = 5214616601992785772L;

    @RpcModelProperty(value = "申领单Id")
    private Integer claimId;

    @RpcModelProperty(value = "申领单号")
    private String claimNo;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty(value = "申领人名字")
    private String claimUserName;

    @RpcModelProperty(value = "创建时间")
    private Long createTime;

    @RpcModelProperty(value = "库房名称")
    private String roomName;

    @RpcModelProperty(value = "库房Id")
    private Integer roomId;

    @RpcModelProperty(value = "部门名称")
    private String departmentName;

    @RpcModelProperty(value = "部门Id")
    private Integer departmentId;

    @RpcModelProperty(value = "申领状态", enumClass = ReceiceStatus.class)
    private Integer status;

    @RpcModelProperty(value = "申领状态名称")
    private String statusName;

    @RpcModelProperty(value = "审批状态（0审批中，1审批通过，2审批驳回）")
    private Integer approvalStatus;

    @RpcModelProperty(value = "审批状态名称")
    private String approvalStatusName;

    @RpcModelProperty(value = "申领单类型")
    private Integer claimType;

    @RpcModelProperty(value = "申领单类型名称")
    private String claimTypeName;
    
    @RpcModelProperty(value = "是否显示申领单打印按钮")
    private Boolean showPrintClaimApplication;

    public Integer getClaimId() {
        return claimId;
    }

    public void setClaimId(Integer claimId) {
        this.claimId = claimId;
    }

    public String getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(String claimNo) {
        this.claimNo = claimNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getClaimUserName() {
        return claimUserName;
    }

    public void setClaimUserName(String claimUserName) {
        this.claimUserName = claimUserName;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public Integer getClaimType() {
        return claimType;
    }

    public void setClaimType(Integer claimType) {
        this.claimType = claimType;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getApprovalStatusName() {
        return approvalStatusName;
    }

    public void setApprovalStatusName(String approvalStatusName) {
        this.approvalStatusName = approvalStatusName;
    }

    public String getClaimTypeName() {
        return claimTypeName;
    }

    public void setClaimTypeName(String claimTypeName) {
        this.claimTypeName = claimTypeName;
    }

    public Boolean getShowPrintClaimApplication() {
        return showPrintClaimApplication;
    }

    public void setShowPrintClaimApplication(Boolean showPrintClaimApplication) {
        this.showPrintClaimApplication = showPrintClaimApplication;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ClaimSimpleVO.class.getSimpleName() + "[", "]")
                .add("claimId=" + claimId)
                .add("claimNo='" + claimNo + "'")
                .add("orderNo='" + orderNo + "'")
                .add("claimUserName='" + claimUserName + "'")
                .add("createTime=" + createTime)
                .add("roomName='" + roomName + "'")
                .add("roomId=" + roomId)
                .add("departmentName='" + departmentName + "'")
                .add("departmentId=" + departmentId)
                .add("status=" + status)
                .add("statusName='" + statusName + "'")
                .add("approvalStatus=" + approvalStatus)
                .add("approvalStatusName='" + approvalStatusName + "'")
                .add("claimType=" + claimType)
                .add("claimTypeName='" + claimTypeName + "'")
                .add("showPrintClaimApplication=" + showPrintClaimApplication)
                .toString();
    }
}
