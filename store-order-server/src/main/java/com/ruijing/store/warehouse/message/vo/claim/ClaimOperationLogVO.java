package com.ruijing.store.warehouse.message.vo.claim;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="申领单操作日志")
public class ClaimOperationLogVO implements Serializable {

    private static final long serialVersionUID = 8200543378867526521L;

    @RpcModelProperty(value = "操作人")
    private String operator;

    @RpcModelProperty(value = "操作内容")
    private String operation;

    @RpcModelProperty(value = "操作时间")
    private Long operationTime;

    @RpcModelProperty(value = "审批意见")
    private String approvalOpinion;

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Long getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Long operationTime) {
        this.operationTime = operationTime;
    }

    public String getApprovalOpinion() {
        return approvalOpinion;
    }

    public void setApprovalOpinion(String approvalOpinion) {
        this.approvalOpinion = approvalOpinion;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("WarehouseOperationLogVO{");
        sb.append("operator='").append(operator).append('\'');
        sb.append(", operation='").append(operation).append('\'');
        sb.append(", operationTime=").append(operationTime);
        sb.append(", approvalOpinion='").append(approvalOpinion).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
