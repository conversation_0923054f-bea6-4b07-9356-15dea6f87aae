package com.ruijing.store.warehouse.message.bean;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/5 10:54
 */
public class WarehouseApplicationBean {
    /**
     * 入库单Id
     */
    private Integer warehouseApplicationId;

    /**
     * 入库单号
     */
    private String warehouseApplicationNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 入库单号对应条形码
     */
    private String entryNoBarcode;

    /**
     * 申请时间
     */
    private Date warehouseApplicationDate;

    /**
     * 申请人
     */
    private String warehouseApplicant;

    /**
     * 库房Id
     */
    private Integer warehouseId;

    /**
     * 库房名称
     */
    private String warehouseName;

    /**
     * 入库申请页面，用户填写的备注内容，即入库单的备注字段
     */
    private String remark;

    /**
     * 审批状态（0审批中，1审批通过，2审批驳回）
     */
    private Integer approvalStatus;

    /**
     * 审批状态名称（待审批、审核通过、审核驳回）
     */
    private String approvalStatusName;

    /**
     * 入库单状态（0：未入库 1：已入库）
     */
    private  Integer status;

    /**
     * 申请入库商品状态名称（未入库、已入库）
     */
    private String statusName;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 入库单所有商品的总额的合计
     */
    private double totalPrice;

    /**
     * 入库日期，时间戳格式
     */
    private Date inWarehouseDate;

    /**
     * 入库审核人姓名
     */
    private String approverName;

    /**
     * 入库审核时间
     */
    private String approvalTimeString;

    /**
     * 入库申请图片
     */
    private List<String> inWarehousePictureUrlList;

    /**
     * 入库申请单关联商品信息列表
     */
    private List<ProductBean> productBeans;

    /**
     * 审批进度列表
     */
    private List<ApprovalProgressBean> approvalProgressBeans;

    /**
     * 操作日志
     */
    private List<WarehouseOperationLogBean> operationLogBeans;

    /**
     * 入库单对应的即入即出的出库单
     */
    private OutWarehouseApplicationBean immediatelyOutWarehouseApplicationBean;

    /**
     * 入库方式
     */
    private Integer businessType;

    /**
     * 是否重新提交
     */
    private boolean recommit;

    /**
     * 补充库房-制单人
     */
    private String docCreator;

    /**
     * 补充库房-领料部门
     */
    private String pickingDept;

    /**
     * 补充库房-领用仓库
     */
    private String receiveWarehouse;

    /**
     * 第一归还人
     */
    private String firstReverter;

    /**
     * 第二归还人
     */
    private String secondReverter;

    public String getFirstReverter() {
        return firstReverter;
    }

    public WarehouseApplicationBean setFirstReverter(String firstReverter) {
        this.firstReverter = firstReverter;
        return this;
    }

    public String getSecondReverter() {
        return secondReverter;
    }

    public WarehouseApplicationBean setSecondReverter(String secondReverter) {
        this.secondReverter = secondReverter;
        return this;
    }

    public Integer getWarehouseApplicationId() {
        return warehouseApplicationId;
    }

    public void setWarehouseApplicationId(Integer warehouseApplicationId) {
        this.warehouseApplicationId = warehouseApplicationId;
    }

    public String getWarehouseApplicationNo() {
        return warehouseApplicationNo;
    }

    public void setWarehouseApplicationNo(String warehouseApplicationNo) {
        this.warehouseApplicationNo = warehouseApplicationNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getEntryNoBarcode() {
        return entryNoBarcode;
    }

    public void setEntryNoBarcode(String entryNoBarcode) {
        this.entryNoBarcode = entryNoBarcode;
    }

    public Date getWarehouseApplicationDate() {
        return warehouseApplicationDate;
    }

    public void setWarehouseApplicationDate(Date warehouseApplicationDate) {
        this.warehouseApplicationDate = warehouseApplicationDate;
    }

    public String getWarehouseApplicant() {
        return warehouseApplicant;
    }

    public void setWarehouseApplicant(String warehouseApplicant) {
        this.warehouseApplicant = warehouseApplicant;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getApprovalStatusName() {
        return approvalStatusName;
    }

    public void setApprovalStatusName(String approvalStatusName) {
        this.approvalStatusName = approvalStatusName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Date getInWarehouseDate() {
        return inWarehouseDate;
    }

    public void setInWarehouseDate(Date inWarehouseDate) {
        this.inWarehouseDate = inWarehouseDate;
    }

    public String getApproverName() {
        return approverName;
    }

    public void setApproverName(String approverName) {
        this.approverName = approverName;
    }

    public String getApprovalTimeString() {
        return approvalTimeString;
    }

    public void setApprovalTimeString(String approvalTimeString) {
        this.approvalTimeString = approvalTimeString;
    }

    public List<String> getInWarehousePictureUrlList() {
        return inWarehousePictureUrlList;
    }

    public void setInWarehousePictureUrlList(List<String> inWarehousePictureUrlList) {
        this.inWarehousePictureUrlList = inWarehousePictureUrlList;
    }

    public List<ProductBean> getProductBeans() {
        return productBeans;
    }

    public void setProductBeans(List<ProductBean> productBeans) {
        this.productBeans = productBeans;
    }

    public List<ApprovalProgressBean> getApprovalProgressBeans() {
        return approvalProgressBeans;
    }

    public void setApprovalProgressBeans(List<ApprovalProgressBean> approvalProgressBeans) {
        this.approvalProgressBeans = approvalProgressBeans;
    }

    public List<WarehouseOperationLogBean> getOperationLogBeans() {
        return operationLogBeans;
    }

    public void setOperationLogBeans(List<WarehouseOperationLogBean> operationLogBeans) {
        this.operationLogBeans = operationLogBeans;
    }

    public OutWarehouseApplicationBean getImmediatelyOutWarehouseApplicationBean() {
        return immediatelyOutWarehouseApplicationBean;
    }

    public void setImmediatelyOutWarehouseApplicationBean(OutWarehouseApplicationBean immediatelyOutWarehouseApplicationBean) {
        this.immediatelyOutWarehouseApplicationBean = immediatelyOutWarehouseApplicationBean;
    }

    public double getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(double totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public WarehouseApplicationBean setBusinessType(Integer businessType) {
        this.businessType = businessType;
        return this;
    }

    public boolean getRecommit() {
        return recommit;
    }

    public void setRecommit(boolean recommit) {
        this.recommit = recommit;
    }

    public String getDocCreator() {
        return docCreator;
    }

    public void setDocCreator(String docCreator) {
        this.docCreator = docCreator;
    }

    public String getPickingDept() {
        return pickingDept;
    }

    public void setPickingDept(String pickingDept) {
        this.pickingDept = pickingDept;
    }

    public String getReceiveWarehouse() {
        return receiveWarehouse;
    }

    public void setReceiveWarehouse(String receiveWarehouse) {
        this.receiveWarehouse = receiveWarehouse;
    }

    @Override
    public String toString() {
        return "WarehouseApplicationBean{" +
                "warehouseApplicationId=" + warehouseApplicationId +
                ", warehouseApplicationNo='" + warehouseApplicationNo + '\'' +
                ", orderNo='" + orderNo + '\'' +
                ", entryNoBarcode='" + entryNoBarcode + '\'' +
                ", warehouseApplicationDate=" + warehouseApplicationDate +
                ", warehouseApplicant='" + warehouseApplicant + '\'' +
                ", warehouseId=" + warehouseId +
                ", warehouseName='" + warehouseName + '\'' +
                ", remark='" + remark + '\'' +
                ", approvalStatus=" + approvalStatus +
                ", approvalStatusName='" + approvalStatusName + '\'' +
                ", status=" + status +
                ", statusName='" + statusName + '\'' +
                ", departmentName='" + departmentName + '\'' +
                ", totalPrice=" + totalPrice +
                ", inWarehouseDate=" + inWarehouseDate +
                ", approverName='" + approverName + '\'' +
                ", approvalTimeString='" + approvalTimeString + '\'' +
                ", inWarehousePictureUrlList=" + inWarehousePictureUrlList +
                ", productBeans=" + productBeans +
                ", approvalProgressBeans=" + approvalProgressBeans +
                ", operationLogBeans=" + operationLogBeans +
                ", immediatelyOutWarehouseApplicationBean=" + immediatelyOutWarehouseApplicationBean +
                ", businessType=" + businessType +
                ", recommit=" + recommit +
                ", docCreator='" + docCreator + '\'' +
                ", pickingDept='" + pickingDept + '\'' +
                ", receiveWarehouse='" + receiveWarehouse + '\'' +
                ", firstReverter='" + firstReverter + '\'' +
                ", secondReverter='" + secondReverter + '\'' +
                '}';
    }
}
