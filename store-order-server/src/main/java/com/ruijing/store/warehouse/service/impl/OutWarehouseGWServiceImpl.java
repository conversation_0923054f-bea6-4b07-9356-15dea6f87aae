package com.ruijing.store.warehouse.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.reagent.bid.api.rpc.dto.BidApprovalLogDTO;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.shop.category.api.dto.CategoryDTO;
import com.ruijing.store.approval.api.dto.PurchaseApprovalLogDTO;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.general.dto.FundCardSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.OrderMasterTranslator;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.util.BarCodeUtils;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.warehouse.message.bean.OrderBean;
import com.ruijing.store.warehouse.message.bean.OutWarehouseApplicationBean;
import com.ruijing.store.warehouse.message.bean.ProductBean;
import com.ruijing.store.warehouse.message.constant.WarehouseConstant;
import com.ruijing.store.warehouse.message.vo.outwarehouse.OutWarehouseApplicationDetailVO;
import com.ruijing.store.warehouse.message.vo.outwarehouse.OutWarehouseDetailRequestVO;
import com.ruijing.store.warehouse.service.OutWarehouseGWService;
import com.ruijing.store.warehouse.utils.CategoryUtil;
import com.ruijing.store.warehouse.utils.PriceUtil;
import com.ruijing.store.warehouse.utils.translator.OrderBeanTranslator;
import com.ruijing.store.warehouse.utils.translator.OutWarehouseApplicationBeanTranslator;
import com.ruijing.store.warehouse.utils.translator.ProductBeanTranslator;
import com.ruijing.store.wms.api.dto.BizWarehouseExitDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseExitDetailDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/1/6 17:54
 */
@Service
public class OutWarehouseGWServiceImpl implements OutWarehouseGWService {

    private static final String CAT_TYPE = "OutWarehouseGWServiceImpl";

    /**
     * 需要打印出库单条形码的单位code
     */
    private static final List<String> ORG_CODE_NEED_Exit_NO_BARCODE = Lists.newArrayList(OrgEnum.GUANG_DONG_YAO_KE_DA_XUE.getCode());

    @Resource
    private BizExitServiceClient bizExitServiceClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @Resource
    private UserClient userClient;

    @Resource
    private CategoryServiceClient categoryServiceClient;

    @Resource
    private BidClient bidClient;

    @Override
    public OutWarehouseApplicationDetailVO getOutWarehouseApplicationDetail(OutWarehouseDetailRequestVO request) {
        Preconditions.notNull(request.getOutWarehouseApplicationId(), "出库申请单Id不能为空");
        //根据出库单id查找出库单信息
        BizWarehouseExitDTO outWarehouseApplicationInfo = this.getNotNullOutWarehouseApplicationById(request.getOutWarehouseApplicationId());
        BusinessErrUtil.notEmpty(outWarehouseApplicationInfo.getExitDetailDTOList(), ExecptionMessageEnum.OUTBOUND_ORDER_DETAILS_NOT_FOUND, request.getOutWarehouseApplicationId());
        //根据出库单的订单编号查订单(含验收图片但不含订单详情列表)
        OrderMasterDTO orderMasterDTO = this.getNotNullOrderMasterByOrderNo(outWarehouseApplicationInfo.getOrderNo());
        //根据订单Id查订单（含订单详情列表但不含验收图片）
        OrderMasterSearchDTO orderMasterSearchDTO = this.getNotNullSearchOrder(orderMasterDTO.getId());
        BusinessErrUtil.notEmpty(orderMasterSearchDTO.getOrderDetail(), ExecptionMessageEnum.ORDER_DETAILS_NOT_FOUND_FOR_ORDER_ID, orderMasterDTO.getForderno());

        //出库单信息
        OutWarehouseApplicationBean outWarehouseApplicationBean = this.getImmediatelyOutWarehouseApplicationBean(outWarehouseApplicationInfo, orderMasterSearchDTO.getOrderDetail(), orderMasterDTO.getFusercode());
        //订单信息
        OrderBean orderBean = this.getOrderBean(orderMasterDTO);
        //获取订单对应的经费卡项目
        if (CollectionUtils.isNotEmpty(orderMasterSearchDTO.getCard())) {
            this.populateFundCard(orderBean, orderMasterSearchDTO.getCard(), orderMasterDTO.getFusercode());
        }
        return this.getImmediatelyOutWarehouseApplicationDetail(outWarehouseApplicationBean, orderBean);
    }

    @Override
    public OutWarehouseApplicationDetailVO getImmediatelyOutWarehouseApplicationDetail(OutWarehouseApplicationBean outWarehouseApplicationBean, OrderBean orderBean) {
        OutWarehouseApplicationDetailVO receiver = new OutWarehouseApplicationDetailVO();
        receiver.setOrderNo(orderBean.getOrderNo());
        receiver.setOrderId(orderBean.getOrderId());
        receiver.setPurchaserName(orderBean.getPurchaserName());
        receiver.setOrderSpecies(orderBean.getSpecies());
        //根据入库单的订单编号查订单的验收图片、机构名称、供应商编码、供应商名称、采购人联系方式、
        receiver.setOrgName(orderBean.getOrgName());
        receiver.setSupplierCode(orderBean.getSupplierCode());
        receiver.setSupplierName(orderBean.getSupplierName());
        receiver.setAcceptor(orderBean.getAcceptor());
        receiver.setPurchaserPhone(orderBean.getPurchaserPhone());
        //获取入库单对应的订单的经费项目的项目编码、项目名称、经费卡号
        receiver.setProjectCode(orderBean.getProjectCode());
        receiver.setProjectName(orderBean.getProjectName());
        receiver.setFunCardNo(orderBean.getFunCardNo());
        //根据订单对应的采购申请单或竞价单获取二级审批人(别名科长)
        receiver.setSectionChief(orderBean.getSectionChief());
        //根据订单对应部门获取部门负责人&上一级部门
        receiver.setDepartmentDirector(orderBean.getDepartmentDirector());
        receiver.setDepartmentParentName(orderBean.getDepartmentParentName());
        //获取订单号对应的条形码
        receiver.setOrderNoBarcode(orderBean.getOrderNoBarcode());
        //获取订单对应的发票信息
        receiver.setInvoiceNo(orderBean.getInvoiceNo());
        receiver.setInvoiceNoList(orderBean.getInvoiceNoList());
        receiver.setInvoiceDateTimeList(orderBean.getInvoiceDateTimeList());
        //封装入库单信息
        receiver.setOutWarehouseApplicationNo(outWarehouseApplicationBean.getOutWarehouseApplicationNo());
        receiver.setOutWarehouseApplicationId(outWarehouseApplicationBean.getOutWarehouseApplicationId());
        receiver.setDepartmentName(outWarehouseApplicationBean.getDepartmentName());
        receiver.setStatus(outWarehouseApplicationBean.getStatus());
        receiver.setStatusName(outWarehouseApplicationBean.getStatusName());
        receiver.setOutWarehouseApplicant(outWarehouseApplicationBean.getOutWarehouseApplicant());
        receiver.setOutWarehouseApplicationTime(outWarehouseApplicationBean.getOutWarehouseApplicationDate() == null ? null : outWarehouseApplicationBean.getOutWarehouseApplicationDate().getTime());
        receiver.setWarehouseId(outWarehouseApplicationBean.getWarehouseId());
        receiver.setWarehouseName(outWarehouseApplicationBean.getWarehouseName());
        receiver.setOutWarehouseTime(outWarehouseApplicationBean.getOutWarehouseDate() == null ? null : outWarehouseApplicationBean.getOutWarehouseDate().getTime());
        //获取入库单号对应条形码
        receiver.setExitNoBarcode(outWarehouseApplicationBean.getExitNoBarcode());
        //提取申请单相关商品信息
        receiver.setWarehouseProductInfoVOList(outWarehouseApplicationBean.getProductBeans().stream().map(ProductBeanTranslator::productBean2WarehouseProductInfoVO).collect(Collectors.toList()));
        receiver.setTotalPrice(PriceUtil.formatDouble2TwoDecimal(outWarehouseApplicationBean.getTotalPrice()));
        receiver.setTotalPriceInChinese(PriceUtil.convert(String.valueOf(outWarehouseApplicationBean.getTotalPrice())));
        return receiver;
    }

    @Override
    public OutWarehouseApplicationBean getImmediatelyOutWarehouseApplicationBean(BizWarehouseExitDTO outWarehouseApplicationInfo, List<OrderDetailSearchDTO> orderDetailSearchDTOList, String orgCode) {
        //出库单信息
        OutWarehouseApplicationBean outWarehouseApplicationBean = OutWarehouseApplicationBeanTranslator.bizWarehouseExitDTO2WarehouseApplicationBean(outWarehouseApplicationInfo);
        //获取出库单号对应的条形码(个性化)
        String exitNoBarcode = this.getPersonalizedBarcodeByExitNo(orgCode, outWarehouseApplicationInfo.getExitNo());
        outWarehouseApplicationBean.setExitNoBarcode(exitNoBarcode);

        //商品信息
        List<ProductBean> productBeans = this.getProductBeans(outWarehouseApplicationInfo.getExitDetailDTOList(), orderDetailSearchDTOList, orgCode);
        //设置总价
        this.populateOutWarehouseApplicationPrice(outWarehouseApplicationBean, productBeans);
        outWarehouseApplicationBean.setProductBeans(productBeans);
        return outWarehouseApplicationBean;
    }

    /**
     * @param outWarehouseApplicationInfo
     * @param orgCode
     * @return com.ruijing.store.warehouse.message.bean.OutWarehouseApplicationBean
     * @description: 获取出库单中间存储对象，即入即出用，且用于通用打印接口getCommonPrintData中
     * @date: 2021/3/16 19:00
     * @author: zengyanru
     */
    @Override
    public OutWarehouseApplicationBean getImmediatelyOutWarehouseApplicationBean(BizWarehouseExitDTO outWarehouseApplicationInfo, String orgCode) {
        //出库单信息
        OutWarehouseApplicationBean outWarehouseApplicationBean = OutWarehouseApplicationBeanTranslator.bizWarehouseExitDTO2WarehouseApplicationBean(outWarehouseApplicationInfo);
        //获取出库单号对应的条形码(个性化)
        String exitNoBarcode = this.getPersonalizedBarcodeByExitNo(orgCode, outWarehouseApplicationInfo.getExitNo());
        outWarehouseApplicationBean.setExitNoBarcode(exitNoBarcode);
        return outWarehouseApplicationBean;
    }

    private void populateFundCard(OrderBean receiver, List<FundCardSearchDTO> fundCardSearchDTOList, String orgCode) {
        if(CollectionUtils.isEmpty(fundCardSearchDTOList)){
            receiver.setFunCardNo(StringUtils.EMPTY);
            receiver.setProjectCode(StringUtils.EMPTY);
            receiver.setProjectName(StringUtils.EMPTY);
            return;
        }
        List<String> cardIds = fundCardSearchDTOList.stream().map(FundCardSearchDTO::getFundCardId).collect(Collectors.toList());
        List<FundCardDTO> currentLevelCards = researchFundCardServiceClient.findCurrentCardByOrgCodeAndCardId(orgCode, cardIds);
        String fundCardNoStr = currentLevelCards.stream()
                .map(item->item.getSecondLevelCode() != null ? item.getSecondLevelCode() : item.getFirstLevelCode())
                .collect(Collectors.joining(WarehouseConstant.ORDER_PROJECT_NAME_OR_CODE_OR_FUND_CARD_NO_SEPARATOR));
        receiver.setFunCardNo(fundCardNoStr);

        List<FundCardDTO> allLevelCards = this.getNotNullFundCardList(orgCode, cardIds);
        Set<String> projectNameSet = allLevelCards.stream().map(FundCardDTO::getName).collect(Collectors.toSet());
        receiver.setProjectName(String.join(WarehouseConstant.ORDER_PROJECT_NAME_OR_CODE_OR_FUND_CARD_NO_SEPARATOR, projectNameSet));
        Set<String> projectCodeSet = allLevelCards.stream().map(FundCardDTO::getCode).collect(Collectors.toSet());
        receiver.setProjectCode(String.join(WarehouseConstant.ORDER_PROJECT_NAME_OR_CODE_OR_FUND_CARD_NO_SEPARATOR, projectCodeSet));
    }

    private OrderBean getOrderBean(OrderMasterDTO orderMasterDTO) {
        //订单信息
        OrderBean orderBean = OrderBeanTranslator.orderMasterDTO2BaseOrderBean(orderMasterDTO);
        //科长
        String sectionChief = this.getSectionChief(orderMasterDTO);
        orderBean.setSectionChief(sectionChief);
        //部门负责人
        BusinessErrUtil.notNull(orderMasterDTO.getFbuydepartmentid(), ExecptionMessageEnum.DEPARTMENT_NOT_FOUND_FOR_ORDER, orderMasterDTO.getForderno());
        orderBean.setDepartmentDirector(this.getDepartmentDirectorName(orderMasterDTO.getFbuydepartmentid()));
        //获取订单对应的条形码（个性化）
        String orderNoBarcode = this.getPersonalizedBarcodeByOrderNo(orderMasterDTO.getFusercode(), orderMasterDTO.getForderno());
        orderBean.setOrderNoBarcode(orderNoBarcode);
        //找采购人信息
        UserBaseInfoDTO purchaser = userClient.getNotNullUserDetailById(orderMasterDTO.getFbuyerid());
        this.populatePurchaser(orderBean, purchaser);
        return orderBean;
    }

    private List<ProductBean> getProductBeans(List<BizWarehouseExitDetailDTO> exitDetails, List<OrderDetailSearchDTO> orderDetails, String orgCode) {
        Map<String, OrderDetailSearchDTO> goodCodeAndOrderDetailMap = orderDetails.stream().collect(Collectors.toMap(o -> o.getFgoodcode().trim(), Function.identity(), (o,n) -> n));
        Map<Integer, OrderDetailSearchDTO> detailIdAndOrderDetailMap = orderDetails.stream().collect(Collectors.toMap(o -> o.getDetailId(), Function.identity(), (o, n) -> n));
        List<ProductBean> productBeans = new ArrayList<>();
        for (BizWarehouseExitDetailDTO exitDetail : exitDetails) {
            Preconditions.notNull(exitDetail.getProductCode(), "出库商品的货号为空：" + exitDetail.getId());
            // 兼容匹配 order detail id 和 货号
            OrderDetailSearchDTO orderDetailSearchDTO;
            if (exitDetail.getOrderDetailId() != null) {
                orderDetailSearchDTO = detailIdAndOrderDetailMap.get(exitDetail.getOrderDetailId());
            } else {
                orderDetailSearchDTO = goodCodeAndOrderDetailMap.get(exitDetail.getProductCode().trim());
            }
            BusinessErrUtil.notNull(orderDetailSearchDTO, ExecptionMessageEnum.INBOUND_ORDER_DETAILS_NOT_FOUND, exitDetail.getProductCode().trim(), exitDetail.getOrderDetailId());
            ProductBean productBean = ProductBeanTranslator.bizWarehouseExitDetailDTO2ProductBean(exitDetail);
            this.populateProductPrice(productBean, exitDetail, orderDetailSearchDTO.getFbidprice());
            productBean.setProductId(orderDetailSearchDTO.getProductId());
            productBean.setCategoryId(orderDetailSearchDTO.getCategoryId());
            productBean.setFirstLevelCategoryId(orderDetailSearchDTO.getFirstCategoryId());
            productBean.setFirstLevelCategoryName(orderDetailSearchDTO.getFirstCategoryName());
            productBean.setOrderDetailId(orderDetailSearchDTO.getDetailId());
            productBeans.add(productBean);
        }
        //获取商品一级分类(个性化)
        if (this.needFirstLevelCategory(orgCode)) {
            this.selfPopulateFirstLevelCategory(productBeans);
        }
        return productBeans;
    }

    private BizWarehouseExitDTO getNotNullOutWarehouseApplicationById(Integer outWarehouseApplicationId) {
        //根据入库单号查找入库单信息
        BizWarehouseExitDTO bizWarehouseExitDTO = bizExitServiceClient.queryExitById(outWarehouseApplicationId);
        Preconditions.notNull(bizWarehouseExitDTO, "找不到出库单:" + outWarehouseApplicationId);
        return bizWarehouseExitDTO;
    }

    private OrderMasterDTO getNotNullOrderMasterByOrderNo(String orderNo) {
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(orderNo);
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.ORDER_INFO_NOT_FOUND_FOR_ORDER_NO, orderNo);
        return OrderMasterTranslator.orderMasterDO2OrderMasterDTO(orderMasterDO);
    }

    private OrderMasterSearchDTO getNotNullSearchOrder(Integer orderId) {
        List<OrderMasterSearchDTO> orderMasterSearchDTOList = orderSearchBoostService.searchOrderById(orderId);
        BusinessErrUtil.notEmpty(orderMasterSearchDTOList, ExecptionMessageEnum.ORDER_INFO_NOT_FOUND_FOR_ORDER_ID, orderId);
        return orderMasterSearchDTOList.get(0);
    }

    private List<FundCardDTO> getNotNullFundCardList(String orgCode, List<String> cardIdList) {
        List<FundCardDTO> fundCardDTOList = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(orgCode, cardIdList);
        BusinessErrUtil.notEmpty(fundCardDTOList, ExecptionMessageEnum.FUNDING_CARD_NOT_FOUND, JsonUtils.toJson(cardIdList));
        return fundCardDTOList;
    }

    private String getPurchaseApprover(int approvalLevel, Integer purchaseApplicationId) {
        List<PurchaseApprovalLogDTO> purchaseApprovalLogDTOList = purchaseApprovalLogClient.getApprovalLogByIdList(Sets.newHashSet(purchaseApplicationId));
        if (CollectionUtils.isEmpty(purchaseApprovalLogDTOList)) {
            return StringUtils.EMPTY;
        }
        for (PurchaseApprovalLogDTO purchaseApprovalLogDTO : purchaseApprovalLogDTOList) {
            if (approvalLevel == purchaseApprovalLogDTO.getApproveLevel()) {
                return purchaseApprovalLogDTO.getApprover();
            }
        }
        return StringUtils.EMPTY;
    }

    private String getBidApprover(int approvalLevel, OrderMasterDTO orderMasterDTO) {
        List<BidApprovalLogDTO> bidApprovalLogDTOList = bidClient.findApprovalLogInfo(orderMasterDTO.getForderno(), orderMasterDTO.getBidOrderId(), orderMasterDTO.getFusercode());
        if (CollectionUtils.isEmpty(bidApprovalLogDTOList)) {
            return StringUtils.EMPTY;
        }
        for (BidApprovalLogDTO bidApprovalLogDTO : bidApprovalLogDTOList) {
            if (approvalLevel == bidApprovalLogDTO.getLevel()) {
                return bidApprovalLogDTO.getOperatorName();
            }
        }
        return StringUtils.EMPTY;
    }

    private String getSectionChief(OrderMasterDTO orderMasterDTO) {
        OrderTypeEnum orderType = OrderTypeEnum.getByCode(orderMasterDTO.getOrderType());
        BusinessErrUtil.notNull(orderType, ExecptionMessageEnum.ORDER_TYPE_ERROR, orderMasterDTO.getOrderType());
        Integer purchaseApplicationId = orderMasterDTO.getFbuyerid();
        //科长(对宁波二院：入库单对应的订单对应的采购申请单或竞价单的二级审批人姓名）
        String sectionChiefName;
        switch (orderType) {
            case PURCHASE_ORDER:
            case CLINICAL_PURCHASE_ORDER:
                sectionChiefName = this.getPurchaseApprover(WarehouseConstant.SECTION_CHIEF_APPROVAL_LEVEL, purchaseApplicationId);
                break;
            case BID_ORDER:
                sectionChiefName = this.getBidApprover(WarehouseConstant.SECTION_CHIEF_APPROVAL_LEVEL, orderMasterDTO);
                break;
            default:
                throw new BusinessInterceptException(ExecptionMessageEnum.UNSUPPORTED_ORDER_TYPE, orderMasterDTO.getOrderType());
        }
        return sectionChiefName;
    }

    private String getDepartmentDirectorName(Integer departmentId) {
        //根据订单对应部门获取部门负责人信息
        DepartmentDTO departmentDTO = userClient.getDepartmentInfo(departmentId);
        BusinessErrUtil.notNull(departmentDTO, ExecptionMessageEnum.DEPARTMENT_INFO_NOT_FOUND_FOR_ORDER, departmentId);
        Integer managerId = departmentDTO.getManagerId();
        if (managerId != null) {
            UserBaseInfoDTO departmentDirectorInfo = userClient.getNotNullUserDetailById(managerId);
            return departmentDirectorInfo.getName();
        }
        return StringUtils.EMPTY;
    }

    private boolean needFirstLevelCategory(String orgCode) {
        return WarehouseConstant.ORG_CODE_NEED_FIRST_LEVEL_CATEGORY.contains(orgCode);
    }

    private void selfPopulateFirstLevelCategory(List<ProductBean> productBeansWithCateGoryId) {
        //获取商品一级分类(个性化)
        List<Integer> categoryIds = productBeansWithCateGoryId.stream().map(ProductBean::getCategoryId).distinct().collect(Collectors.toList());
        List<CategoryDTO> categoryDTOList = categoryServiceClient.getAllCategoryByIds(categoryIds);
        BusinessErrUtil.notEmpty(categoryDTOList, ExecptionMessageEnum.FAILED_TO_OBTAIN_PRODUCT_CATEGORY_INFO);
        for (ProductBean productBean : productBeansWithCateGoryId) {
            CategoryDTO categoryDTO = CategoryUtil.getFirstLevelCategory(productBean.getCategoryId(), categoryDTOList);
            Preconditions.notNull(categoryDTO, "找不到商品的一级分类信息，当前分类：" + productBean.getCategoryId());
            productBean.setFirstLevelCategoryId(categoryDTO.getId().intValue());
            productBean.setFirstLevelCategoryName(categoryDTO.getName());
        }
    }

    private String getPersonalizedBarcodeByOrderNo(String orgCode, String orderNo) {
        if (WarehouseConstant.ORG_CODE_NEED_ORDER_NO_BARCODE.contains(orgCode)) {
            try {
                return BarCodeUtils.getBase64Img(orderNo);
            } catch (Exception e) {
                Cat.logError(CAT_TYPE, "getPersonalizedBarcodeByOrderNo", "条形码生成异常，入参,orgCode:" + orgCode + ";orderNo:" + orderNo, e);
                return StringUtils.EMPTY;
            }
        }
        return StringUtils.EMPTY;
    }

    private String getPersonalizedBarcodeByExitNo(String orgCode, String exitNo) {
        if (ORG_CODE_NEED_Exit_NO_BARCODE.contains(orgCode)) {
            try {
                return BarCodeUtils.getBase64Img(exitNo);
            } catch (Exception e) {
                Cat.logError(CAT_TYPE, "getPersonalizedBarcodeByExitNo", "条形码生成异常，入参,orgCode:" + orgCode + ";exitNo:" + exitNo, e);
                return StringUtils.EMPTY;
            }
        }
        return StringUtils.EMPTY;
    }

    private void populatePurchaser(OrderBean receiver, UserBaseInfoDTO provider) {
        receiver.setPurchaserName(provider.getName());
        receiver.setPurchaserPhone(provider.getMobile());
    }

    private void populateProductPrice(ProductBean receiver, BizWarehouseExitDetailDTO exitDetail, double orderDetailRealPrice) {
        //商品单价
        double singleProductPrice = exitDetail.getUnitPrice() != null && exitDetail.getUnitPrice().compareTo(BigDecimal.ZERO) > 0 ? exitDetail.getUnitPrice().doubleValue() : orderDetailRealPrice;
        receiver.setSinglePrice(singleProductPrice);
        //商品总价
        double singleProductTotalPrice = exitDetail.getPrice() != null && exitDetail.getPrice().compareTo(BigDecimal.ZERO) > 0 ? exitDetail.getPrice().doubleValue() : singleProductPrice * exitDetail.getExitedNum();
        receiver.setTotalPrice(singleProductTotalPrice);
    }

    private void populateOutWarehouseApplicationPrice(OutWarehouseApplicationBean receiver, List<ProductBean> provider) {
        double totalPriceForWarehouseApplication = 0;
        for (ProductBean productBean : provider) {
            totalPriceForWarehouseApplication += productBean.getTotalPrice();
        }
        receiver.setTotalPrice(totalPriceForWarehouseApplication);
    }
}
