package com.ruijing.store.warehouse.controller;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.store.warehouse.message.vo.outwarehouse.OutWarehouseApplicationDetailVO;
import com.ruijing.store.warehouse.message.vo.outwarehouse.OutWarehouseDetailRequestVO;
import com.ruijing.store.warehouse.service.OutWarehouseGWService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/12/24 11:42
 */
@MSharpService(isGateway = "true")
@RpcApi(value = "采购人出库单网关服务", description = "采购人出库单网关服务")
@RpcMapping("/outwarehouse")
public class OutWarehouseGWController {

    @Resource
    private OutWarehouseGWService outWarehouseGWService;

    @RpcMethod(value = "获取出库申请单详情")
    @RpcMapping("/getOutWarehouseApplicationDetail")
    public RemoteResponse<OutWarehouseApplicationDetailVO> getOutWarehouseApplicationDetail(OutWarehouseDetailRequestVO request) {
        OutWarehouseApplicationDetailVO result = outWarehouseGWService.getOutWarehouseApplicationDetail(request);
        return RemoteResponse.<OutWarehouseApplicationDetailVO>custom().setSuccess().setData(result);
    }
}
