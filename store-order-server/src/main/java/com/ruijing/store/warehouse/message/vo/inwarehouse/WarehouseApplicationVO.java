package com.ruijing.store.warehouse.message.vo.inwarehouse;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="入库申请单简单信息, 用于入库单列表显示")
public class WarehouseApplicationVO implements Serializable {

    private static final long serialVersionUID = -3846329822836478405L;
    @RpcModelProperty(value = "入库单号")
    private String warehouseApplicationNo;

    @RpcModelProperty(value = "入库申请单Id")
    private Integer warehouseApplicationId;

    @RpcModelProperty(value = "订单号")
    private String orderNo;

    @RpcModelProperty(value = "申请时间")
    private Long warehouseApplicationTime;

    @RpcModelProperty(value = "申请人")
    private String warehouseApplicant;

    @RpcModelProperty(value = "库房名称")
    private String warehouseName;

    @RpcModelProperty(value = "审批状态（0审批中，1审批通过，2审批驳回）")
    private Integer approvalStatus;

    @RpcModelProperty(value = "审批状态名称（待审批、审核通过、审核驳回）")
    private String approvalStatusName;

    @RpcModelProperty(value = "入库单状态（0：未入库 1：已入库）")
    private  Integer status;

    @RpcModelProperty(value = "入库单状态名称（未入库、已入库）")
    private String statusName;

    @RpcModelProperty(value = "采购人所属的部门名称")
    private String departmentName;

    @RpcModelProperty("展示出入库单打印按钮")
    private Boolean showPrintWareHouseApplication;

    /**
     * 订单主表的入库状态
     */
    @RpcModelProperty(value = "订单主表的入库状态", enumLink = "com.ruijing.store.order.api.base.enums.InventoryStatusEnum")
    private Integer inventoryStatus;

    @RpcModelProperty(value = "自关联单号")
    private String refNo;

    @RpcModelProperty(value = "业务类型", enumLink = "com.ruijing.store.wms.api.enums.EntryBusinessTypeEnum")
    private Integer businessType;

    @RpcModelProperty(value = "重新提交")
    private boolean recommit;

    /**
     * 库房的推送状态，目前只管中爆
     */
    @RpcModelProperty(value = "推送状态", description = "推送状态  1 推送中 2推送成功 3推送失败")
    private Integer pushStatus;

    public String getWarehouseApplicationNo() {
        return warehouseApplicationNo;
    }

    public void setWarehouseApplicationNo(String warehouseApplicationNo) {
        this.warehouseApplicationNo = warehouseApplicationNo;
    }

    public Integer getWarehouseApplicationId() {
        return warehouseApplicationId;
    }

    public void setWarehouseApplicationId(Integer warehouseApplicationId) {
        this.warehouseApplicationId = warehouseApplicationId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getWarehouseApplicationTime() {
        return warehouseApplicationTime;
    }

    public void setWarehouseApplicationTime(Long warehouseApplicationTime) {
        this.warehouseApplicationTime = warehouseApplicationTime;
    }

    public String getWarehouseApplicant() {
        return warehouseApplicant;
    }

    public void setWarehouseApplicant(String warehouseApplicant) {
        this.warehouseApplicant = warehouseApplicant;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getApprovalStatusName() {
        return approvalStatusName;
    }

    public void setApprovalStatusName(String approvalStatusName) {
        this.approvalStatusName = approvalStatusName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Boolean getShowPrintWareHouseApplication() {
        return showPrintWareHouseApplication;
    }

    public void setShowPrintWareHouseApplication(Boolean showPrintWareHouseApplication) {
        this.showPrintWareHouseApplication = showPrintWareHouseApplication;
    }

    public Integer getInventoryStatus() {
        return inventoryStatus;
    }

    public void setInventoryStatus(Integer inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
    }

    public String getRefNo() {
        return refNo;
    }

    public void setRefNo(String refNo) {
        this.refNo = refNo;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public boolean isRecommit() {
        return recommit;
    }

    public void setRecommit(boolean recommit) {
        this.recommit = recommit;
    }

    public Integer getPushStatus() {
        return pushStatus;
    }

    public WarehouseApplicationVO setPushStatus(Integer pushStatus) {
        this.pushStatus = pushStatus;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", WarehouseApplicationVO.class.getSimpleName() + "[", "]")
                .add("warehouseApplicationNo='" + warehouseApplicationNo + "'")
                .add("warehouseApplicationId=" + warehouseApplicationId)
                .add("orderNo='" + orderNo + "'")
                .add("warehouseApplicationTime=" + warehouseApplicationTime)
                .add("warehouseApplicant='" + warehouseApplicant + "'")
                .add("warehouseName='" + warehouseName + "'")
                .add("approvalStatus=" + approvalStatus)
                .add("approvalStatusName='" + approvalStatusName + "'")
                .add("status=" + status)
                .add("statusName='" + statusName + "'")
                .add("departmentName='" + departmentName + "'")
                .add("showPrintWareHouseApplication=" + showPrintWareHouseApplication)
                .add("inventoryStatus=" + inventoryStatus)
                .add("refNo='" + refNo + "'")
                .add("businessType=" + businessType)
                .add("recommit=" + recommit)
                .add("pushStatus=" + pushStatus)
                .toString();
    }
}
