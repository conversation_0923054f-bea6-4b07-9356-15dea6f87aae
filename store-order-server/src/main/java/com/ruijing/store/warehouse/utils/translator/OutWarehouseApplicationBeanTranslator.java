package com.ruijing.store.warehouse.utils.translator;

import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.store.warehouse.message.bean.OutWarehouseApplicationBean;
import com.ruijing.store.wms.api.dto.BizWarehouseExitDTO;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/1/6 19:55
 */
public class OutWarehouseApplicationBeanTranslator {
    /**
     * 未出库状态对应名称
     */
    private static final String NOT_OUT_WAREHOUSE_STATUS_NAME = "未出库";

    /**
     * 已出库状态对应名称
     */
    private static final String ALREADY_OUT_WAREHOUSE_STATUS_NAME = "已出库";

    public static OutWarehouseApplicationBean bizWarehouseExitDTO2WarehouseApplicationBean(BizWarehouseExitDTO from) {
        OutWarehouseApplicationBean to = new OutWarehouseApplicationBean();
        to.setWarehouseName(from.getRoomName());
        to.setWarehouseId(from.getRoomId());
        to.setOutWarehouseApplicationDate(from.getCreateTime());
        to.setOutWarehouseApplicationNo(from.getExitNo());
        to.setOutWarehouseApplicationId(from.getId());
        to.setOutWarehouseApplicant(from.getUserName());
        to.setStatus(from.getStatus());
        to.setStatusName(from.getStatus() == null ? null : getStatusName(from.getStatus()));
        to.setOrderNo(from.getOrderNo());
        if (StringUtils.isNotBlank(from.getExitTime())) {
            to.setOutWarehouseDate(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, from.getExitTime()));
        }
        to.setDepartmentName(from.getDeptName());
        return to;
    }

    private static final String getStatusName(int status) {
        switch (status) {
            case 0:
                return NOT_OUT_WAREHOUSE_STATUS_NAME;
            case 1:
                return ALREADY_OUT_WAREHOUSE_STATUS_NAME;
            default:
                return "未知状态";
        }
    }
}
