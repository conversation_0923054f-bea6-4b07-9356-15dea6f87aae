package com.ruijing.store.warehouse.message.vo.claim;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 14:16 2020/12/24.
 * 申领单库房请求对象
 */
@RpcModel(description="申领单库房请求对象")
public class RoomListRequestVO implements Serializable {

    private static final long serialVersionUID = 4717454454475281942L;

    @RpcModelProperty(value = "课题组Id", example = "10")
    private Integer departmentId;

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("RoomListRequestVO{");
        sb.append("departmentId=").append(departmentId);
        sb.append('}');
        return sb.toString();
    }
}
