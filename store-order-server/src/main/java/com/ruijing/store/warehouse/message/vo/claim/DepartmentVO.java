package com.ruijing.store.warehouse.message.vo.claim;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 14:19 2020/12/24.
 * 申领单用户的部门信息
 */
@RpcModel(description="申领单用户的部门信息")
public class DepartmentVO implements Serializable {
    private static final long serialVersionUID = 772180762827572604L;

    @RpcModelProperty(value = "部门Id")
    private Integer departmentId;

    @RpcModelProperty(value = "部门名称")
    private String departmentName;

    public DepartmentVO() {
    }

    public DepartmentVO(Integer departmentId, String departmentName) {
        this.departmentId = departmentId;
        this.departmentName = departmentName;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("DepartmentVO{");
        sb.append("departmentId=").append(departmentId);
        sb.append(", departmentName='").append(departmentName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
