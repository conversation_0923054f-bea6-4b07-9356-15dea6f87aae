package com.ruijing.store.warehouse.service;

import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.warehouse.message.vo.inwarehouse.*;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryExitDTO;

import java.util.List;

public interface InWareHouseGWService {

    /**
     * 获取要提交入库申请的订单相关信息
     * @param request
     * @return
     */
    default WarehouseApplicationPrepareSubmitVO getPrepareSubmitInfoByOrderId(OrderRequestVO request) {
        return null;
    }

    /**
     * 批量获取要提交入库申请的订单相关信息
     * @param request
     * @return
     */
    default WarehouseApplicationPrepareSubmitBatchVO getBatchPrepareSubmitInfoByOrderId(OrderBasicParamDTO request) {
        return null;
    }

    /**
     * 提交入库申请单
     *
     * @param request
     * @return
     */
    default SubmitWarehouseVO submitWarehouseApplication(WarehouseSubmitApplicationRequestVO request) {
        return null;
    }

    /**
     * 批量提交入库申请单
     *
     * @param request
     * @return
     */
    default SubmitWarehouseVO batchSubmitWarehouseApplication(WarehouseSubmitBatchRequestVO request) {
        return null;
    }

    /**
     * 重新提交入库申请单
     *
     * @param request
     * @return
     */
    default SubmitWarehouseVO reSubmitWarehouseApplication(WarehouseReSubmitApplicationRequestVO request) {
        return null;
    }

    /**
     * 重新推送第三方平台入库状态
     * @param request
     * @return
     */
    default boolean rePushWarehousingToThirdPlatForm(OrderRequestVO request) {
        return false;
    }

    /**
     * 获取入库申请单列表(个人)
     * @param request
     * @return
     */
    default WarehouseApplicationPageVO getPersonalWarehouseApplicationList(WarehouseApplicationPersonalPageRequestVO request) {
        return null;
    }

    /**
     * 获取入库申请单列表(部门)
     * @param request
     * @return
     */
    default WarehouseApplicationPageVO getDepartmentWarehouseApplicationList(WarehouseApplicationDeptPageRequestVO request) {
        return null;
    }

    /**
     * 获取入库申请单详情
     * @param request
     * @return
     */
    default WarehouseApplicationDetailVO getWarehouseApplicationDetail(WarehouseApplicationDetailRequestVO request) {
        return null;
    }

    /**
     * 取入库申请单列表的枚举常量列表
     * @return
     */
    default WarehouseConstantListVO getWarehouseConstantList() {
        return null;
    }

    /**
     * 获取入库申请人为当前用户的所有入库申请单数量
     * @return
     */
    default CountWarehouseApplicationVO getPersonalWarehouseApplicationCount() {
        return null;
    }

    /**
     * 获取入库申请人为当前用户课题组成员的所有入库申请单数量
     * @return
     */
    default CountWarehouseApplicationVO getDepartmentWarehouseApplicationCount() {
        return null;
    }

    /**
     * 是否使用新出入库系统
     * @return
     */
    default boolean isUseNewWarehouseSystem() {
        return false;
    }

    /**
     * 获取订单相关的入库与出库信息列表，前端渲染打印用
     * @param request
     * @return
     */
    default InAndOutWarehouseApplicationDetailVO getInAndOutWarehouseInfoForOrder(OrderRequestVO request) {
        return null;
    }

    /**
     * 用户是否具有库房系统访问权限，没有首页不会显示库房系统入口
     * @return
     */
    default boolean haveWarehouseAccess() {
        return false;
    }

    /**
     * 用户是否能把当前订单提交入库
     * @param request
     * @return
     */
    default boolean checkCanSubmitWarehouse(OrderRequestVO request) {
        return false;
    }

    /**
     * 检查商品是否已入库
     * @param request
     * @return
     */
    default WarehouseProductInfoVO checkProductHasInWarehouse(WarehouseProductRequestVO request) {
        return null;
    }

    /**
     * 查询出入库单信息
     *
     * @param request
     */
    default List<BizWarehouseEntryExitDTO> queryEntryExitByOrderNo(OrderRequestVO request) {
        return null;
    }

}
