package com.ruijing.store.warehouse.message.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2022/10/25 14:16
 * @description
 */
@RpcModel("入库对接推送数据")
public class WarehouseDockingDataDTO implements Serializable {

    private static final long serialVersionUID = -8170310074834701965L;

    @RpcModelProperty("领料人工号")
    private String materialsUserJobNumber;
    
    @RpcModelProperty("出库科室ID")
    private String exitDeptId;
    
    @RpcModelProperty("出库科室名")
    private String exitDeptName;
    
    @RpcModelProperty("出库科室代码")
    private String exitDeptCode;
    
    @RpcModelProperty("资金性质")
    private Integer capitalProperty;

    public String getMaterialsUserJobNumber() {
        return materialsUserJobNumber;
    }

    public void setMaterialsUserJobNumber(String materialsUserJobNumber) {
        this.materialsUserJobNumber = materialsUserJobNumber;
    }

    public String getExitDeptId() {
        return exitDeptId;
    }

    public void setExitDeptId(String exitDeptId) {
        this.exitDeptId = exitDeptId;
    }

    public String getExitDeptName() {
        return exitDeptName;
    }

    public void setExitDeptName(String exitDeptName) {
        this.exitDeptName = exitDeptName;
    }

    public String getExitDeptCode() {
        return exitDeptCode;
    }

    public void setExitDeptCode(String exitDeptCode) {
        this.exitDeptCode = exitDeptCode;
    }

    public Integer getCapitalProperty() {
        return capitalProperty;
    }

    public void setCapitalProperty(Integer capitalProperty) {
        this.capitalProperty = capitalProperty;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", WarehouseDockingDataDTO.class.getSimpleName() + "[", "]")
                .add("materialsUserJobNumber='" + materialsUserJobNumber + "'")
                .add("exitDeptId='" + exitDeptId + "'")
                .add("exitDeptName='" + exitDeptName + "'")
                .add("exitDeptCode='" + exitDeptCode + "'")
                .add("capitalProperty=" + capitalProperty)
                .toString();
    }
}
