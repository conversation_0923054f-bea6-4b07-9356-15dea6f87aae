package com.ruijing.store.warehouse.utils.translator;

import com.ruijing.store.warehouse.message.dto.WarehouseDockingDataDTO;
import com.ruijing.store.wms.api.dto.docking.AHSLDockIngDTO;

/**
 * <AUTHOR>
 * @date 2022/10/25 15:30
 * @description
 */
public class WareHouseDockingDataTranslator {

    /**
     * 库房系统安徽省立推送dto 转 订单库房数据推送dto
     *
     * @param ahslDockIngDTO 安徽省立推送dto
     * @return 订单库房数据推送dto
     */
    public static WarehouseDockingDataDTO ahslDockingDtoToWareHouseDataDto(AHSLDockIngDTO ahslDockIngDTO) {
        if (ahslDockIngDTO == null) {
            return null;
        }
        // 中国科学技术大学附属第一医院（安徽省立医院）推送数据定制
        WarehouseDockingDataDTO dockingData = new WarehouseDockingDataDTO();
        dockingData.setCapitalProperty(ahslDockIngDTO.getCapitalProperty());
        dockingData.setExitDeptId(ahslDockIngDTO.getExitDeptId());
        dockingData.setExitDeptName(ahslDockIngDTO.getExitDeptName());
        dockingData.setExitDeptCode(ahslDockIngDTO.getExitDeptCode());
        dockingData.setMaterialsUserJobNumber(ahslDockIngDTO.getMaterialsUserJobNumber());
        return dockingData;
    }

    /**
     * 订单库房数据推送dto 转 库房系统安徽省立推送dto
     *
     * @param warehouseDockingDataDTO 订单库房数据推送dto
     * @return 库房系统安徽省立推送dto
     */
    public static AHSLDockIngDTO wareHouseDataDtoToAhslDockingDto(WarehouseDockingDataDTO warehouseDockingDataDTO) {
        if (warehouseDockingDataDTO == null) {
            return null;
        }
        // 中国科学技术大学附属第一医院（安徽省立医院）推送数据定制
        AHSLDockIngDTO dockingData = new AHSLDockIngDTO();
        dockingData.setCapitalProperty(warehouseDockingDataDTO.getCapitalProperty());
        dockingData.setExitDeptId(warehouseDockingDataDTO.getExitDeptId());
        dockingData.setExitDeptName(warehouseDockingDataDTO.getExitDeptName());
        dockingData.setExitDeptCode(warehouseDockingDataDTO.getExitDeptCode());
        dockingData.setMaterialsUserJobNumber(warehouseDockingDataDTO.getMaterialsUserJobNumber());
        return dockingData;
    }
}
