package com.ruijing.store.warehouse.message.bean;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 * 出库申请单详细信息
 */
public class OutWarehouseApplicationBean {

    /**
     * 出库单Id
     */
    private Integer outWarehouseApplicationId;

    /**
     * 出库单号
     */
    private String outWarehouseApplicationNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 出库申请时间
     */
    private Date outWarehouseApplicationDate;

    /**
     * 出库申请人
     */
    private String outWarehouseApplicant;

    /**
     * 库房Id
     */
    private Integer warehouseId;

    /**
     * 库房名称
     */
    private String warehouseName;

    /**
     * 出库单状态（状态0未出库，1已出库）
     */
    private  Integer status;

    /**
     * 申请出库商品状态名称（未出库、已出库）
     */
    private String statusName;

    /**
     * 申领人所属的部门名称,和订单部门一致
     */
    private String departmentName;

    /**
     * 出库单所有商品的总额的合计
     */
    private double totalPrice;

    /**
     * 商品的总额的大写
     */
    private String totalPriceInChinese;

    /**
     * 出库日期
     */
    private Date outWarehouseDate;

    /**
     * 出库单号对应条形码
     */
    private String exitNoBarcode;

    /**
     * 出库申请单关联商品信息列表
     */
    private List<ProductBean> productBeans;

    public Integer getOutWarehouseApplicationId() {
        return outWarehouseApplicationId;
    }

    public void setOutWarehouseApplicationId(Integer outWarehouseApplicationId) {
        this.outWarehouseApplicationId = outWarehouseApplicationId;
    }

    public String getOutWarehouseApplicationNo() {
        return outWarehouseApplicationNo;
    }

    public void setOutWarehouseApplicationNo(String outWarehouseApplicationNo) {
        this.outWarehouseApplicationNo = outWarehouseApplicationNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Date getOutWarehouseApplicationDate() {
        return outWarehouseApplicationDate;
    }

    public void setOutWarehouseApplicationDate(Date outWarehouseApplicationDate) {
        this.outWarehouseApplicationDate = outWarehouseApplicationDate;
    }

    public String getOutWarehouseApplicant() {
        return outWarehouseApplicant;
    }

    public void setOutWarehouseApplicant(String outWarehouseApplicant) {
        this.outWarehouseApplicant = outWarehouseApplicant;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getTotalPriceInChinese() {
        return totalPriceInChinese;
    }

    public void setTotalPriceInChinese(String totalPriceInChinese) {
        this.totalPriceInChinese = totalPriceInChinese;
    }

    public Date getOutWarehouseDate() {
        return outWarehouseDate;
    }

    public void setOutWarehouseDate(Date outWarehouseDate) {
        this.outWarehouseDate = outWarehouseDate;
    }

    public String getExitNoBarcode() {
        return exitNoBarcode;
    }

    public void setExitNoBarcode(String exitNoBarcode) {
        this.exitNoBarcode = exitNoBarcode;
    }

    public List<ProductBean> getProductBeans() {
        return productBeans;
    }

    public void setProductBeans(List<ProductBean> productBeans) {
        this.productBeans = productBeans;
    }

    public double getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(double totalPrice) {
        this.totalPrice = totalPrice;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OutWarehouseApplicationBean{");
        sb.append("outWarehouseApplicationId=").append(outWarehouseApplicationId);
        sb.append(", outWarehouseApplicationNo='").append(outWarehouseApplicationNo).append('\'');
        sb.append(", orderNo='").append(orderNo).append('\'');
        sb.append(", outWarehouseApplicationDate=").append(outWarehouseApplicationDate);
        sb.append(", outWarehouseApplicant='").append(outWarehouseApplicant).append('\'');
        sb.append(", warehouseId=").append(warehouseId);
        sb.append(", warehouseName='").append(warehouseName).append('\'');
        sb.append(", status=").append(status);
        sb.append(", statusName='").append(statusName).append('\'');
        sb.append(", departmentName='").append(departmentName).append('\'');
        sb.append(", totalPrice='").append(totalPrice).append('\'');
        sb.append(", totalPriceInChinese='").append(totalPriceInChinese).append('\'');
        sb.append(", outWarehouseDate=").append(outWarehouseDate);
        sb.append(", exitNoBarcode='").append(exitNoBarcode).append('\'');
        sb.append(", productBeans=").append(productBeans);
        sb.append('}');
        return sb.toString();
    }
}
