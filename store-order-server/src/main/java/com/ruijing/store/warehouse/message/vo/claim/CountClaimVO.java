package com.ruijing.store.warehouse.message.vo.claim;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 15:23 2020/12/24.
 * 展示各状态的申领单数量
 */
@RpcModel(description="申领单列表获取已入库、未入库、总数信息返回")
public class CountClaimVO implements Serializable {

    private static final long serialVersionUID = 1873150556348382863L;

    @RpcModelProperty(value = "全部数量")
    private Integer allCount;

    @RpcModelProperty(value = "未完成数量")
    private Integer notCompleteCount;

    @RpcModelProperty(value = "已完成数量")
    private Integer completeCount;

    @RpcModelProperty(value = "已取消数量")
    private Integer cancelCount;

    public Integer getAllCount() {
        return allCount;
    }

    public void setAllCount(Integer allCount) {
        this.allCount = allCount;
    }

    public Integer getNotCompleteCount() {
        return notCompleteCount;
    }

    public void setNotCompleteCount(Integer notCompleteCount) {
        this.notCompleteCount = notCompleteCount;
    }

    public Integer getCompleteCount() {
        return completeCount;
    }

    public void setCompleteCount(Integer completeCount) {
        this.completeCount = completeCount;
    }

    public Integer getCancelCount() {
        return cancelCount;
    }

    public void setCancelCount(Integer cancelCount) {
        this.cancelCount = cancelCount;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("CountClaimVO{");
        sb.append("allCount=").append(allCount);
        sb.append(", notCompleteCount=").append(notCompleteCount);
        sb.append(", completeCount=").append(completeCount);
        sb.append(", cancelCount=").append(cancelCount);
        sb.append('}');
        return sb.toString();
    }
}
