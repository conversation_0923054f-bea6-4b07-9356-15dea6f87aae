package com.ruijing.store.warehouse.message.bean;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/01/05.
 */
@RpcModel(description="入库单操作日志")
public class WarehouseOperationLogBean {

    /**
     * 操作人
     */
    @RpcModelProperty("操作人")
    private String operator;

    /**
     * 操作内容
     */
    @RpcModelProperty("操作内容")
    private String operation;

    /**
     * 操作时间
     */
    @RpcModelProperty("操作时间")
    private Date operationDate;

    /**
     * 备注
     */
    @RpcModelProperty("备注")
    private String comment;

    /**
     * 审批意见
     */
    @RpcModelProperty("审批意见")
    private String approvalOpinion;

    @RpcModelProperty("是否为电子签名 0非1是")
    private Integer sign;
    @RpcModelProperty("电子签名图片")
    private String signPhoto;

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Date getOperationDate() {
        return operationDate;
    }

    public void setOperationDate(Date operationDate) {
        this.operationDate = operationDate;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getApprovalOpinion() {
        return approvalOpinion;
    }

    public void setApprovalOpinion(String approvalOpinion) {
        this.approvalOpinion = approvalOpinion;
    }

    public Integer getSign() {
        return sign;
    }

    public WarehouseOperationLogBean setSign(Integer sign) {
        this.sign = sign;
        return this;
    }

    public String getSignPhoto() {
        return signPhoto;
    }

    public WarehouseOperationLogBean setSignPhoto(String signPhoto) {
        this.signPhoto = signPhoto;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("WarehouseOperationLogBean{");
        sb.append("operator='").append(operator).append('\'');
        sb.append(", operation='").append(operation).append('\'');
        sb.append(", operationDate=").append(operationDate);
        sb.append(", comment='").append(comment).append('\'');
        sb.append(", approvalOpinion='").append(approvalOpinion).append('\'');
        sb.append(", sign=").append(sign);
        sb.append(", signPhoto='").append(signPhoto).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
