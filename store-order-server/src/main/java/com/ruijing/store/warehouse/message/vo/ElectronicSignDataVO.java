package com.ruijing.store.warehouse.message.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/6/17 15:57
 * @description
 */
@RpcModel("电子签名通用返回体")
public class ElectronicSignDataVO implements Serializable {

    private static final long serialVersionUID = 515520030078536506L;
    
    @RpcModelProperty("电子签名类型")
    /**
     * {@link com.ruijing.store.electronicsign.api.enums.ElectronicSignatureOperationEnum}
     */
    private Integer type;
    
    @RpcModelProperty("电子签名路径")
    private String url;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ElectronicSignDataVO{");
        sb.append("type=").append(type);
        sb.append(", url='").append(url).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
