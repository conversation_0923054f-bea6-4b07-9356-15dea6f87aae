package com.ruijing.store.warehouse.message.bean;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.math.BigDecimal;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 * 订单商品信息
 */
public class ProductBean {

    /**
     * 商品Id
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品规格
     */
    private String specifications;

    /**
     * 商品品牌
     */
    private String brand;

    /**
     * 商品货号
     */
    private String goodCode;

    /**
     * CAS号
     */
    private String casNo;

    /**
     * 危化品标识(类型)
     */
    private Integer dangerousType;

    /**
     * 危化品标识名称
     */
    private String dangerousTypeName;

    /**
     * 是否是危化品, 0不是， 1是
     */
    private Integer dangerousFlag;

    /**
     * 管制类型, 1,管制类;2,非管制,其他商品类型为null
     */
    private Integer regulatoryFlag;

    /**
     * 商品单位
     */
    private String unit;

    /**
     * 商品数量
     */
    private Integer quantityWithoutReturn;

    /**
     * 退货后数量
     */
    private Integer quantityAfterReturn;

    /**
     * 计量总量
     */
    private Double totalQuantity;

    /**
     * 计量单位
     */
    private String quantityUnit;

    /**
     * 供应商Id
     */
    private Integer supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 商品图片
     */
    private String productPhoto;

    /**
     * 是否是要提交入库的商品，如果这个商品的分类不在该单位指定的分类中则无需提交入库;0不需要入库，1需要入库
     */
    private Integer needSubmitWarehouseTag;

    /**
     * 商品单价
     */
    private double singlePrice;

    /**
     * 商品总价
     */
    private double totalPrice;

    /**
     * 退货后总价
     */
    private BigDecimal totalPriceAfterReturn;

    /**
     * 商品分类id
     */
    private Integer categoryId;

    /**
     * 一级商品分类id
     */
    private Integer firstLevelCategoryId;

    /**
     * 一级商品分类名称
     */
    private String firstLevelCategoryName;

    /**
     * 是否需要用户输入危化品属性, 0否，1是
     */
    private Integer needShowDangerousInputFlag;

    /**
     * {@link com.ruijing.store.wms.api.enums.FormEnum}
     * 商品状态, 1 固体, 2 液体, 3 气体
     */
    private Integer form;

    /**
     * 默认库房Id
     */
    private Integer defaultWarehouseId;

    /**
     * 个性化商品品类名称，只用作存取显示
     */
    private String personalizedCategoryName;

    /**
     * 订单明细id
     */
    private Integer orderDetailId;

    /**
     * 补充库房信息分类
     */
    @RpcModelProperty(value = "补充库房-分类")
    private String supplyCategory;

    @RpcModelProperty(value = "补充库房-物料编号")
    private String materialNo;

    @RpcModelProperty("中爆-损耗量")
    private String cbsdWastage;

    @RpcModelProperty("中爆-合法用途")
    private String cbsdLegallyPurposes;

    @RpcModelProperty(value = "中爆-储物场所id")
    private String cbsdStorageAreaId;

    @RpcModelProperty("中爆-储物场所")
    private String cbsdStorageArea;

    @RpcModelProperty("绑定的气瓶码")
    private List<String> bindGasBottleBarcodes;

    @RpcModelProperty(value = "注册编码", description = "医疗器械注册证书编号")
    private String medicalDeviceRegisCertNumber;

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public Integer getDangerousType() {
        return dangerousType;
    }

    public void setDangerousType(Integer dangerousType) {
        this.dangerousType = dangerousType;
    }

    public String getDangerousTypeName() {
        return dangerousTypeName;
    }

    public void setDangerousTypeName(String dangerousTypeName) {
        this.dangerousTypeName = dangerousTypeName;
    }

    public Integer getDangerousFlag() {
        return dangerousFlag;
    }

    public void setDangerousFlag(Integer dangerousFlag) {
        this.dangerousFlag = dangerousFlag;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Integer getQuantityWithoutReturn() {
        return quantityWithoutReturn;
    }

    public void setQuantityWithoutReturn(Integer quantityWithoutReturn) {
        this.quantityWithoutReturn = quantityWithoutReturn;
    }

    public Integer getQuantityAfterReturn() {
        return quantityAfterReturn;
    }

    public void setQuantityAfterReturn(Integer quantityAfterReturn) {
        this.quantityAfterReturn = quantityAfterReturn;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getProductPhoto() {
        return productPhoto;
    }

    public void setProductPhoto(String productPhoto) {
        this.productPhoto = productPhoto;
    }

    public String getGoodCode() {
        return goodCode;
    }

    public void setGoodCode(String goodCode) {
        this.goodCode = goodCode;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public String getQuantityUnit() {
        return quantityUnit;
    }

    public void setQuantityUnit(String quantityUnit) {
        this.quantityUnit = quantityUnit;
    }

    public Double getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Double totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public Integer getNeedSubmitWarehouseTag() {
        return needSubmitWarehouseTag;
    }

    public void setNeedSubmitWarehouseTag(Integer needSubmitWarehouseTag) {
        this.needSubmitWarehouseTag = needSubmitWarehouseTag;
    }

    public double getSinglePrice() {
        return singlePrice;
    }

    public void setSinglePrice(double singlePrice) {
        this.singlePrice = singlePrice;
    }

    public double getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(double totalPrice) {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getTotalPriceAfterReturn() {
        return totalPriceAfterReturn;
    }

    public void setTotalPriceAfterReturn(BigDecimal totalPriceAfterReturn) {
        this.totalPriceAfterReturn = totalPriceAfterReturn;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getNeedShowDangerousInputFlag() {
        return needShowDangerousInputFlag;
    }

    public void setNeedShowDangerousInputFlag(Integer needShowDangerousInputFlag) {
        this.needShowDangerousInputFlag = needShowDangerousInputFlag;
    }

    public Integer getRegulatoryFlag() {
        return regulatoryFlag;
    }

    public void setRegulatoryFlag(Integer regulatoryFlag) {
        this.regulatoryFlag = regulatoryFlag;
    }

    public Integer getForm() {
        return form;
    }

    public void setForm(Integer form) {
        this.form = form;
    }

    public String getFirstLevelCategoryName() {
        return firstLevelCategoryName;
    }

    public void setFirstLevelCategoryName(String firstLevelCategoryName) {
        this.firstLevelCategoryName = firstLevelCategoryName;
    }

    public Integer getDefaultWarehouseId() {
        return defaultWarehouseId;
    }

    public void setDefaultWarehouseId(Integer defaultWarehouseId) {
        this.defaultWarehouseId = defaultWarehouseId;
    }

    public String getPersonalizedCategoryName() {
        return personalizedCategoryName;
    }

    public void setPersonalizedCategoryName(String personalizedCategoryName) {
        this.personalizedCategoryName = personalizedCategoryName;
    }

    public Integer getFirstLevelCategoryId() {
        return firstLevelCategoryId;
    }

    public void setFirstLevelCategoryId(Integer firstLevelCategoryId) {
        this.firstLevelCategoryId = firstLevelCategoryId;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public String getSupplyCategory() {
        return supplyCategory;
    }

    public void setSupplyCategory(String supplyCategory) {
        this.supplyCategory = supplyCategory;
    }

    public String getMaterialNo() {
        return materialNo;
    }

    public void setMaterialNo(String materialNo) {
        this.materialNo = materialNo;
    }

    public String getCbsdWastage() {
        return cbsdWastage;
    }

    public ProductBean setCbsdWastage(String cbsdWastage) {
        this.cbsdWastage = cbsdWastage;
        return this;
    }

    public String getCbsdLegallyPurposes() {
        return cbsdLegallyPurposes;
    }

    public ProductBean setCbsdLegallyPurposes(String cbsdLegallyPurposes) {
        this.cbsdLegallyPurposes = cbsdLegallyPurposes;
        return this;
    }

    public String getCbsdStorageAreaId() {
        return cbsdStorageAreaId;
    }

    public ProductBean setCbsdStorageAreaId(String cbsdStorageAreaId) {
        this.cbsdStorageAreaId = cbsdStorageAreaId;
        return this;
    }

    public String getCbsdStorageArea() {
        return cbsdStorageArea;
    }

    public ProductBean setCbsdStorageArea(String cbsdStorageArea) {
        this.cbsdStorageArea = cbsdStorageArea;
        return this;
    }

    public List<String> getBindGasBottleBarcodes() {
        return bindGasBottleBarcodes;
    }

    public ProductBean setBindGasBottleBarcodes(List<String> bindGasBottleBarcodes) {
        this.bindGasBottleBarcodes = bindGasBottleBarcodes;
        return this;
    }

    public String getMedicalDeviceRegisCertNumber() {
        return medicalDeviceRegisCertNumber;
    }

    public ProductBean setMedicalDeviceRegisCertNumber(String medicalDeviceRegisCertNumber) {
        this.medicalDeviceRegisCertNumber = medicalDeviceRegisCertNumber;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ProductBean.class.getSimpleName() + "[", "]")
                .add("productId=" + productId)
                .add("productName='" + productName + "'")
                .add("specifications='" + specifications + "'")
                .add("brand='" + brand + "'")
                .add("goodCode='" + goodCode + "'")
                .add("casNo='" + casNo + "'")
                .add("dangerousType=" + dangerousType)
                .add("dangerousTypeName='" + dangerousTypeName + "'")
                .add("dangerousFlag=" + dangerousFlag)
                .add("regulatoryFlag=" + regulatoryFlag)
                .add("unit='" + unit + "'")
                .add("quantityWithoutReturn=" + quantityWithoutReturn)
                .add("quantityAfterReturn=" + quantityAfterReturn)
                .add("totalQuantity=" + totalQuantity)
                .add("quantityUnit='" + quantityUnit + "'")
                .add("supplierId=" + supplierId)
                .add("supplierName='" + supplierName + "'")
                .add("productPhoto='" + productPhoto + "'")
                .add("needSubmitWarehouseTag=" + needSubmitWarehouseTag)
                .add("singlePrice=" + singlePrice)
                .add("totalPrice=" + totalPrice)
                .add("totalPriceAfterReturn=" + totalPriceAfterReturn)
                .add("categoryId=" + categoryId)
                .add("firstLevelCategoryId=" + firstLevelCategoryId)
                .add("firstLevelCategoryName='" + firstLevelCategoryName + "'")
                .add("needShowDangerousInputFlag=" + needShowDangerousInputFlag)
                .add("form=" + form)
                .add("defaultWarehouseId=" + defaultWarehouseId)
                .add("personalizedCategoryName='" + personalizedCategoryName + "'")
                .add("orderDetailId=" + orderDetailId)
                .add("supplyCategory='" + supplyCategory + "'")
                .add("materialNo='" + materialNo + "'")
                .add("cbsdWastage='" + cbsdWastage + "'")
                .add("cbsdLegallyPurposes='" + cbsdLegallyPurposes + "'")
                .add("cbsdStorageAreaId='" + cbsdStorageAreaId + "'")
                .add("cbsdStorageArea='" + cbsdStorageArea + "'")
                .add("bindGasBottleBarcodes=" + bindGasBottleBarcodes)
                .add("medicalDeviceRegisCertNumber='" + medicalDeviceRegisCertNumber + "'")
                .toString();
    }
}
