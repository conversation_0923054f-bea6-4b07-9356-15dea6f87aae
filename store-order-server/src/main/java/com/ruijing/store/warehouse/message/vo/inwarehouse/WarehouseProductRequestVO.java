package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="入库商品保存请求参数")
public class WarehouseProductRequestVO implements Serializable {

    private static final long serialVersionUID = -2256148826857051104L;

    @RpcModelProperty(value = "库房Id", example = "94")
    private Integer warehouseId;

    @RpcModelProperty(value = "库房名称", example = "生物库房")
    private String warehouseName;

    @RpcModelProperty(value = "商品Id，暂未用到", example = "100000903320", hidden = true)
    private Long productId;

    @RpcModelProperty(value = "商品数量", example = "10", hidden = true)
    private Integer quantity;

    @RpcModelProperty(value = "总商品计量含量（计量数量）", example = "50.0")
    private Double totalQuantity;

    @RpcModelProperty(value = "计量数量（单个）")
    private BigDecimal unitMeasurementNum;

    @RpcModelProperty(value = "计量单位", example = "ml")
    private String quantityUnit;

    @RpcModelProperty(value = "商品名称", example = "入库test商品")
    private String productName;

    @RpcModelProperty(value = "商品规格", example = "10ml/盒", hidden = true)
    private String specifications;

    @RpcModelProperty(value = "商品品牌", example = "卡尔马", hidden = true)
    private String brand;

    @RpcModelProperty(value = "商品货号", example = "47266")
    private String goodCode;

    @RpcModelProperty(value = "CAS号", example = "cas号")
    private String casNo;

    @RpcModelProperty(value = "危化品标识(类型) 1,管制类;2,非管制", example = "1")
    private Integer dangerousType;

    @RpcModelProperty(value = "危化品标识名称", example = "管制类", hidden = true)
    private String dangerousTypeName;

    @RpcModelProperty(value = "是否是危化品, 0不是， 1是", example = "1", hidden = true)
    private Integer dangerousFlag;

    @RpcModelProperty(value = "单位", example = "盒", hidden = true)
    private String unit;

    @RpcModelProperty(value = "供应商Id", example = "1234")
    private Integer supplierId;

    @RpcModelProperty(value = "供应商名称", example = "test供应商", hidden = true)
    private String supplierName;

    @RpcModelProperty(value = "商品图片", example = "http://images-test.rjmart.cn/image/c19bd248/a8a4188f-22db-4146-8561-6a231f9bff75.jpg")
    private String productPhoto;

    @RpcModelProperty(value = "商品分类id", example = "570")
    private Integer categoryId;

    /**
     * {@link com.ruijing.store.wms.api.enums.FormEnum}
     */
    @RpcModelProperty(value = "商品状态, 1 固体, 2 液体, 3 气体")
    private Integer form;

    @RpcModelProperty(value = "管制类型, 1,管制类;2,非管制,其他商品类型为null", hidden = true)
    private Integer regulatoryFlag;

    @RpcModelProperty(value = "个性化商品品类名称，只用作存取显示")
    private String personalizedCategoryName;

    @RpcModelProperty(value = "订单详情id", example = "10888")
    private Integer orderDetailId;

    @RpcModelProperty("中爆-损耗量")
    private String cbsdWastage;

    @RpcModelProperty("中爆-合法用途")
    private String cbsdLegallyPurposes;

    @RpcModelProperty(value = "中爆-储物场所id")
    private String cbsdStorageAreaId;

    @RpcModelProperty("中爆-储物场所")
    private String cbsdStorageArea;

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Double getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Double totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public BigDecimal getUnitMeasurementNum() {
        return unitMeasurementNum;
    }

    public WarehouseProductRequestVO setUnitMeasurementNum(BigDecimal unitMeasurementNum) {
        this.unitMeasurementNum = unitMeasurementNum;
        return this;
    }

    public String getQuantityUnit() {
        return quantityUnit;
    }

    public void setQuantityUnit(String quantityUnit) {
        this.quantityUnit = quantityUnit;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getGoodCode() {
        return goodCode;
    }

    public void setGoodCode(String goodCode) {
        this.goodCode = goodCode;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public Integer getDangerousType() {
        return dangerousType;
    }

    public void setDangerousType(Integer dangerousType) {
        this.dangerousType = dangerousType;
    }

    public String getDangerousTypeName() {
        return dangerousTypeName;
    }

    public void setDangerousTypeName(String dangerousTypeName) {
        this.dangerousTypeName = dangerousTypeName;
    }

    public Integer getDangerousFlag() {
        return dangerousFlag;
    }

    public void setDangerousFlag(Integer dangerousFlag) {
        this.dangerousFlag = dangerousFlag;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getProductPhoto() {
        return productPhoto;
    }

    public void setProductPhoto(String productPhoto) {
        this.productPhoto = productPhoto;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Integer getForm() {
        return form;
    }

    public void setForm(Integer form) {
        this.form = form;
    }

    public Integer getRegulatoryFlag() {
        return regulatoryFlag;
    }

    public void setRegulatoryFlag(Integer regulatoryFlag) {
        this.regulatoryFlag = regulatoryFlag;
    }

    public String getPersonalizedCategoryName() {
        return personalizedCategoryName;
    }

    public void setPersonalizedCategoryName(String personalizedCategoryName) {
        this.personalizedCategoryName = personalizedCategoryName;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public String getCbsdWastage() {
        return cbsdWastage;
    }

    public WarehouseProductRequestVO setCbsdWastage(String cbsdWastage) {
        this.cbsdWastage = cbsdWastage;
        return this;
    }

    public String getCbsdLegallyPurposes() {
        return cbsdLegallyPurposes;
    }

    public WarehouseProductRequestVO setCbsdLegallyPurposes(String cbsdLegallyPurposes) {
        this.cbsdLegallyPurposes = cbsdLegallyPurposes;
        return this;
    }

    public String getCbsdStorageAreaId() {
        return cbsdStorageAreaId;
    }

    public WarehouseProductRequestVO setCbsdStorageAreaId(String cbsdStorageAreaId) {
        this.cbsdStorageAreaId = cbsdStorageAreaId;
        return this;
    }

    public String getCbsdStorageArea() {
        return cbsdStorageArea;
    }

    public WarehouseProductRequestVO setCbsdStorageArea(String cbsdStorageArea) {
        this.cbsdStorageArea = cbsdStorageArea;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", WarehouseProductRequestVO.class.getSimpleName() + "[", "]")
                .add("warehouseId=" + warehouseId)
                .add("warehouseName='" + warehouseName + "'")
                .add("productId=" + productId)
                .add("quantity=" + quantity)
                .add("totalQuantity=" + totalQuantity)
                .add("unitMeasurementNum=" + unitMeasurementNum)
                .add("quantityUnit='" + quantityUnit + "'")
                .add("productName='" + productName + "'")
                .add("specifications='" + specifications + "'")
                .add("brand='" + brand + "'")
                .add("goodCode='" + goodCode + "'")
                .add("casNo='" + casNo + "'")
                .add("dangerousType=" + dangerousType)
                .add("dangerousTypeName='" + dangerousTypeName + "'")
                .add("dangerousFlag=" + dangerousFlag)
                .add("unit='" + unit + "'")
                .add("supplierId=" + supplierId)
                .add("supplierName='" + supplierName + "'")
                .add("productPhoto='" + productPhoto + "'")
                .add("categoryId=" + categoryId)
                .add("form=" + form)
                .add("regulatoryFlag=" + regulatoryFlag)
                .add("personalizedCategoryName='" + personalizedCategoryName + "'")
                .add("orderDetailId=" + orderDetailId)
                .add("cbsdWastage='" + cbsdWastage + "'")
                .add("cbsdLegallyPurposes='" + cbsdLegallyPurposes + "'")
                .add("cbsdStorageAreaId='" + cbsdStorageAreaId + "'")
                .add("cbsdStorageArea='" + cbsdStorageArea + "'")
                .toString();
    }
}
