package com.ruijing.store.warehouse.utils;

import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.store.order.api.base.enums.OrderDetailReturnStatus;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.base.core.model.OrderDetailDO;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/11/2 14:59
 */
public class OrderDetailsUtil {

    /**
     * 竞价类型的订单的值
     */
    private static final int BID_ORDER_TYPE = 1;

    public static BigDecimal getRealOrderProductPrice(OrderDetailSearchDTO orderDetailSearchDTO) {
        BigDecimal productNum = BigDecimal.valueOf(Integer.valueOf(getRealOrderProductQuantity(orderDetailSearchDTO)).doubleValue());
        return BigDecimal.valueOf(orderDetailSearchDTO.getFbidprice()).multiply(productNum);
    }

    public static BigDecimal getRealOrderProductPrice(OrderDetailDTO orderDetailDTO) {
        BigDecimal productNum = BigDecimal.valueOf(Integer.valueOf(getRealOrderProductQuantity(orderDetailDTO)).doubleValue());
        return orderDetailDTO.getFbidprice().multiply(productNum);
    }

    public static BigDecimal getRealOrderProductPrice(OrderDetailDO orderDetailDO) {
        BigDecimal productNum = BigDecimal.valueOf(Integer.valueOf(getRealOrderProductQuantity(orderDetailDO)).doubleValue());
        return orderDetailDO.getFbidprice().multiply(productNum);
    }

    /**
     * 获取订单详情真正的商品数量
     * @param orderDetailDTO
     * @return
     */
    public static int getRealOrderProductQuantity(OrderDetailDTO orderDetailDTO) {
        Preconditions.notNull(orderDetailDTO, "订单详情对象不能为空");
        Preconditions.notNull(orderDetailDTO.getFquantity(), "订单详情对象的商品数量不能为空");
        Integer cancelQuantity = orderDetailDTO.getFcancelquantity() == null ? null : orderDetailDTO.getFcancelquantity().intValue();
        return doGetRealOrderProductQuantity(orderDetailDTO.getFquantity().intValue(), cancelQuantity, orderDetailDTO.getReturnStatus());
    }

    public static int getRealOrderProductQuantity(OrderDetailSearchDTO orderDetailSearchDTO) {
        Preconditions.notNull(orderDetailSearchDTO, "订单详情对象不能为空");
        Preconditions.notNull(orderDetailSearchDTO.getFquantity(), "订单详情对象的商品数量不能为空");
        Integer cancelQuantity = orderDetailSearchDTO.getFcancelquantity() == null ? null : orderDetailSearchDTO.getFcancelquantity().intValue();
        return doGetRealOrderProductQuantity(orderDetailSearchDTO.getFquantity(), cancelQuantity, orderDetailSearchDTO.getReturnStatus());
    }

    public static int getRealOrderProductQuantity(OrderDetailDO orderDetailDO) {
        Preconditions.notNull(orderDetailDO, "订单详情对象不能为空");
        Preconditions.notNull(orderDetailDO.getFquantity(), "订单详情对象的商品数量不能为空");
        Integer cancelQuantity = orderDetailDO.getFcancelquantity() == null ? null : orderDetailDO.getFcancelquantity().intValue();
        return doGetRealOrderProductQuantity(orderDetailDO.getFquantity().intValue(), cancelQuantity, orderDetailDO.getReturnStatus());
    }

    private static int doGetRealOrderProductQuantity(int quantity, Integer cancelQuantity, Integer returnStatus) {
        //减去退货完成数量
        if (cancelQuantity != null && OrderDetailReturnStatus.SUCCESS.getCode().equals(returnStatus)) {
            return quantity - cancelQuantity;
        }
        return quantity;
    }

    /**
     * 获取订单详情的危化品类型
     * @param dangerousType
     * @return nullable
     */
    public static int getDangerousType(Integer dangerousType) {
        //危化品类型，如果没有，写死为“其他”
        if (dangerousType == null) {
            return DangerousTypeEnum.UN_DANGEROUS.getValue();
        } else {
            return dangerousType;
        }
    }
}
