package com.ruijing.store.warehouse.utils;

import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import org.apache.commons.lang3.StringUtils;

import java.text.DecimalFormat;

/**
 * <AUTHOR>
 */
public class PriceUtil {

    private final static String[] STRINGS = {"零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"};

    public static String formatDouble2TwoDecimal(Double d) {
        DecimalFormat df = new DecimalFormat("#.00");
        String str = df.format(d);
        //处理这个样的情况 .80
        if (str.indexOf(".") == 0) {
            str = "0" + str;
        }
        return str;
    }

    public static String convert(String amount) {
        // 如果是空字符串,返回""
        if (StringUtils.isBlank(amount)) {
            return "";
        }
        if (amount.startsWith(".")) {
            throw new BusinessInterceptException(ExecptionMessageEnum.AMOUNT_CANNOT_START_WITH_DOT);
        }
        int i = StringUtils.indexOf(amount, '.');
        String integerPart;
        String doublePart = null;
        if (i > 0) {
            //带有小数的金额
            String[] strings = amount.split("\\.");
            integerPart = strings[0];
            doublePart = strings[1];
        } else {
            //只有整数金额
            integerPart = amount;
        }
        //整数部分超过两位不能以0开头
        int integerPartLength = integerPart.length();
        if (integerPart.startsWith("0") && integerPartLength > 1) {
            throw new BusinessInterceptException(ExecptionMessageEnum.AMOUNT_CANNOT_START_WITH_ZERO, amount);
        }
        //整数部分最大只能12位
        if (integerPartLength > 12) {
            throw new BusinessInterceptException(ExecptionMessageEnum.AMOUNT_EXCEEDS_LIMIT, amount);
        }
        if (integerPart.startsWith("0") && integerPartLength == 1) {
            integerPart = "";
        }


        return integerPartTrans(integerPart) +
                doublePartTrans(doublePart);
    }

    /**
     * 整数部分转成中文大写
     * @param integerPart
     * @return
     */
    private static String integerPartTrans(String integerPart) {
        int length = integerPart.length();
        StringBuilder stringBuilder = new StringBuilder();
        String prefix = getPrefix(integerPart);
        if (!prefix.equals("0000")) {
            stringBuilder.append(transForm(prefix));
            stringBuilder.append(unitP(length));
        } else {
            stringBuilder.append(unitP(length));
        }
        int prefixLenth = prefix.length();
        if (length > prefixLenth) {
            String substring = integerPart.substring(prefixLenth, length);
            return stringBuilder.append(integerPartTrans(substring)).toString();
        } else {
            return stringBuilder.toString();
        }

    }

    /**
     * 整数每小部分部分转换大写
     * @param partAmount
     * @return
     */
    private static String transForm(String partAmount) {
        StringBuilder stringBuilder = new StringBuilder();
        int unitLenth = partAmount.length();


        for (int i = 0; i < partAmount.length(); i++) {
            int index = (int) partAmount.charAt(i) - 48;

            String string = STRINGS[index];
            if (string.equals(STRINGS[0])) {
                //避免出现3001转换成叁仟零零拾这种情况
                if (stringBuilder.toString().endsWith(STRINGS[0])) {
                    if (i == partAmount.length() - 1) {
                        //避免出现700转成柒佰零圆整的情况
                        stringBuilder.delete(stringBuilder.length() - 1, stringBuilder.length());
                    }
                    --unitLenth;
                } else {
                    if (i != partAmount.length() - 1) {
                        stringBuilder.append(string);
                    }
                    --unitLenth;
                }
            } else {

                stringBuilder.append(string).append(unit(--unitLenth));
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 小数部分转换大写,默认只保留两位
     * @param doublePart
     * @return
     */
    private static String doublePartTrans(String doublePart) {
        StringBuilder stringBuilder = new StringBuilder();
        if (StringUtils.isBlank(doublePart) || doublePart.startsWith("00")) {
            return stringBuilder.append("整").toString();
        }

        for (int i = 0; i < doublePart.length(); i++) {
            if (i == 2) {
                break;
            }
            //数字字符串转成int类型,然后获取相对应的大写单词
            int index = (int) doublePart.charAt(i) - 48;
            String string = STRINGS[index];
            if (!string.equals(STRINGS[0])) {
                stringBuilder.append(string).append(unitDecimal(i + 1));
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 小数部分每个位数的简称,默认只到分结束
     * @param lenth
     * @return
     */
    private static String unitDecimal(int lenth) {
        switch (lenth) {
            case 1:
                return "角";
            case 2:
                return "分";
            default:
                return "";
        }
    }

    /**
     * 每个部分的简称 eg:"9999"后面的万
     * @param length
     * @return
     */
    private static String unitP(int length) {
        if (length > 8) {
            return "亿";
        } else if (length > 4) {
            return "万";
        } else if (length > 0) {
            return "圆";
        } else {
            return "";
        }
    }

    /**
     * 每个位数上对应的简称
     * @param lenth
     * @return
     */
    private static String unit(int lenth) {
        switch (lenth) {
            case 3:
                return "仟";
            case 2:
                return "佰";
            case 1:
                return "拾";
            default:
                return "";

        }
    }

    /**
     * 整数部分从最末尾开始,按每4位拆分 eg:"1234567" 拆成"123" "4567",并返回数值的第一部分
     * @param amount
     * @return
     */
    private static String getPrefix(String amount) {
        int length = amount.length();
        return length > 4 ? getPrefix(amount.substring(0, length - 4)) : amount;
    }
}
