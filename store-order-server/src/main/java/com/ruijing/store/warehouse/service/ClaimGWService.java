package com.ruijing.store.warehouse.service;

import com.ruijing.store.warehouse.message.vo.claim.*;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/29 19:39
 */
public interface ClaimGWService {
    /**
     * 获取个人申领单列表
     * @param request
     * @return
     */
    ClaimPagingVO getPersonalClaimList(ClaimPersonalPageRequestVO request);

    /**
     * 获取部门申领单列表
     * @param request
     * @return
     */
    ClaimPagingVO getDeptClaimList(ClaimDeptPageRequestVO request);

    /**
     * 获取申领单详情
     * @param request
     * @return
     */
    ClaimDetailVO getClaimDetail(ClaimDetailRequestVO request);

    /**
     * 获取申领单常量列表
     * @return
     */
    ClaimConstantListVO getClaimConstantList();

    /**
     * 提交申领单
     * @param request
     * @return
     */
    boolean submitClaim(ClaimSubmitRequestVO request);

    /**
     * 根据部门获取库房列表
     * @param request
     * @return
     */
    List<WarehouseVO> getRoomListByDepartment(RoomListRequestVO request);

    /**
     * 根据库房和申领类型获取商品信息
     * @param request
     * @return
     */
    ClaimProductPagingVO getProductListByRoomAndClaimType(ProductListRequestVO request);

    /**
     * 取消申领
     * @param request
     * @return
     */
    boolean cancelClaim(ClaimDetailRequestVO request);

    /**
     * 获取申领单数量(个人)
     * @return
     */
    CountClaimVO getPersonalClaimCount();

    /**
     * 获取申领单数量（课题组）
     * @return
     */
    CountClaimVO getDepartmentClaimCount();

    /**
     * 重新提交申领单
     * @param request
     * @return
     */
    boolean reSubmitClaim(ClaimResubmitRequestVO request);
}
