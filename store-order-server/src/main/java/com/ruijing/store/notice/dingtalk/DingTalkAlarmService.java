package com.ruijing.store.notice.dingtalk;

import com.reagent.order.enums.OrderPushEventEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/13 10:09
 * @description 钉钉服务
 */
public interface DingTalkAlarmService {

    String ORDER_GROUP_WEB_HOOK = "https://oapi.dingtalk.com/robot/send?access_token=5629d13c84e64f9db17d699d7da948add164248e935060f895ef33c0e2ed3d8c";

    String DEFAULT_ALARM_PHONE = "18924554293";

    /**
     * 发送钉钉消息
     * @param webHook 钉钉对应的webHook
     * @param phoneList 需要通知到的人的手机号
     * @param content 发送内容
     * @param dingAll 是否通知所有人
     */
    void sendDingMessage(String webHook, List<String> phoneList, String content, boolean dingAll);
}
