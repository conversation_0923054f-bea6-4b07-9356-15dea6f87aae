package com.ruijing.store.notice.util;

import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.notice.enums.NoticeEventEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/6 9:50
 * @description 获取需要通知的appKey
 */
public class GetAppKeyToNoticeUtils {

    /**
     * 中大对应的appkey
     */
    public static final String SYSU_ORDER_SERVICE = "research-sysu-order-service";

    public static List<String> getAppKeyByOrgCodeAndEventType(String orgCode, NoticeEventEnum eventType) {
        switch (eventType) {
            case BUYER_APPLY_RETURN_ORDER_COMPLETE:
                // 申请退货完成
            case CANCEL_GOODS_RETURN_COMPLETE:
                // 取消退货完成
            case VERIFY_BEFORE_BUYER_APPLY_RETURN:
                // 申请退货前置校验
                if (ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgCode(orgCode) && !ZhongShanDaXueBelongUtils.isBanGongByOrgCode(orgCode)) {
                    // 中大但非中大办公，推到中大
                    return New.list(SYSU_ORDER_SERVICE);
                }
                return New.emptyList();
            default:
                return New.emptyList();
        }
    }
}
