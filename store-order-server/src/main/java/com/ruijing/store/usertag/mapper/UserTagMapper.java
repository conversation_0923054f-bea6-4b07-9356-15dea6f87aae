package com.ruijing.store.usertag.mapper;

import com.ruijing.store.order.base.minor.model.DangerousTagDO;
import com.ruijing.store.usertag.model.UserTagDO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/5/7 0007 15:24
 * @Version 1.0
 * @Desc:描述
 */
public interface UserTagMapper {

  List<UserTagDO> getUserCategoryTag(@Param("userIds")Collection<Integer> userIds);
}