package com.ruijing.store.usertag.rpc.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.FieldRangeDTO;
import com.ruijing.store.order.api.search.dto.FieldSortDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.util.ListUtils;
import com.ruijing.store.usertag.client.UserTagRpcServiceClient;
import com.ruijing.store.usertag.mapper.UserTagMapper;
import com.ruijing.store.usertag.model.UserTagDO;
import com.ruijing.user.tag.api.dto.*;
import com.ruijing.user.tag.api.service.UserTagImplRpcService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: zhukai
 * @date : 2023/9/13 18:34
 * @description: 用户标签Rpc
 */
@MSharpService
@CatAnnotation
public class UserTagRpcServiceImpl implements UserTagImplRpcService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserTagImplRpcService.class);

    @Resource
    private UserTagMapper userTagMapper;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private UserTagRpcServiceClient userTagRpcServiceClient;

    /**
     * 控制一次只能有一个导入，超过就报错
     */
    private Boolean isStart = Boolean.FALSE;


    @Override
    public RemoteResponse<UserTagUpScrollInfoDTO> upUserTagScroll(UserTagUpScrollRequest userTagUpScrollRequest) {
        List<Integer> userIds = userTagUpScrollRequest.getUserIds();
        Preconditions.notEmpty(userIds,"userIds不能为空~！");
        List<UserTagDO> userCategoryTag = userTagMapper.getUserCategoryTag(userIds);
        List<UserTagUpScrollDetailDTO> resultList = userCategoryTag.stream().map(tagDO -> {
            UserTagUpScrollDetailDTO dto = new UserTagUpScrollDetailDTO();
            dto.setUserId(tagDO.getUserId());
            dto.setTag(tagDO.getTag());
            dto.setFrequency(tagDO.getCountItem());
            return dto;
        }).collect(Collectors.toList());
        UserTagUpScrollInfoDTO UserTagUpScrollInfoDTO =new UserTagUpScrollInfoDTO();
        UserTagUpScrollInfoDTO.setList(resultList);
        return RemoteResponse.success(UserTagUpScrollInfoDTO);
    }

    @Override
    @ServiceLog(description = "遍历订单导入标签",serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public RemoteResponse startImport(ImportTaskRequest importTaskRequest) {
        if (isStart) {
            return RemoteResponse.custom().failure("有导入任务正在执行，请稍后重试~！");
        }
        isStart = Boolean.TRUE;
        Integer total = null;
        try {
            total = doImport(importTaskRequest.getParam());
            importTaskRequest.setTotal(Long.valueOf(total));
            if (total == 0) {
                //如果没有导入数据则认为导入失败
                importTaskRequest.setStatus(0);
            }
            userTagRpcServiceClient.endImport(importTaskRequest);
            LOGGER.info("总共执行了：{}", total);
        } catch (Exception e) {
            LOGGER.error("导入异常异常", e);
            return RemoteResponse.custom().setFailure("导入异常~！");
        } finally {
            isStart = Boolean.FALSE;
        }
        return RemoteResponse.success();
    }

    private Integer doImport( ImportTaskParam param){
        List<String> keyWords = param.getTags();
        Date startTime = param.getStartTime();
        String startStr = DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, startTime);
        Date endTime = param.getEndTime();
        String endStr = DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, endTime);
        OrderSearchParamDTO orderSearchParamDTO = new OrderSearchParamDTO();
        List<OrderMasterSearchDTO> recordList = null;
        Integer total = 0;
        int maxId = 0;
        Boolean flag = true;
        for (String keyWord : keyWords) {
            LOGGER.info("--初始化的关键字--【{}】", keyWord);
            maxId = 0;
            flag = true;
            while (flag) {
                FieldRangeDTO orderDateRangeDTO = new FieldRangeDTO("forderdate", startStr, endStr);
                FieldRangeDTO idRangeDTO = new FieldRangeDTO("id", maxId + "", null, false, false);
                orderSearchParamDTO.setFieldRangeList(New.list(orderDateRangeDTO, idRangeDTO));
                orderSearchParamDTO.setGoodsName(keyWord);
                orderSearchParamDTO.setPageSize(5000);
                FieldSortDTO fieldSortDTO = new FieldSortDTO("id", SortOrderEnum.ASC);
                List<FieldSortDTO> fieldSortDTOS = New.list(fieldSortDTO);
                orderSearchParamDTO.setFieldSortList(fieldSortDTOS);
                SearchPageResultDTO<OrderMasterSearchDTO> searchResultList = orderSearchBoostService.commonSearch(orderSearchParamDTO);
                LOGGER.info("[{}]的条数为: {}", keyWord, searchResultList.getTotalHits());
                recordList = searchResultList.getRecordList();
                total += recordList.size();
                if (CollectionUtils.isNotEmpty(recordList)) {
                    maxId = recordList.get(recordList.size() - 1).getId();
                    List<AddUserTagRequest> requestList = recordList.stream().map(OrderMasterSearchDTO::getFbuyerid).distinct().map(buyerId -> {
                        AddUserTagRequest addUserTagRequest = new AddUserTagRequest();
                        addUserTagRequest.setUserId(buyerId);
                        addUserTagRequest.setTag(keyWord);
                        addUserTagRequest.setTagType(2);
                        addUserTagRequest.setFirstSourceId(1000);
                        addUserTagRequest.setSecondSourceName("商品标签");
                        return addUserTagRequest;
                    }).collect(Collectors.toList());
                    List<List<AddUserTagRequest>> lists = ListUtils.splitCollection(200, requestList);
                    for (List<AddUserTagRequest> list : lists) {
                        userTagRpcServiceClient.addUserTag(list);
                    }
                }
                if (searchResultList.getTotalHits() < 5000) {
                    flag = false;
                }
            }
        }
        return total;
    }
}
