package com.ruijing.store.cancel.business.rpc.service;

import com.reagent.order.dto.response.OrderEventPushResultResponseDTO;

/**
 * @author: liwenyu
 * @createTime: 2023-03-16 15:16
 * @description: 取消订单相关回调处理
 **/
public interface CancelOrderCallbackService {

    /**
     * 处理回调
     * @param orderEventPushResultResponseDTO 回调入参
     */
    void handleCallback(OrderEventPushResultResponseDTO orderEventPushResultResponseDTO);
}
