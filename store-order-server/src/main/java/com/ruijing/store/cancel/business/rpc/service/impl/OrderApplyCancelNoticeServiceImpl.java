package com.ruijing.store.cancel.business.rpc.service.impl;

import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.api.outer.buyer.OrderApplyCancelNoticeService;
import com.reagent.order.dto.config.OrgDockingConfigDTO;
import com.reagent.order.dto.outer.buyer.OuterBuyerCommonProcessDTO;
import com.reagent.order.enums.config.OmsDockingConfigValueEnum;
import com.reagent.order.enums.config.OrderDockingStrategyEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.ApplyCancelOrderReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.CancelOrderReqDTO;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.business.service.CancelOrderManageService;
import com.ruijing.store.order.constant.CategoryConstant;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.gateway.fundcard.service.OrderFundCardService;
import com.ruijing.store.order.rpc.client.StatementPlatformClient;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/8 18:26
 * @description
 */
@MSharpService
public class OrderApplyCancelNoticeServiceImpl implements OrderApplyCancelNoticeService {

    /**
     * 待发货时能两段式取消订单的单位，即待发货时申请取消订单，需要卖家确认/拒绝
     */
    private final static List<String> TWO_PHASE_CANCEL_WHEN_WAITING_FOR_DELIVERY_ORG_LIST = New.list(OrgConst.HUA_NAN_NONG_YE_DA_XUE);


    /**
     * 强制关单单位
     */
    private static final List<String> FORCE_CLOSE_ORDER_ORG_LIST = New.list(OrgConst.HUA_NAN_SHI_FAN_DA_XUE, OrgConst.GUANG_ZHOU_SHI_DI_YI_REN_MIN_YI_YUAN);

    @Resource
    private CancelOrderManageService cancelOrderManageService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private OrderFundCardService orderFundCardService;

    @Override
    @ServiceLog(description = "外部管理平台申请取消订单", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> applyCancelOrder(OuterBuyerCommonProcessDTO outerBuyerCommonProcessDTO) {
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(outerBuyerCommonProcessDTO.getOrderNo());
        Preconditions.notNull(orderMasterDO, "没有找到订单号为"+ outerBuyerCommonProcessDTO.getOrderNo() + "的订单");
        OrgDockingConfigDTO config = dockingConfigCommonService.getConfig(orderMasterDO.getFusercode());
        if(dockingConfigCommonService.isNewDockingEnable(config, orderMasterDO, null)){
            BusinessErrUtil.isTrue(OmsDockingConfigValueEnum.APPLY_CANCEL_IN_EXTERNAL.name().equals(config.getOrderDockingConfigDTO().getOrderSyncBuyerApplyCancelOrder())
                            || OmsDockingConfigValueEnum.APPLY_CANCEL_IN_EXTERNAL.name().equals(config.getOrderDockingConfigDTO().getOrderSyncBuyerCancelOrderBeforeConfirm()),
                    DockingConstant.CONFIG_MISMATCH_HINT);
        }else {
            BusinessErrUtil.isTrue(dockingConfigCommonService.getIfNeedDocking(orderMasterDO, New.list(OrderDockingStrategyEnum.HANDLE_CANCEL_REQUEST_AND_NOTICE)), DockingConstant.CONFIG_MISMATCH_HINT);
        }
        ApplyCancelOrderReqDTO applyCancelOrderReqDTO = new ApplyCancelOrderReqDTO();
        applyCancelOrderReqDTO.setOrderId(orderMasterDO.getId());
        applyCancelOrderReqDTO.setFcancelreason(StringUtils.truncate(outerBuyerCommonProcessDTO.getReason(), DockingConstant.ORDER_APPROVAL_LOG_REASON_MAX_LENGTH));
        applyCancelOrderReqDTO.setStatus(OrderStatusEnum.PurchaseApplyToCancel.getValue());

        // 单位定制化逻辑
        handleOrgCustom(orderMasterDO, applyCancelOrderReqDTO);

        //强制关单单位
        if(FORCE_CLOSE_ORDER_ORG_LIST.contains(orderMasterDO.getFusercode())){
            // 华师特殊处理，待发货状态下，申请取消订单，直接关闭订单
            if(OrgConst.HUA_NAN_SHI_FAN_DA_XUE.equals(orderMasterDO.getFusercode())){
                Integer orderStatus = orderMasterDO.getStatus();
                if(OrderStatusEnum.WaitingForDelivery.getValue().equals(orderStatus)){
                    return closeOrder(outerBuyerCommonProcessDTO);
                }
            }
        }
        cancelOrderManageService.cancelOrderByThunder(applyCancelOrderReqDTO);
        return RemoteResponse.success(true);
    }

    @Override
    @ServiceLog(description = "外部管理平台同意取消订单", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> approveCancelOrder(OuterBuyerCommonProcessDTO outerBuyerCommonProcessDTO) {
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(outerBuyerCommonProcessDTO.getOrderNo());
        Preconditions.notNull(orderMasterDO, "没有找到订单号为"+ outerBuyerCommonProcessDTO.getOrderNo() + "的订单");
        OrgDockingConfigDTO config = dockingConfigCommonService.getConfig(orderMasterDO.getFusercode());
        if(dockingConfigCommonService.isNewDockingEnable(config, orderMasterDO, null)){
            BusinessErrUtil.isTrue(OmsDockingConfigValueEnum.HANDLE_CANCEL_REQUEST_IN_EXTERNAL.name().equals(config.getOrderDockingConfigDTO().getOrderSyncBuyerReplyCancelOrderResult()), DockingConstant.CONFIG_MISMATCH_HINT);
        }else {
            BusinessErrUtil.isTrue(dockingConfigCommonService.getIfNeedDocking(orderMasterDO, New.list(OrderDockingStrategyEnum.HANDLE_CANCEL_REQUEST_AND_NOTICE)), DockingConstant.CONFIG_MISMATCH_HINT);
        }
        cancelOrderManageService.agreeCancelOrder(getCancelReq(orderMasterDO));
        return RemoteResponse.success(true);
    }

    @Override
    @ServiceLog(description = "外部管理平台拒绝取消订单", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> rejectCancelOrder(OuterBuyerCommonProcessDTO outerBuyerCommonProcessDTO) {
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(outerBuyerCommonProcessDTO.getOrderNo());
        Preconditions.notNull(orderMasterDO, "没有找到订单号为"+ outerBuyerCommonProcessDTO.getOrderNo() + "的订单");
        OrgDockingConfigDTO config = dockingConfigCommonService.getConfig(orderMasterDO.getFusercode());
        if(dockingConfigCommonService.isNewDockingEnable(config, orderMasterDO, null)){
            BusinessErrUtil.isTrue(OmsDockingConfigValueEnum.HANDLE_CANCEL_REQUEST_IN_EXTERNAL.name().equals(config.getOrderDockingConfigDTO().getOrderSyncBuyerReplyCancelOrderResult()), DockingConstant.CONFIG_MISMATCH_HINT);
        }else {
            BusinessErrUtil.isTrue(dockingConfigCommonService.getIfNeedDocking(orderMasterDO, New.list(OrderDockingStrategyEnum.HANDLE_CANCEL_REQUEST_AND_NOTICE)), DockingConstant.CONFIG_MISMATCH_HINT);
        }
        cancelOrderManageService.refuseCancelOrder(getCancelReq(orderMasterDO));
        return RemoteResponse.success(true);
    }

    private CancelOrderReqDTO getCancelReq(OrderMasterDO orderMasterDO){
        CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setOrderMasterId(orderMasterDO.getId());
        cancelOrderReqDTO.setCancelMan(orderMasterDO.getFbuyername());
        cancelOrderReqDTO.setCancelManId(orderMasterDO.getFbuyerid().toString());
        cancelOrderReqDTO.setOrgCode(orderMasterDO.getFusercode());
        cancelOrderReqDTO.setOrgId(orderMasterDO.getFuserid());
        return cancelOrderReqDTO;
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> closeOrder(OuterBuyerCommonProcessDTO outerBuyerCommonProcessDTO) {
        BusinessErrUtil.notNull(outerBuyerCommonProcessDTO.getReason(), ExecptionMessageEnum.CLOSURE_REASON_REQUIRED);
        String reason = outerBuyerCommonProcessDTO.getReason();
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(outerBuyerCommonProcessDTO.getOrderNo());
        Preconditions.notNull(orderMasterDO, "没有找到订单号为"+ outerBuyerCommonProcessDTO.getOrderNo() + "的订单");
        OrgDockingConfigDTO config = dockingConfigCommonService.getConfig(orderMasterDO.getFusercode());
        if(dockingConfigCommonService.isNewDockingEnable(config, orderMasterDO, null)){
            boolean enableCancel = OmsDockingConfigValueEnum.APPLY_CANCEL_IN_EXTERNAL.name().equals(config.getOrderDockingConfigDTO().getOrderSyncBuyerApplyCancelOrder())
                    || OmsDockingConfigValueEnum.APPLY_CANCEL_IN_EXTERNAL.name().equals(config.getOrderDockingConfigDTO().getOrderSyncBuyerCancelOrderBeforeConfirm());
            //华师不读取配置
            if(!OrgConst.HUA_NAN_SHI_FAN_DA_XUE.equals(orderMasterDO.getFusercode())){
                BusinessErrUtil.isTrue(enableCancel && OmsDockingConfigValueEnum.YES.name().equals(config.getOrderDockingConfigDTO().getAllowCancelOrderIgnoreStatus()), DockingConstant.CONFIG_MISMATCH_HINT);
            }
        }else {
            BusinessErrUtil.isTrue(FORCE_CLOSE_ORDER_ORG_LIST.contains(orderMasterDO.getFusercode()), DockingConstant.CONFIG_MISMATCH_HINT);
        }

        if(OrderStatusEnum.Close.getValue().equals(orderMasterDO.getStatus())){
            return RemoteResponse.success();
        }
        List<Integer> orderIdList = New.list(orderMasterDO.getId());
        if(OrderStatusEnum.WaitingForStatement_1.getValue().equals(orderMasterDO.getStatus())){
            // 如果有待结算数据一并删除
            statementPlatformClient.deleteWaitingStatementByOrderId(orderIdList);
        } else if(OrderStatusEnum.Statementing_1.getValue().equals(orderMasterDO.getStatus())
                && orderMasterDO.getStatementId() != null){
            // 如果有结算单一并删除
            statementPlatformClient.cancelStatementOrSummary(orderMasterDO.getStatementId(), reason);
        }
        // 操作日志
        OrderApprovalLog orderApprovalLog = new OrderApprovalLog();
        orderApprovalLog.setOrderId(orderMasterDO.getId());
        orderApprovalLog.setOperatorId(DockingConstant.SYSTEM_OPERATOR_ID);
        // 按照状态|原因存放，用于展示
        orderApprovalLog.setReason(reason);
        orderApprovalLog.setApproveStatus(OrderApprovalEnum.ORDER_CLOSE.getValue());
        orderApprovalLogMapper.insertSelective(orderApprovalLog);
        // 更新为已关闭
        OrderMasterDO updateParam = new OrderMasterDO();
        Date nowDate = new Date();
        updateParam.setStatus(OrderStatusEnum.Close.getValue());
        updateParam.setShutDownDate(nowDate);
        updateParam.setFcancelreason(reason);
        orderMasterMapper.updateStatusByIdIn(updateParam, orderIdList);
        return RemoteResponse.success(true);
    }

    /**
     * 执行单位特殊逻辑
     * @param orderMasterDO                 订单信息
     * @param applyCancelOrderReqDTO        申请取消订单入参
     */
    private void handleOrgCustom(OrderMasterDO orderMasterDO, ApplyCancelOrderReqDTO applyCancelOrderReqDTO){
        // 暨大特殊逻辑
        if (isJiDaOrderChange(orderMasterDO)){
            Integer orderStatus = orderMasterDO.getStatus();
            // 订单已确认-待发货
            if (OrderStatusEnum.WaitingForDelivery.getValue().equals(orderStatus)) {
                applyCancelOrderReqDTO.setFcancelreason("此订单为对接订单，采购人在本单位的化学品管控系统进行了订单取消操作!");
            }
        }
    }

    /**
     * 判断是否为暨大的订单状态变更
     * @param orderMasterDO
     * @return
     */
    private boolean isJiDaOrderChange(OrderMasterDO orderMasterDO){
        if(OrgEnum.JI_NAN_DA_XUE.getValue() == orderMasterDO.getFuserid()){
            List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
            return orderDetailDOList.stream().anyMatch(orderDetailDO -> CategoryConstant.DANGEROUS_ID == orderDetailDO.getFirstCategoryId() ||
                    CategoryConstant.CONVENTIONAL_CHEMICAL_REAGENTS == orderDetailDO.getSecondCategoryId());
        }
        return false;
    }
}
