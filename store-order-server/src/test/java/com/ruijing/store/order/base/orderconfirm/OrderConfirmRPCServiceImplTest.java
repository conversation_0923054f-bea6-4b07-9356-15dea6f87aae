package com.ruijing.store.order.base.orderconfirm;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterCommonReqDTO;
import com.ruijing.store.order.api.base.other.dto.OrderConfirmForTheRecordDTO;
import com.ruijing.store.order.base.minor.mapper.OrderConfirmForTheRecordDOMapper;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.List;


public class OrderConfirmRPCServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderConfirmRPCServiceImpl orderConfirmRPCService;

    @Mock
    private OrderConfirmForTheRecordDOMapper orderConfirmForTheRecordDOMapper;

    @Test
    public void findOrderConfirmByOrderIdList() {
        OrderMasterCommonReqDTO request = new OrderMasterCommonReqDTO();
        request.setOrderMasterIds(Arrays.asList(1));
        Mockito.when(orderConfirmForTheRecordDOMapper.findByOrderIdIn(Mockito.any())).thenReturn(Arrays.asList());

        RemoteResponse<List<OrderConfirmForTheRecordDTO>> response = orderConfirmRPCService.findOrderConfirmByOrderIdList(request);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void saveOrderConfirm() {
        Mockito.when(orderConfirmForTheRecordDOMapper.countByOrderId(Mockito.any())).thenReturn(1);
        Mockito.when(orderConfirmForTheRecordDOMapper.insertSelective(Mockito.any())).thenReturn(1);
        Mockito.when(orderConfirmForTheRecordDOMapper.updateByOrderId(Mockito.any())).thenReturn(1);
        OrderConfirmForTheRecordDTO request = new OrderConfirmForTheRecordDTO();
        request.setOrderId(1);
        RemoteResponse<Integer> response = orderConfirmRPCService.saveOrderConfirm(request);
        Assert.assertTrue(response.isSuccess());

        Mockito.when(orderConfirmForTheRecordDOMapper.countByOrderId(Mockito.any())).thenReturn(0);
        response = orderConfirmRPCService.saveOrderConfirm(request);
        Assert.assertTrue(response.isSuccess());
    }
}