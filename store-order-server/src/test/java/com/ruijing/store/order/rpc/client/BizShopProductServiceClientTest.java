package com.ruijing.store.order.rpc.client;

import com.ruijing.shop.goods.api.dto.BaseProductDTO;
import com.ruijing.shop.goods.api.dto.ProductUniqDTO;
import com.ruijing.shop.goods.api.service.BizShopProductService;
import com.ruijing.shop.shopcommon.base.ApiResult;
import com.ruijing.store.MockBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Collections;
import java.util.List;

/**
 * @auther: Kimmy Tse
 * @Date: 2021/3/8 14:24
 * @Description:
 */
public class BizShopProductServiceClientTest extends MockBaseTestCase {

    @InjectMocks
    private BizShopProductServiceClient bizShopProductServiceClient;

    @Mock
    private BizShopProductService bizShopProductService;

    @Test
    public void testGetProductByIds() throws Exception {

        System.out.println(Runtime.getRuntime().availableProcessors());
        ApiResult<List<BaseProductDTO>> remoteResponse = ApiResult.newSuccess();
        remoteResponse.setData(Collections.emptyList());
        List<ProductUniqDTO> productUniqDTOS = Collections.emptyList();
        Mockito.when(bizShopProductService.getByIds(Mockito.any(List.class))).thenReturn(remoteResponse);
        List<BaseProductDTO> products = bizShopProductServiceClient.getProductByIds(productUniqDTOS);
        Assert.assertTrue(products.size() == 0);

        Mockito.when(bizShopProductService.getByIds(Mockito.any(List.class))).thenThrow(Exception.class);
        try {
            products = bizShopProductServiceClient.getProductByIds(productUniqDTOS);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof Exception);
        }
        Assert.assertTrue(products.size() == 0);

    }
}