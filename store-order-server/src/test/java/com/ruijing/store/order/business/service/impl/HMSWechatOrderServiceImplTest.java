package com.ruijing.store.order.business.service.impl;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

public class HMSWechatOrderServiceImplTest extends MockBaseTestCase {

    @Mock
    private BuyerOrderServiceImpl buyerOrderServiceImpl;

    @Mock
    private OrderSearchBoostService orderSearchBoostService;

    @InjectMocks
    private HMSWechatOrderServiceImpl hmsWechatOrderService;

    @Test
    public void getOrderCount() {
        Integer assumedOrderCount = 306;

        OrderListRequest request = new OrderListRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        LoginUserInfoBO loginInfo = new LoginUserInfoBO();

        OrderSearchParamDTO param = new OrderSearchParamDTO();
        param.setStatusList(New.list(OrderStatusEnum.WaitingForStatement_1.getValue()));
        Mockito.when(buyerOrderServiceImpl.constructOrderSearchParam(Mockito.any(OrderListRequest.class), Mockito.any(LoginUserInfoBO.class), Mockito.anyBoolean())).thenReturn(param);

        SearchPageResultDTO<OrderMasterSearchDTO> searchRes = new SearchPageResultDTO<>();
        searchRes.setTotalHits(assumedOrderCount.longValue());
        Mockito.when(orderSearchBoostService.commonSearch(Mockito.any())).thenReturn(searchRes);

        // 空部门
        loginInfo.setDeptIdList(New.list());
        Integer orderCount = hmsWechatOrderService.getOrderCountByStatus(loginInfo, request);
        Assert.assertEquals(0L, orderCount.longValue());

        // 有部门
        loginInfo.setDeptIdList(New.list(123,456));
        orderCount = hmsWechatOrderService.getOrderCountByStatus(loginInfo, request);
        Assert.assertEquals(assumedOrderCount.longValue(), orderCount.longValue());
    }
}