package com.ruijing.store.order.rpc.client;

import com.alibaba.testable.core.annotation.MockMethod;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.fundcard.api.FundCardRPCService;
import com.reagent.research.fundcard.dto.FreezeDTO;
import com.reagent.research.fundcard.dto.FundCardResultDTO;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.remoting.msharp.context.ProviderContext;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.store.MockBaseTestCase;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

public class ResearchFundCardServiceClientTest extends MockBaseTestCase {

    @InjectMocks
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @org.mockito.Mock
    private FundCardRPCService fundCardRPCService;

    public static class Mock {
        @MockMethod(targetClass = RpcContext.class)
        public static ProviderContext getProviderContext() {
            ProviderContext providerContext = ProviderContext.getProviderContext();
            HashMap<String, Object> callAttachments = new HashMap<>();
            RjSessionInfo rjSessionInfo = new RjSessionInfo();
            rjSessionInfo.setOrgId(2);
            callAttachments.put("RJ_SESSION_INFO", rjSessionInfo);
            providerContext.setCallAttachments(callAttachments);
            return providerContext;
        }

        @MockMethod(targetClass = ResearchFundCardServiceClient.class)
        public List<FundCardDTO> getFundCardListByCardIds(List<String> cardIds, String orgCode) {
            if(orgCode.equals("returnNull")){
                return null;
            }else{
                return New.list(new FundCardDTO());
            }
        }
    }

    @Test
    public void findCurrentCardByOrgCodeAndCardId() {
        RemoteResponse<List<FundCardDTO>> response = RemoteResponse.<List<FundCardDTO>>custom().setData(New.list()).setSuccess();
        Mockito.when(fundCardRPCService.getFundCardListByCardIds(Mockito.any(OrgRequest.class))).thenReturn(response);
        List<FundCardDTO> result = researchFundCardServiceClient.findCurrentCardByOrgCodeAndCardId(OrgEnum.WEN_YI_DA_REN_JI_XUE_YUAN.getCode(), New.list("6as5d4f-a6sd-asdf123"));
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void fundCardFreezeBatch() {
        FundCardResultDTO fundCardResultDTO = new FundCardResultDTO();
        fundCardResultDTO.setRelyAsyCallback("0");
        RemoteResponse<Object> response = RemoteResponse.custom().setSuccess().setData(fundCardResultDTO);
        Mockito.when(fundCardRPCService.fundCardFreezeBatch(Mockito.any(OrgRequest.class))).thenReturn(response);

        OrgRequest<FreezeDTO> request = new OrgRequest<>();
        FundCardResultDTO fundCardResultDTO1 = researchFundCardServiceClient.fundCardFreezeBatch(request);
        Assert.assertTrue("error", fundCardResultDTO1 != null);
    }

    @Test
    public void checkFundCardSufficient() {
        FundCardDTO fundCardDTO = new FundCardDTO();
        fundCardDTO.setBalanceAmount(new BigDecimal(1000));
       researchFundCardServiceClient.checkFundCardSufficient(fundCardDTO, new BigDecimal(100));
    }

    @Test
    public void getFundCardByCardId() {
        // 返回null
        FundCardDTO fundCardByCardId = researchFundCardServiceClient.getFundCardByCardId("returnNull", "returnNull");
        Assert.assertTrue(fundCardByCardId == null);

        // 返回第一个
        fundCardByCardId = researchFundCardServiceClient.getFundCardByCardId("returnNotNull", "returnNotNull");
        Assert.assertTrue(fundCardByCardId != null);
    }

    @Test
    public void getFundCardListByCardIds() {
        Mockito.when(fundCardRPCService.getFundCardListByCardIds(Mockito.any())).
                thenReturn(RemoteResponse.<List<FundCardDTO>>custom()
                        .setSuccess().setData(New.list(new FundCardDTO())).build());
        List<FundCardDTO> fundCardListByCardIds =
                researchFundCardServiceClient.getFundCardListByCardIds(Mockito.anyListOf(String.class), "");
        Assert.assertTrue(CollectionUtils.isNotEmpty(fundCardListByCardIds));
    }
}