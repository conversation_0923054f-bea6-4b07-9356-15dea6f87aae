package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.shop.shopcommon.base.ApiResult;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.wms.api.dto.BizWarehouseExitDTO;
import com.ruijing.store.wms.api.service.BizExitService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.List;

public class BizExitServiceClientTest extends MockBaseTestCase {

    @InjectMocks
    private BizExitServiceClient bizExitServiceClient;

    @Mock
    private BizExitService bizExitService;

    @Test
    public void testQueryExitByOrderNoList() {
        // mock input
        List<String> orderNoList = New.list("DC202103180001");
        List<BizWarehouseExitDTO> resultList = New.list(new BizWarehouseExitDTO(){{setId(456);}});
        ApiResult<List<BizWarehouseExitDTO>> listApiResult = new ApiResult<List<BizWarehouseExitDTO>>(){{setData(resultList);}};
        Mockito.when(bizExitService.queryExitByOrderNoList(Mockito.anyList())).thenReturn(listApiResult);

        List<BizWarehouseExitDTO> bizWarehouseExitDTOS = bizExitServiceClient.queryExitByOrderNoList(orderNoList);
        Assert.assertTrue(CollectionUtils.isNotEmpty(bizWarehouseExitDTOS));
    }
}