package com.ruijing.store.order.business.service.impl;

import com.reagent.research.statement.api.statement.dto.StatementResultDTO;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.rpc.client.ResearchStatementClient;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import com.ruijing.store.order.statement.service.WaitingStatementService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;

/**
 * @auther: <PERSON><PERSON> Tse
 * @Date: 2021/3/30 18:24
 * @Description:
 */
public class OrderMasterCommonServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderMasterCommonServiceImpl orderMasterCommonService;

    @Mock
    private SysConfigClient sysConfigClient;

    @Mock
    private ResearchStatementClient researchStatementClient;

    @Mock
    private OrderMasterMapper orderMasterMapper;

    @Mock
    private WaitingStatementService waitingStatementService;

    @Test
    public void testGetPersonalizedInvoice() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
        updateOrderParamDTO.setOrderId(1);

        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setId(1);
        orderMasterDO.setForderno("123");

        HashMap<String, String> receiptConfigMap = new HashMap<>();
        receiptConfigMap.put("ONLINE_ORDER_USE_STATEMENT_SYSTEM", "1");
        Class<OrderMasterCommonServiceImpl> OrderMasterCommonServiceImplClass = (Class<OrderMasterCommonServiceImpl>) orderMasterCommonService.getClass();
        Method inBoundCallBack = OrderMasterCommonServiceImplClass.getDeclaredMethod("inBoundCallBack", UpdateOrderParamDTO.class, OrderMasterDO.class);
        boolean accessible = inBoundCallBack.isAccessible();
        inBoundCallBack.setAccessible(true);
        Mockito.when(sysConfigClient.isAutoStatement(Mockito.any(String.class))).thenReturn(true);
        Mockito.when(sysConfigClient.getConfigMapByOrgCodeAndConfigCode(Mockito.any(String.class), Mockito.anyList())).thenReturn(new HashMap<String, String>());
        //todo 私有方法测试
        Mockito.when(researchStatementClient.createStatementSingle(Mockito.any(Integer.class), Mockito.any(String.class), Mockito.any(OrderMasterDO.class))).thenReturn(new StatementResultDTO());
        Mockito.when(orderMasterMapper.updateOrderById(Mockito.any(UpdateOrderParamDTO.class))).thenReturn(1);
        Mockito.doNothing().when(waitingStatementService).pushWaitingStatement(Mockito.any(String.class), Mockito.anyList());

        inBoundCallBack.invoke(orderMasterCommonService, updateOrderParamDTO, orderMasterDO);
        // return accessible
        inBoundCallBack.setAccessible(accessible);
    }
}