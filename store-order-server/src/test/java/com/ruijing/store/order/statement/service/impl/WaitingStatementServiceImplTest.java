package com.ruijing.store.order.statement.service.impl;

import com.alibaba.testable.core.annotation.MockMethod;
import com.reagent.research.statement.api.order.dto.WaitingStatementOrderRequestDTO;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.util.OrderCommonUtils;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.rpc.client.StatementPlatformClient;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

// todo zyl
public class WaitingStatementServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private WaitingStatementServiceImpl waitingStatementService;

    @org.mockito.Mock
    private StatementPlatformClient statementPlatformClient;

    @org.mockito.Mock
    private SysConfigClient sysConfigClient;

    @org.mockito.Mock
    private OrderMasterMapper orderMasterMapper;

    @org.mockito.Mock
    private OrderDetailMapper orderDetailMapper;

    @org.mockito.Mock
    private RefFundcardOrderService refFundcardOrderService;

    @org.mockito.Mock
    private OrderManageService orderManageService;

    static class Mock {
        @MockMethod(targetClass = WaitingStatementServiceImpl.class)
        public List<WaitingStatementOrderRequestDTO> waitingStatementGenerate(List<Integer> orderIdList, String orgCode) {
            WaitingStatementOrderRequestDTO item = new WaitingStatementOrderRequestDTO();
            item.setOrderId(100);
            item.setSpecies(0);

            return Arrays.asList(item);
        }

        @MockMethod(targetClass = OrderCommonUtils.class)
        public static boolean isStatementTimeOut(OrderMasterDO order, List<OrderDetailDO> detailList, Map<String, Integer> timeOutConfigMap) {
            return true;
        }
    }

    @Test
    public void pushWaitingStatement() throws Exception {
        Mockito.when(statementPlatformClient.saveWaitingStatement(Mockito.anyList())).thenReturn(new ArrayList());
        Mockito.when(sysConfigClient.getConfigByOrgCodeAndConfigCode(Mockito.anyString(), Mockito.anyString())).thenReturn("0");
        waitingStatementService.pushWaitingStatement("SHEN_ZHEN_SHI_FU_YOU_BAO_JIAN_YUAN", new ArrayList<>(100));
    }


    @Test
    public void pushWaitingStatement2() throws Exception {
        Mockito.when(statementPlatformClient.saveWaitingStatement(Mockito.anyList())).thenReturn(new ArrayList());
        Mockito.when(sysConfigClient.getConfigByOrgCodeAndConfigCode(Mockito.anyString(), Mockito.anyString())).thenReturn("0");

        UpdateOrderParamDTO updated = new UpdateOrderParamDTO();
        updated.setInventoryStatus(9);
        waitingStatementService.pushWaitingStatement("SHEN_ZHEN_SHI_FU_YOU_BAO_JIAN_YUAN", new ArrayList<>(100), 9);
        waitingStatementService.pushWaitingStatement("GUANG_XI_ZHONG_LIU", new ArrayList<>(100), 9);
        waitingStatementService.pushWaitingStatement("ZHONG_SHAN_DA_XUE", new ArrayList<>(100), 9);
        Thread.sleep(10000);
    }
}
