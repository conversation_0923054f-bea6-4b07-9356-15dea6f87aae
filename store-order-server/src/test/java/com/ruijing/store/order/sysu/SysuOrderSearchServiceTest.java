package com.ruijing.store.order.sysu;

import com.reagent.research.custom.enums.ZhongShanDaXueOrgEnum;
import com.ruijing.fundamental.common.util.GsonUtil;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.sysu.dto.OrderDeptDeliveryDateDTO;
import com.ruijing.store.order.api.sysu.dto.OrderReceiptApproveQueryDTO;
import com.ruijing.store.order.api.sysu.dto.OrderRequestDTO;
import com.ruijing.store.order.api.sysu.service.SysuOrderSearchService;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.annotation.Commit;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @author: <PERSON> <PERSON>dong
 * @date: 2022/6/15 16:39
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ComponentScan(basePackages = {
        "com.reagent.research",
})
@Commit
public class SysuOrderSearchServiceTest {

    @Resource
    private SysuOrderSearchService sysuOrderSearchService;

    @Test
    public void overtimeSearch(){
        OrderRequestDTO orderRequestDTO = new OrderRequestDTO();
        orderRequestDTO.setPageNo(1);
        orderRequestDTO.setPageSize(20);
        orderRequestDTO.setOrgId(ZhongShanDaXueOrgEnum.ZHONG_SHAN_DA_XUE.getOrgId());
//        orderRequestDTO.setNo("DC202303274921401");
//        orderRequestDTO.setDepartmentId(1482);
//        orderRequestDTO.setUserSearch("任间");
        orderRequestDTO.setCardAuthAccess(Boolean.TRUE);
//        orderRequestDTO.setDepartmentIds(Lists.newArrayList(1482, 1479));
        List<Integer> departmentIds = new ArrayList<>();
//        for(int i =0 ; i<200;i++){
            departmentIds.addAll(Lists.newArrayList(29045, 27777, 22471));
//        }
        orderRequestDTO.setDepartmentIds(departmentIds);
        orderRequestDTO.setDeliveryStartDate("2019-01-01 00:00:00");
        orderRequestDTO.setDeliveryEndDate("2023-08-30 23:59:59");
        OrderDeptDeliveryDateDTO orderDeptDeliveryDateDTO = new OrderDeptDeliveryDateDTO();
        orderDeptDeliveryDateDTO.setDepartmentId(263);
        orderDeptDeliveryDateDTO.setDeliveryStartDate("2019-01-01 00:00:00");
        orderDeptDeliveryDateDTO.setDeliveryEndDate("2024-08-01 23:59:59");
        orderRequestDTO.setOrderDeptDeliveryDateDtoList(Collections.singletonList(orderDeptDeliveryDateDTO));

        orderRequestDTO.setStatusList(Lists.newArrayList(5,20,22));
        orderRequestDTO.setProcessSpecies(0);

        System.out.println(GsonUtil.toJson(sysuOrderSearchService.overTimeOrderSearch(orderRequestDTO)));
    }

    @Test
    public void orderListSearch (){
        OrderRequestDTO orderRequestDTO = new OrderRequestDTO();
        orderRequestDTO.setPageNo(1);
        orderRequestDTO.setPageSize(10);
        orderRequestDTO.setOrgId(ZhongShanDaXueOrgEnum.ZHONG_SHAN_DA_XUE.getOrgId());
//        orderRequestDTO.setOrgIds(Lists.newArrayList(3, 62));
//        orderRequestDTO.setOrgId(3);
//        orderRequestDTO.setOrderNoList(Lists.newArrayList("DC202303224945001"));
//        orderRequestDTO.setUserId(2455);
//        List<String> orderNoList = Lists.newArrayList("DC202206144094701","DC202206144094801");
//        List<String> orderNoList = Lists.newArrayList("DC202303301421802");
//        orderRequestDTO.setOrderNoList(orderNoList);
//        orderRequestDTO.setDepartmentId(263);
//        orderRequestDTO.setDepartmentIds(Lists.newArrayList(263));
//        orderRequestDTO.setDeliveryStartDate("2019-01-01 00:00:00");
//        orderRequestDTO.setDeliveryEndDate("2023-08-30 23:59:59");
//        orderRequestDTO.setStatusList(Lists.newArrayList(5,20,22));
//        orderRequestDTO.setOrderStartTime("201-10-01 00:00:00");
//        orderRequestDTO.setOrderEndTime("2023-04-27 17:21:59");
//        orderRequestDTO.setOrderNoList(Lists.newArrayList("DC202302244644101"));
//        orderRequestDTO.setExcludeOrderList(Lists.newArrayList(""));
//        orderRequestDTO.setOrderNoList(Lists.newArrayList("DC202304032493301"));
//        orderRequestDTO.setDangerousTypeList(Lists.newArrayList(1,2,3));
//        orderRequestDTO.setNo("DC202206144094701");orderListSearch
        List<OrderReceiptApproveQueryDTO> orderReceiptApproveQueryDTOS = new ArrayList<>();
        OrderReceiptApproveQueryDTO orderReceiptApproveQueryDTO1 = new OrderReceiptApproveQueryDTO(OrderStatusEnum.OrderReceiveApproval.getValue(), Collections.singletonList(263));
        OrderReceiptApproveQueryDTO orderReceiptApproveQueryDTO2 = new OrderReceiptApproveQueryDTO(OrderStatusEnum.OrderReceiveApprovalTwo.getValue(), Collections.singletonList(263));
        orderRequestDTO.setOrderReceiptApproveQueryDTOS(Lists.newArrayList(orderReceiptApproveQueryDTO1, orderReceiptApproveQueryDTO2));
        System.out.println(GsonUtil.toJson(sysuOrderSearchService.orderListSearch(orderRequestDTO)));
    }

    @Test
    public void collegeIdsTest(){
        OrderRequestDTO orderRequestDTO = new OrderRequestDTO();
        orderRequestDTO.setPageNo(1);
        orderRequestDTO.setPageSize(10);
        orderRequestDTO.setOrderNoList(Lists.newArrayList("DC202311153184201", "DC202212283344001"));
        orderRequestDTO.setOrgId(ZhongShanDaXueOrgEnum.ZHONG_SHAN_DA_XUE.getOrgId());
        orderRequestDTO.setCollegeIds(Lists.newArrayList( 1390, 18252));
        System.out.println(GsonUtil.toJson(sysuOrderSearchService.orderListSearch(orderRequestDTO)));
    }

}
