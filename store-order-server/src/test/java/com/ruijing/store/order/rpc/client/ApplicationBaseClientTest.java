//package com.ruijing.store.order.rpc.client;
//
//import com.ruijing.fundamental.api.remote.RemoteResponse;
//import com.ruijing.fundamental.common.collections.New;
//import com.ruijing.store.MockBaseTestCase;
//import com.ruijing.store.apply.dto.ApplicationDetailDTO;
//import com.ruijing.store.apply.dto.ApplicationMasterDTO;
//import com.ruijing.store.apply.dto.ApplicationQueryDTO;
//import com.ruijing.store.apply.dto.ApplicationSaveDTO;
//import com.ruijing.store.apply.dto.application.ApplyRefSuppBusinessDTO;
//import com.ruijing.store.apply.dto.offline.OfflineExtraDTO;
//import com.ruijing.store.apply.dto.offline.OfflineProductDTO;
//import com.ruijing.store.apply.service.application.ApplicationBaseService;
//import com.ruijing.store.apply.service.application.ApplyRefSuppBusinessService;
//import com.ruijing.store.apply.service.offline.ApplicationOfflineService;
//import com.ruijing.store.apply.service.offline.ApplyOfflineProductService;
//import org.junit.Assert;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.List;
//
///**
// * @auther: Kimmy Tse
// * @Date: 2021/3/9 15:58
// * @Description:
// */
//// todo zyl
//public class ApplicationBaseClientTest extends MockBaseTestCase {
//
//    @InjectMocks
//    private ApplicationBaseClient applicationBaseClient;
//
//    @Mock
//    private ApplyOfflineProductService applyOfflineProductService;
//
//    @Mock
//    private ApplicationBaseService applicationBaseService;
//
//    @Mock
//    private ApplicationOfflineService applicationOfflineService;
//
//    @Mock
//    private ApplyRefSuppBusinessService applyRefSuppBusinessService;
//
//    public void testSaveApplicationMaster() {
//        RemoteResponse<List<ApplicationDetailDTO>> remoteResponse = RemoteResponse.<List<ApplicationDetailDTO>>custom().setSuccess();
//        RemoteResponse<List<ApplicationMasterDTO>> remoteResponse2 = RemoteResponse.<List<ApplicationMasterDTO>>custom().setSuccess().setData(new ArrayList<>());
//        remoteResponse.setData(Collections.emptyList());
//        ApplicationSaveDTO dto = new ApplicationSaveDTO();
//        Mockito.when(applicationBaseService.saveApplicationDetail(Mockito.any(ApplicationSaveDTO.class))).thenReturn(remoteResponse);
//        Mockito.when(applicationBaseService.saveApplicationMaster(Mockito.any(ApplicationSaveDTO.class))).thenReturn(remoteResponse2);
//        applicationBaseClient.saveApplicationMaster(dto);
//
//        Mockito.when(applicationBaseService.saveApplicationDetail(Mockito.any(ApplicationSaveDTO.class))).thenThrow(Exception.class);
//        try {
//            applicationBaseClient.saveApplicationMaster(dto);
//        } catch (Exception e) {
//            Assert.assertTrue(e instanceof Exception);
//        }
//    }
//
//    public void testFindByMasterId() {
//        RemoteResponse<List<ApplicationMasterDTO>> remoteResponse = RemoteResponse.<List<ApplicationMasterDTO>>custom().setSuccess();
//        remoteResponse.setData(Collections.emptyList());
//        ApplicationQueryDTO dto = new ApplicationQueryDTO();
//        Mockito.when(applicationBaseService.listApplicationMaster(Mockito.any(ApplicationQueryDTO.class))).thenReturn(remoteResponse);
//        List<ApplicationMasterDTO> byMasterId = applicationBaseClient.findByMasterId(dto);
//        Assert.assertTrue(byMasterId.size() == 0);
//
//        Mockito.when(applicationBaseService.listApplicationMaster(Mockito.any(ApplicationQueryDTO.class))).thenThrow(Exception.class);
//        try {
//            byMasterId = applicationBaseClient.findByMasterId(dto);
//        } catch (Exception e) {
//            Assert.assertTrue(e instanceof Exception);
//        }
//        Assert.assertTrue(byMasterId.size() == 0);
//    }
//
//    public void testFindDetailByMasterId() {
//        RemoteResponse<List<ApplicationDetailDTO>> remoteResponse = RemoteResponse.<List<ApplicationDetailDTO>>custom().setSuccess();
//        remoteResponse.setData(Collections.emptyList());
//        ApplicationQueryDTO dto = new ApplicationQueryDTO();
//        Mockito.when(applicationBaseService.listApplicationDetail(Mockito.any(ApplicationQueryDTO.class))).thenReturn(remoteResponse);
//        List<ApplicationDetailDTO> detailByMasterId = applicationBaseClient.findDetailByMasterId(dto);
//        Assert.assertTrue(detailByMasterId.size() == 0);
//
//        Mockito.when(applicationBaseService.listApplicationDetail(Mockito.any(ApplicationQueryDTO.class))).thenThrow(Exception.class);
//        try {
//            detailByMasterId = applicationBaseClient.findDetailByMasterId(dto);
//        } catch (Exception e) {
//            Assert.assertTrue(e instanceof Exception);
//        }
//        Assert.assertTrue(detailByMasterId.size() == 0);
//    }
//
//    public void testFindOfflineByAppIdList() {
//        RemoteResponse<List<OfflineExtraDTO>> remoteResponse = RemoteResponse.<List<OfflineExtraDTO>>custom().setSuccess();
//        remoteResponse.setData(Collections.emptyList());
//        List<Integer> param = New.list(1, 2, 3);
//        ApplicationQueryDTO dto = new ApplicationQueryDTO();
//        Mockito.when(applicationOfflineService.listByApplicationIds(Mockito.any(List.class))).thenReturn(remoteResponse);
//        List<OfflineExtraDTO> offlineByAppIdList = applicationBaseClient.findOfflineByAppIdList(param);
//        Assert.assertTrue(offlineByAppIdList.size() == 0);
//
//        Mockito.when(applicationBaseService.listApplicationDetail(Mockito.any(ApplicationQueryDTO.class))).thenThrow(Exception.class);
//        try {
//            offlineByAppIdList = applicationBaseClient.findOfflineByAppIdList(param);
//        } catch (Exception e) {
//            Assert.assertTrue(e instanceof Exception);
//        }
//        Assert.assertTrue(offlineByAppIdList.size() == 0);
//    }
//
//    public void testFindProcureChannelBySuppIdAndAppId() {
//        RemoteResponse<List<ApplyRefSuppBusinessDTO>> remoteResponse = RemoteResponse.<List<ApplyRefSuppBusinessDTO>>custom().setSuccess();
//        ApplyRefSuppBusinessDTO dto = new ApplyRefSuppBusinessDTO();
//        dto.setProcurementChannelId(1);
//        remoteResponse.setData(New.list(dto));
//        Integer suppId = 1;
//        Integer applicationId = 1;
//        Mockito.when(applyRefSuppBusinessService.listBySuppIdsAndApplicationIds(Mockito.any(List.class), Mockito.any(List.class))).thenReturn(remoteResponse);
//        Integer procureChannelBySuppIdAndAppId = applicationBaseClient.findProcureChannelBySuppIdAndAppId(suppId, applicationId);
//        Assert.assertTrue(procureChannelBySuppIdAndAppId != null);
//
//        Mockito.when(applyRefSuppBusinessService.listBySuppIdsAndApplicationIds(Mockito.any(List.class), Mockito.any(List.class))).thenThrow(Exception.class);
//
//        try {
//            procureChannelBySuppIdAndAppId = applicationBaseClient.findProcureChannelBySuppIdAndAppId(suppId, applicationId);
//        } catch (Exception e) {
//            Assert.assertTrue(e instanceof Exception);
//        }
//        Assert.assertTrue(procureChannelBySuppIdAndAppId != null);
//    }
//
//    public void testFindApplyOfflineProductByIds() {
//        RemoteResponse<List<OfflineProductDTO>> remoteResponse = RemoteResponse.<List<OfflineProductDTO>>custom().setSuccess();
//        remoteResponse.setData(Collections.emptyList());
//        List<Long> productIds = New.list(1L,2L,3L);
//        Mockito.when(applyOfflineProductService.listByIds(Mockito.any(List.class))).thenReturn(remoteResponse);
//        List<OfflineProductDTO> applyOfflineProductByIds = applicationBaseClient.findApplyOfflineProductByIds(productIds);
//        Assert.assertTrue(applyOfflineProductByIds.size() == 0);
//
//        Mockito.when(applyOfflineProductService.listByIds(Mockito.any(List.class))).thenThrow(Exception.class);
//        try {
//            applyOfflineProductByIds = applicationBaseClient.findApplyOfflineProductByIds(productIds);
//        } catch (Exception e) {
//            Assert.assertTrue(e instanceof Exception);
//        }
//        Assert.assertTrue(applyOfflineProductByIds.size() == 0);
//    }
//
//
//
//    public void testGetApplicationMasterByApplyId() {
//        RemoteResponse<List<ApplicationMasterDTO>> response = new RemoteResponse<>();
//        response.setData(New.list(new ApplicationMasterDTO()));
//        response.setSuccess();
//        Mockito.when(applicationBaseService.listApplicationMaster(Mockito.any(ApplicationQueryDTO.class))).thenReturn(response);
//        ApplicationMasterDTO applicationMasterByApplyId = null;
//        try {
//            applicationMasterByApplyId = applicationBaseClient.getApplicationMasterByApplyId(1, true);
//        } catch (Exception e) {
//            Assert.assertTrue("根据采购申请单id获取采购申请单失败",false);
//        }
//
//    }
//}