package com.ruijing.store.order.base.docking.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.statement.api.invoice.dto.InvoiceDTO;
import com.reagent.research.statement.api.invoice.dto.InvoiceRefResultDTO;
import com.reagent.research.statement.api.statement.dto.StatementResultDTO;
import com.reagent.supp.api.suppbank.dto.SuppBankInfoDTO;
import com.reagent.supp.api.supplier.dto.SupplierDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.order.api.base.docking.dto.ThirdPartQueryRequestDTO;
import com.ruijing.store.order.api.base.docking.dto.ThirdPartUpdateOrderRequestDTO;
import com.ruijing.store.order.api.base.docking.dto.ThirdPartUpdateRequestDTO;
import com.ruijing.store.order.api.base.docking.dto.ThirdPartUpdateResponseDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.ReceiptOrderResponseDO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.api.general.dto.FundCardSearchDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderIdInvoiceTitleDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.util.OrderCommonUtils;
import com.ruijing.store.order.business.service.OrderAcceptService;
import com.ruijing.store.order.business.service.OrderMasterForTPIService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


public class OrderForThirdPartRPCServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderForThirdPartRPCServiceImpl orderForThirdPartRPCService;

    @Mock
    private OrderMasterForTPIService orderMasterForTPIService;

    @Mock
    private OrderMasterMapper orderMasterMapper;

    @Mock
    private OrderAcceptService orderAcceptService;

    @Mock
    private BusinessDockingRPCClient businessDockingRPCClient;

    @Mock
    private ApplicationBaseClient applicationBaseClient;

    @Mock
    private CacheClient cacheClient;

    @Mock
    private OrderSearchBoostService orderSearchBoostService;

    @Mock
    private RefFundcardOrderService refFundcardOrderService;

    @Mock
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Mock
    private InvoiceClient invoiceClient;

    @Mock
    private SuppClient suppClient;

    @Mock
    private ResearchStatementClient researchStatementClient;

    @Mock
    private OrderDetailMapper orderDetailMapper;

    @Mock
    private UserClient userClient;

    @Mock
    private OrderOtherLogClient orderOtherLogClient;

    @Test
    public void thirdPartyUpdateOrder() {
        Mockito.when(orderMasterMapper.updateFieldByForderno(Mockito.anyList())).thenReturn(1);
        Mockito.when(orderMasterMapper.findByFordernoIn(Mockito.anyList())).thenReturn(Arrays.asList(new OrderMasterDO()));

        OrderMasterDO orderMaster = new OrderMasterDO();
        orderMaster.setForderno("DC202103221306401");
        orderMaster.setStatus(5);
        Mockito.when(orderMasterMapper.findByForderno(Mockito.any())).thenReturn(orderMaster);
        orderMaster.setStatus(8);
        Mockito.when(orderMasterMapper.findByFordernoIn(Mockito.anyList())).thenReturn(Arrays.asList(orderMaster));
        Mockito.when(orderMasterForTPIService.updateThirdPlatformOrder(Mockito.any())).thenReturn(true);
        Mockito.when(orderAcceptService.userAcceptOrder(Mockito.any())).thenReturn(new ReceiptOrderResponseDO());
        Mockito.when(applicationBaseClient.saveApplicationMaster(Mockito.any())).thenReturn(1);
        Mockito.when(businessDockingRPCClient.saveBusinessOrders(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt(), Mockito.any(), Mockito.any())).thenReturn(1);
        Mockito.when(cacheClient.lockRetry(Mockito.anyString(), Mockito.anyInt())).thenReturn(true);
        Mockito.doNothing().when(cacheClient).removeCache(Mockito.anyString());
        Mockito.doNothing().when(orderOtherLogClient).createOrderDockingLog(Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
        OrderCommonUtils.matchOrderDeliveryAddress("广东省广州市", OrderCommonUtils.PROVINCE_ANALYSIS);
        OrderCommonUtils.matchOrderDeliveryAddress("广东", OrderCommonUtils.PROVINCE_ANALYSIS);
        OrderCommonUtils.matchOrderDeliveryAddress("广东", OrderCommonUtils.AUTONOMY_ANALYSIS);
        OrderCommonUtils.matchOrderDeliveryAddress("广东自治区广州市", OrderCommonUtils.CITY_ANALYSIS);
        OrderCommonUtils.matchOrderDeliveryAddress("广东省广州市", OrderCommonUtils.CITY_ANALYSIS);
        OrderCommonUtils.matchOrderDeliveryAddress("广东省广州市龙江县", OrderCommonUtils.COUNTY_ANALYSIS);
        OrderCommonUtils.matchOrderDeliveryAddress("广东省龙江县", OrderCommonUtils.COUNTY_ANALYSIS);
        OrderCommonUtils.matchOrderDeliveryAddress("广东自治区广州市", OrderCommonUtils.AUTONOMY_ANALYSIS);

        ThirdPartUpdateRequestDTO request = new ThirdPartUpdateRequestDTO();
        request.setOrgCode(DockingConstant.GUANG_ZHOU_YI_KE_DA_XUE);
        List<ThirdPartUpdateOrderRequestDTO> dtoList = new ArrayList<>();
        ThirdPartUpdateOrderRequestDTO item = new ThirdPartUpdateOrderRequestDTO();
        item.setStatus(6);
        item.setOrderNo("DC202103221306401");
        dtoList.add(item);
        request.setThirdPartUpdateOrderRequestDTOList(dtoList);

        RemoteResponse<ThirdPartUpdateResponseDTO<String>> response = orderForThirdPartRPCService.thirdPartyUpdateOrder(request);
        Assert.assertTrue("成功", response.isSuccess());

        ThirdPartUpdateOrderRequestDTO item2 = new ThirdPartUpdateOrderRequestDTO();
        item2.setStatus(3);
        item2.setOrderNo("DC202103221306401");
        dtoList.add(item2);
        request.setThirdPartUpdateOrderRequestDTOList(dtoList);
        response = orderForThirdPartRPCService.thirdPartyUpdateOrder(request);
        Assert.assertTrue("成功", response.isSuccess());

        ThirdPartUpdateOrderRequestDTO item3 = new ThirdPartUpdateOrderRequestDTO();
        item3.setStatus(8);
        item3.setOrderNo("DC202103221306401");
        dtoList.add(item3);
        request.setThirdPartUpdateOrderRequestDTOList(dtoList);
        response = orderForThirdPartRPCService.thirdPartyUpdateOrder(request);
        Assert.assertTrue("成功", response.isSuccess());

        ThirdPartUpdateOrderRequestDTO item4 = new ThirdPartUpdateOrderRequestDTO();
        item4.setStatus(5);
        item4.setOrderNo("DC202103221306401");
        dtoList.add(item4);
        request.setThirdPartUpdateOrderRequestDTOList(dtoList);
        response = orderForThirdPartRPCService.thirdPartyUpdateOrder(request);
        Assert.assertTrue("成功", response.isSuccess());

        request.setOrgCode(DockingConstant.HUA_NAN_SHI_FAN_DA_XUE);
        ThirdPartUpdateOrderRequestDTO item5 = new ThirdPartUpdateOrderRequestDTO();
        item5.setStatus(4);
        item5.setOrderNo("DC202103221306401");
        dtoList.add(item5);
        request.setThirdPartUpdateOrderRequestDTOList(dtoList);
        response = orderForThirdPartRPCService.thirdPartyUpdateOrder(request);
        Assert.assertTrue("成功", response.isSuccess());

        response = orderForThirdPartRPCService.thirdPartyUpdateOrder(request);
        Assert.assertTrue("成功", response.isSuccess());

        orderMaster.setStatus(3);
        Mockito.when(orderMasterMapper.findByFordernoIn(Mockito.anyList())).thenReturn(Arrays.asList(orderMaster));
        response = orderForThirdPartRPCService.thirdPartyUpdateOrder(request);
        Assert.assertTrue("成功", response.isSuccess());

        orderMaster.setStatus(10);
        Mockito.when(orderMasterMapper.findByFordernoIn(Mockito.anyList())).thenReturn(Arrays.asList(orderMaster));
        request.setOrgCode(OrgEnum.GUANG_XI_ZHONG_LIU.getCode());
        ThirdPartUpdateOrderRequestDTO item6 = new ThirdPartUpdateOrderRequestDTO();
        item6.setStatus(10);
        item6.setOrderNo("DC202103221302301");
        dtoList.add(item6);
        request.setThirdPartUpdateOrderRequestDTOList(dtoList);
        StatementResultDTO statementResultDTO = new StatementResultDTO();
        statementResultDTO.setId(1L);
        statementResultDTO.setStatus(3);
        Mockito.when(researchStatementClient.createStatementSingle(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(statementResultDTO);
        response = orderForThirdPartRPCService.thirdPartyUpdateOrder(request);
        Assert.assertTrue("成功", response.isSuccess());

        item6.setStatus(6);
        dtoList.add(item6);
        request.setThirdPartUpdateOrderRequestDTOList(dtoList);
        response = orderForThirdPartRPCService.thirdPartyUpdateOrder(request);
        Assert.assertTrue("成功", response.isSuccess());

        orderMaster.setStatus(0);
        Mockito.when(orderMasterMapper.findByFordernoIn(Mockito.anyList())).thenReturn(Arrays.asList(orderMaster));
        response = orderForThirdPartRPCService.thirdPartyUpdateOrder(request);
        Assert.assertTrue("成功", response.isSuccess());
    }

    @Test
    public void thirdPartyQueryOrder() {
        List<OrderMasterDO> r1 = new ArrayList<>();
        OrderMasterDO order = new OrderMasterDO();
        order.setId(1);
        order.setForderno("test1");
        order.setFbuydepartment("test");
        order.setFbuyername("test");
        order.setForderdate(new Date());
        order.setStatus(5);
        order.setFlastreceiveman("test");
        order.setFlastreceivemanid("1");
        order.setForderamounttotal(BigDecimal.valueOf(10.00));
        order.setFsuppid(1);
        order.setOrderType(1);
        FundCardSearchDTO card = new FundCardSearchDTO();
        card.setFundCardId("11");
        order.setFtbuyappid(1);
        order.setFuserid(1);
        order.setFbuyerid(1);
        order.setReceivePicUrls("test1.jpg");
        r1.add(order);

        OrderDetailDO orderDetailSearchDTO = new OrderDetailDO();
        orderDetailSearchDTO.setFgoodname("goodname");
        orderDetailSearchDTO.setFbrand("brand");
        orderDetailSearchDTO.setFgoodcode("goodcode");
        orderDetailSearchDTO.setFspec("spec");
        orderDetailSearchDTO.setFbidprice(BigDecimal.valueOf(2.00));
        orderDetailSearchDTO.setFquantity(BigDecimal.valueOf(1.00));
        orderDetailSearchDTO.setFmasterid(1);
        orderDetailSearchDTO.setFirstCategoryId(1);
        List<OrderDetailDO> r11 = new ArrayList<>();
        r11.add(orderDetailSearchDTO);

        Mockito.when(orderMasterMapper.findByFordernoIn(Mockito.any())).thenReturn(r1);
        Mockito.when(orderDetailMapper.findAllByFmasteridIn(Mockito.any())).thenReturn(r11);

        RefFundcardOrderDTO r2 = new RefFundcardOrderDTO();
        r2.setCardId("test");
        Mockito.when(refFundcardOrderService.findByOrderIdList(Mockito.any())).thenReturn(Arrays.asList(r2));
        FundCardDTO r3 = new FundCardDTO();
        Mockito.when(researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(Mockito.any(), Mockito.any())).thenReturn(Arrays.asList(r3));

        InvoiceDTO r4 = new InvoiceDTO();
        r4.setSummaryNo("test");
        InvoiceRefResultDTO invoiceRefResultDTO = new InvoiceRefResultDTO();
        invoiceRefResultDTO.setInvoiceId(1L);
        invoiceRefResultDTO.setSourceId(1L);
        r4.setInvoiceRefDTOS(Arrays.asList(invoiceRefResultDTO));
        Mockito.when(invoiceClient.findInvoiceList(Mockito.any(), Mockito.any())).thenReturn(Arrays.asList(r4));

        SupplierDTO r5 = new SupplierDTO();
        r5.setId(1L);
        r5.setSuppName("test");
        r5.setSuppCode("test");
        r5.setFcontactman("test");
        r5.setTelephone("test");
        // Mockito.when(suppClient.getSupplierListByIds(Mockito.any())).thenReturn(Arrays.asList(r5));

        ApplicationMasterDTO r6 = new ApplicationMasterDTO();
        r6.setId(1L);
        r6.setApplyInfo("test");
        Mockito.when(applicationBaseClient.findByMasterId(Mockito.any(List.class))).thenReturn(Arrays.asList(r6));

        OrderIdInvoiceTitleDO r7 = new OrderIdInvoiceTitleDO();
        r7.setFundTypeName("1");
        r7.setId(1);
        Mockito.when(orderMasterMapper.findIdAndInvoiceTitleByIdIn(Mockito.any())).thenReturn(Arrays.asList(r7));

        FundCardDTO r33 = new FundCardDTO();
        r33.setId("2");
        r33.setCode("code2");
        r33.setLevel(2);
        FundCardDTO r333 = new FundCardDTO();
        r333.setLevel(3);
        r333.setCode("code3");
        r333.setId("3");

        r33.setFundCardDTOs(Arrays.asList(r333));
        r3.setFundCardDTOs(Arrays.asList(r33));
        r3.setCode("code1");
        r3.setId("1");
        r3.setLevel(1);
        Mockito.when(researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(Mockito.any(), Mockito.any())).thenReturn(Arrays.asList(r3));

        List<UserBaseInfoDTO> r8 = new ArrayList<>();
        UserBaseInfoDTO u1 = new UserBaseInfoDTO();
        u1.setId(1);
        u1.setJobnumber("test");
        r8.add(u1);
        Mockito.when(userClient.getUserByIdsAndOrgId(Mockito.any(List.class), Mockito.anyInt())).thenReturn(r8);

        SuppBankInfoDTO r9 = new SuppBankInfoDTO();
        r9.setBankAccount("test");

        ThirdPartQueryRequestDTO thirdPartQueryRequestDTO = new ThirdPartQueryRequestDTO();
        thirdPartQueryRequestDTO.setOrgCode(OrgEnum.GUANG_XI_ZHONG_LIU.getCode());
        thirdPartQueryRequestDTO.setOrderNoList(Arrays.asList("test1"));
        // RemoteResponse<List<ThirdPartQueryOrderResponseDTO>> response = orderForThirdPartRPCService.thirdPartyQueryOrder(thirdPartQueryRequestDTO);
        // Assert.assertTrue(response.isSuccess());

    }
}
