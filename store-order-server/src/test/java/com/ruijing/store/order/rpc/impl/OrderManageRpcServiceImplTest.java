package com.ruijing.store.order.rpc.impl;

import com.alibaba.testable.core.annotation.MockMethod;
import com.alibaba.testable.core.tool.PrivateAccessor;
import com.alibaba.testable.processor.annotation.EnablePrivateAccess;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.statement.api.invoice.dto.InvoiceQueryDTO;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.concurrent.ListenableFutures;
import com.ruijing.fundamental.remoting.msharp.context.ProviderContext;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.shop.crm.api.pojo.dto.OfflineSupplierDTO;
import com.ruijing.shop.crm.api.pojo.dto.account.UserAccountDTO;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.dto.ApplicationQueryDTO;
import com.ruijing.store.apply.dto.application.ApplyRefSuppBusinessDTO;
import com.ruijing.store.order.api.base.common.OrderCommonPrintParamDTO;
import com.ruijing.store.order.api.base.enums.OrderPrintDataConstant;
import com.ruijing.store.order.api.base.ordermaster.dto.ApplyCancelOrderReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.CancelOrderReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.other.dto.OrderCommonPrintDataDTO;
import com.ruijing.store.order.api.base.other.dto.OrderPrintDTO;
import com.ruijing.store.order.api.base.other.dto.PrintApproveDTO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.docking.mapper.DockingExtraMapper;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import com.ruijing.store.order.business.service.CancelOrderManageService;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.business.service.OrderMasterForTPIService;
import com.ruijing.store.order.business.service.impl.OrderManageServiceImpl;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderInvoiceInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderAcceptCommentVO;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.util.BarCodeUtils;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.utils.MyMockUtils;
import com.ruijing.store.warehouse.message.bean.OutWarehouseApplicationBean;
import com.ruijing.store.warehouse.service.OutWarehouseGWService;
import com.ruijing.store.wms.api.dto.BizWarehouseExitDTO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@EnablePrivateAccess(srcClass = OrderManageRpcServiceImpl.class)
public class OrderManageRpcServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderManageRpcServiceImpl orderManageRpcService;

    @org.mockito.Mock
    private OrderMasterMapper orderMasterMapper;

    @org.mockito.Mock
    private OrderDetailMapper orderDetailMapper;

    @org.mockito.Mock
    private RefFundcardOrderService refFundcardOrderService;

    @org.mockito.Mock
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @org.mockito.Mock
    private ApplicationBaseClient applicationBaseClient;

    @org.mockito.Mock
    private UserClient userClient;

    @org.mockito.Mock
    private DepartmentRpcClient departmentRpcClient;

    @org.mockito.Mock
    private OrderAcceptCommentClient orderAcceptCommentClient;

    @org.mockito.Mock
    private BizExitServiceClient bizExitServiceClient;

    @org.mockito.Mock
    private OutWarehouseGWService outWarehouseGWService;

    @org.mockito.Mock
    private InvoiceClient invoiceClient;

    @org.mockito.Mock
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @org.mockito.Mock
    private BidClient bidClient;

    @org.mockito.Mock
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @org.mockito.Mock
    private CacheClient cacheClient;

    @org.mockito.Mock
    private OrderManageService orderManageService;

    @org.mockito.Mock
    private CancelOrderManageService cancelOrderManageService;

    @org.mockito.Mock
    private DockingExtraMapper dockingExtraMapper;

    @org.mockito.Mock
    private TPIOrderClient tpiOrderClient;

    @org.mockito.Mock
    private OrderOtherLogClient orderOtherLogClient;

    @org.mockito.Mock
    private SuppClient suppClient;

    @org.mockito.Mock
    private SysConfigClient sysConfigClient;

    @org.mockito.Mock
    private BarCodeUtils barCodeUtils;

    @org.mockito.Mock
    private CompletableFuture completableFuture;

    @org.mockito.Mock
    private OrderExtraClient orderExtraClient;

    @org.mockito.Mock
    private OrderMasterForTPIService orderMasterForTPIService;

    @org.mockito.Mock
    private OrderAddressRPCClient orderAddressRPCClient;

    @org.mockito.Mock
    private ThirdPartOrderRPCClient thirdPartOrderRPCClient;

    @org.mockito.Mock
    private GoodsReturnMapper goodsReturnMapper;

    public static class Mock {
        @MockMethod(targetClass = OrderManageServiceImpl.class)
        private void newPackageOrderFundCardInfo(OrderPrintDTO orderPrint, List<RefFundcardOrderDTO> orderFundcardList, String orgCode) {
        }

        @MockMethod(targetClass = CacheClient.class)
        public void controlRepeatOperation(String uniqKey, Integer timeLimit) {
        }

        @MockMethod(targetClass = CacheClient.class)
        public void removeCache(String uniqKey) {
        }

        /**
         * 条形码（测试调用会失败，因此需要mock
         * @param content
         * @return
         * @throws Exception
         */
        @MockMethod(targetClass = BarCodeUtils.class)
        public static String getBase64Img(String content) throws Exception {
            return "barcode";
        }

        @MockMethod(targetClass = CancelOrderManageService.class)
        public void cancelOrder(ApplyCancelOrderReqDTO applyCancelOrderReqDTO) {
        }

        @MockMethod(targetClass = CancelOrderManageService.class)
        public void refuseCancelOrder(CancelOrderReqDTO cancelOrderReqDTO){
        }

        @MockMethod(targetClass = CancelOrderManageService.class)
        public void agreeCancelOrder(CancelOrderReqDTO cancelOrderReqDTO){
        }

        @MockMethod(targetClass = CancelOrderManageService.class)
        public void cancelOfflineOrder(CancelOrderReqDTO cancelOrderReqDTO){
        }
    }

    @Test
    public void packageDeliveryInfo() {
        OrderCommonPrintDataDTO orderCommonPrintDataDTO = new OrderCommonPrintDataDTO();
        OrderPrintDTO orderPrintDTO = new OrderPrintDTO();
        orderPrintDTO.setOrderId(1);
        orderCommonPrintDataDTO.setOrderMasterList(New.list(orderPrintDTO));
        PrivateAccessor.invoke(orderManageRpcService, "packageDeliveryInfo", New.list(), orderCommonPrintDataDTO);
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setId(1);
        orderMasterDO.setFconfirmmanid("1");
        UserAccountDTO userDTO = new UserAccountDTO();
        userDTO.setId(1l);
        Mockito.when(suppClient.querySuppAccountList(Mockito.anyInt(), Mockito.anyString())).thenReturn(New.list(userDTO));
        PrivateAccessor.invoke(orderManageRpcService, "packageDeliveryInfo", New.list(orderMasterDO), orderCommonPrintDataDTO);
    }

    @Test
    public void testGetCommonPrintData() throws Exception {
        String orderInfoListJson = "[{\"id\":175096,\"fmasterguid\":null,\"ftbuyappid\":247120,\"forderno\":\"*****************\",\"forderdate\":*************,\"fbuyerid\":4233,\"fbuyercode\":null,\"fbuyername\":\"all\",\"fbuyeremail\":\"<EMAIL>\",\"fbuyercontactman\":\"all12345\",\"fbuyertelephone\":\"***********,***********\",\"fbuydepartmentid\":17039,\"fbuydepartment\":\"test课题组\",\"fsuppid\":5,\"fsuppcode\":\"S0005\",\"fsuppname\":\"西格玛奥德里123(上海)贸易有限公司\",\"fbiderdeliveryplace\":\"北京市北京市西城区8765432\",\"forderamounttotal\":406.00,\"fundStatus\":1,\"failedReason\":null,\"status\":10,\"fconfirmdate\":*************,\"fconfirmmanid\":\"5\",\"fconfirmman\":\"赵健荣\",\"fcanceldate\":null,\"fcancelmanid\":null,\"fcancelman\":null,\"fdeliverydate\":1610592458000,\"fdeliverymanid\":\"5\",\"fdeliveryman\":\"赵健荣\",\"flastreceivedate\":1610592493000,\"flastreceivemanid\":\"4233\",\"flastreceiveman\":\"all\",\"fassessdate\":null,\"fassessmanid\":null,\"fassessman\":null,\"projectid\":\"\",\"projectnumber\":\"\",\"projecttitle\":\"\",\"fuserid\":60,\"fusercode\":\"WU_YI_DA_XUE\",\"fusername\":\"江门五邑大学\",\"statementId\":1502283,\"fcancelreason\":null,\"frefuseCancelReason\":null,\"shutDownDate\":null,\"deliveryInfo\":\"\",\"deliveryNo\":\"\",\"returnAmount\":0.0,\"frefuseCancelDate\":null,\"fdeliveryid\":88159,\"bidOrderId\":null,\"orderType\":0,\"receivePicUrls\":\"https://images-test.rjmart.cn/image/8881d93e/8e88c2bc-0572-4f7d-ba0b-fe8509a8e6fe.jpg\",\"tpiProjectId\":null,\"originalAmount\":406.00,\"inventoryStatus\":9,\"species\":0,\"updateTime\":1610593093000,\"inStateTime\":1610593093000,\"purchaseRootinType\":0,\"carryFee\":0.00,\"invoiceTitleId\":23,\"invoiceTitle\":\"五邑大学发票抬头BBBB\",\"invoiceTitleNumber\":\"FGHJKLLKJHGFDS\",\"fundType\":null,\"fundTypeName\":\"\",\"paymentAmount\":0.00,\"statementStatus\":-1}]";
        List<OrderMasterDO> orderInfoList = JsonUtils.parseList(orderInfoListJson, OrderMasterDO.class);
        Mockito.when(orderMasterMapper.findByIdIn(Mockito.anyCollection())).thenReturn(orderInfoList);

        String orderDetailListJson = "[{\"id\":178072,\"fmasterid\":175096,\"fbiddate\":1610553600000,\"fdetailno\":\"*****************\",\"categoryid\":251,\"fclassification\":\"动物种类\",\"fgoodcode\":\"xbph-005\",\"fgoodname\":\"小白鼠\",\"fbrand\":\"SUPELCO\",\"fspec\":\"5kg\",\"funit\":\"件\",\"fquantity\":7.00,\"fbidprice\":58.00,\"fbidamount\":406.00,\"fpicpath\":\"https://images-test.rjmart.cn/image/c19bd248/ae189d46-6ca2-4fa1-8a8a-1510d67cb7c7.jpg\",\"fremainquantity\":0.00,\"fbrandid\":385,\"tsuppmerpassid\":null,\"fcancelquantity\":0.00,\"fcancelamount\":0.00,\"productSn\":100000756739,\"returnStatus\":null,\"returnAmount\":0.0,\"originalAmount\":406.00,\"originalPrice\":58.00,\"modifyPrice\":false,\"deliveryTime\":0,\"remainderPrice\":0.00,\"negotiatedPrice\":null,\"sysuCategoryId\":null,\"categoryDirectoryId\":0,\"updateTime\":*************,\"carryFee\":0.00,\"firstCategoryId\":56,\"firstCategoryName\":\"实验动物\",\"secondCategoryId\":251,\"secondCategoryName\":\"动物种类\",\"feeTypeTag\":\"实验耗材费\",\"categoryTag\":\"动物\",\"dangerousTypeId\":13,\"dangerousTypeName\":\"动物种类\",\"regulatoryTypeId\":2,\"regulatoryTypeName\":\"非管制\",\"casno\":\"\",\"suppId\":5,\"suppName\":\"西格玛奥德里123(上海)贸易有限公司\",\"suppCode\":null,\"brandEname\":\"SUPELCO\"}]";
        List<OrderDetailDO> orderDetailList = JsonUtils.parseList(orderDetailListJson, OrderDetailDO.class);
        Mockito.when(orderDetailMapper.findAllByFmasteridIn(Mockito.anyCollection())).thenReturn(orderDetailList);

        String refFundCardListJson = "[{\"id\":\"e67310a1-64a3-4997-9ff9-77fb1d6ba5ce\",\"applicationId\":\"\",\"orderId\":\"175096\",\"bidId\":null,\"cardId\":\"ab5e0b19-31ee-48e9-873d-ae40bbc710c7\",\"subjectId\":null,\"serialNumber\":null,\"usemoney\":null,\"deletionTime\":null,\"isDeleted\":false,\"creationTime\":1610593087000,\"updateTime\":1610593087000,\"cardNo\":\"32323\",\"fundType\":0,\"approveUser\":0,\"freezeAmount\":0.000,\"consumablesFee\":null,\"analysisFee\":null,\"projectId\":null}]";
        List<RefFundcardOrderDTO> refFundCardList = JsonUtils.parseList(refFundCardListJson, RefFundcardOrderDTO.class);
        Mockito.when(refFundcardOrderService.findByOrderIdList(Mockito.anyList())).thenReturn(refFundCardList);

        String orderApprovalLogListJson = "[{\"id\":214683,\"orderId\":175096,\"photo\":null,\"reason\":null,\"approveStatus\":6,\"operatorId\":4233,\"creationTime\":1610592493000},{\"id\":214684,\"orderId\":175096,\"photo\":null,\"reason\":\"\",\"approveStatus\":1,\"operatorId\":4162,\"creationTime\":1610592527000}]";
        List<OrderApprovalLog> orderApprovalLogList = JsonUtils.parseList(orderApprovalLogListJson, OrderApprovalLog.class);
        Mockito.when(orderApprovalLogMapper.findByOrderIdInAndApproveStatusIn(Mockito.anyList(), Mockito.anyCollection())).thenReturn(orderApprovalLogList);

        String buyerDTOListJson = "[{\"id\":4162,\"email\":\"<EMAIL>\",\"enabled\":true,\"jobnumber\":\"wydx001\",\"mobile\":\"16788888800\",\"password\":\"{bcrypt}$2a$10$CvQqFDiC0hwZVSehi.8OzOgVTDJ1D8tlVIcqARsf9wSHuZQeZcifi\",\"name\":\"五邑管理员\",\"tel\":null,\"activate\":true,\"organizationId\":null,\"guid\":\"1219440773896863744\",\"pinying\":\"wu yi guan li yuan\",\"creationTime\":1579536000000,\"updateTime\":1603209600000,\"resetPsw\":false,\"idcard\":null},{\"id\":4233,\"email\":\"<EMAIL>\",\"enabled\":true,\"jobnumber\":\"wyall\",\"mobile\":\"15678787878\",\"password\":\"{bcrypt}$2a$10$De5AxLZdq2xh/XjJ6MjfWOxJ8wOVs7fF0pAmwHTECF0WelxTf4OGa\",\"name\":\"all\",\"tel\":null,\"activate\":true,\"organizationId\":null,\"guid\":\"1235449308187197440\",\"pinying\":\"a l l\",\"creationTime\":1583337600000,\"updateTime\":1583337600000,\"resetPsw\":false,\"idcard\":null}]\n";
        List<UserBaseInfoDTO> buyerDTOList = JsonUtils.parseList(buyerDTOListJson, UserBaseInfoDTO.class);
        Mockito.when(userClient.getUserByIdsAndOrgId(Mockito.anyList(), Mockito.anyInt())).thenReturn(buyerDTOList);

        String orderDepartmentMappingJson = "[{\"id\":17039,\"organizationId\":60,\"name\":\"test课题组\",\"managerId\":4162,\"departmentType\":0,\"groupId\":null,\"parentId\":16977,\"lft\":8,\"rgt\":9,\"creationTime\":1583164800000,\"updateTime\":1589731200000,\"guid\":\"473e2268-5370-42db-9b46-9e3e65845284\",\"accessDTOList\":null}]\n";
        List<DepartmentDTO> orderDepartmentMapping = JsonUtils.parseList(orderDepartmentMappingJson, DepartmentDTO.class);
        Mockito.when(departmentRpcClient.getDepartmentsByIds(Mockito.anyList())).thenReturn(orderDepartmentMapping);

        String departmentManagerListJson = "[{\"id\":4162,\"email\":\"<EMAIL>\",\"enabled\":true,\"jobnumber\":\"wydx001\",\"mobile\":\"16788888800\",\"password\":\"{bcrypt}$2a$10$CvQqFDiC0hwZVSehi.8OzOgVTDJ1D8tlVIcqARsf9wSHuZQeZcifi\",\"name\":\"五邑管理员\",\"tel\":null,\"activate\":true,\"organizationId\":null,\"guid\":\"1219440773896863744\",\"pinying\":\"wu yi guan li yuan\",\"creationTime\":1579536000000,\"updateTime\":1603209600000,\"resetPsw\":false,\"idcard\":null}]\n";
        List<UserBaseInfoDTO> departmentManagerList = JsonUtils.parseList(departmentManagerListJson, UserBaseInfoDTO.class);
        Mockito.when(userClient.getUserByIdsAndOrgId(Mockito.anyList(), Mockito.anyInt())).thenReturn(departmentManagerList);

        String orderCommentListJson = "[]";
        List<OrderAcceptCommentVO> orderCommentList = JsonUtils.parseList(orderCommentListJson, OrderAcceptCommentVO.class);
        Mockito.when(orderAcceptCommentClient.getOrderComment(Mockito.anyInt(), Mockito.anyList())).thenReturn(orderCommentList);

        String exitInfoListJson = "[{\"id\":3847,\"exitNo\":\"CK402052740315877378\",\"orgId\":60,\"userGuid\":\"1235449308187197440\",\"orderNo\":\"*****************\",\"userName\":\"all\",\"roomId\":196,\"roomName\":\"普通试剂耗材库房(总库)\",\"status\":1,\"createTime\":1610592843000,\"deptId\":17039,\"deptName\":\"test课题组\",\"exitTime\":\"2021-01-14 10:54:03\",\"businessType\":1,\"exitDetailDTOList\":[{\"id\":4989,\"exitId\":3847,\"suppId\":5,\"suppName\":\"西格玛奥德里123(上海)贸易有限公司\",\"specifications\":\"5kg\",\"productName\":\"小白鼠\",\"brandName\":\"SUPELCO\",\"casNo\":\"\",\"productCode\":\"xbph-005\",\"dangerousType\":13,\"controlFlag\":2,\"shouldoutNum\":7.000,\"exitedNum\":7,\"exitedUnit\":\"件\",\"measurementUnit\":\"\",\"measurementNum\":0.000,\"form\":0,\"sort\":\"\",\"price\":406.00,\"unitPrice\":58.00}]}]\n";
        List<BizWarehouseExitDTO> exitInfoList = JsonUtils.parseList(exitInfoListJson, BizWarehouseExitDTO.class);
        Mockito.when(bizExitServiceClient.queryExitByOrderNoList(Mockito.anyList())).thenReturn(exitInfoList);

        String outWarehouseApplicationBeanJson = "{\"outWarehouseApplicationId\":3847,\"outWarehouseApplicationNo\":\"CK402052740315877378\",\"orderNo\":\"*****************\",\"outWarehouseApplicationDate\":1610592843000,\"outWarehouseApplicant\":\"all\",\"warehouseId\":196,\"warehouseName\":\"普通试剂耗材库房(总库)\",\"status\":1,\"statusName\":\"已出库\",\"departmentName\":\"test课题组\",\"totalPrice\":0.0,\"totalPriceInChinese\":null,\"outWarehouseDate\":1610592843000,\"exitNoBarcode\":\"\",\"productBeans\":null}\n";
        OutWarehouseApplicationBean outWarehouseApplicationBean = JsonUtils.fromJson(outWarehouseApplicationBeanJson, OutWarehouseApplicationBean.class);
        Mockito.when(outWarehouseGWService.getImmediatelyOutWarehouseApplicationBean(Mockito.any(BizWarehouseExitDTO.class), Mockito.anyString())).thenReturn(outWarehouseApplicationBean);

        String invoiceVOListJson = "[{\"invoiceId\":1501419,\"invoiceNo\":\"********\",\"invoiceCode\":\"**********\",\"amount\":406.00,\"issueDate\":\"2021-01-14 08:00:00\",\"remark\":\"6\",\"drawer\":\"西格玛奥德里123(上海)贸易有限公司\",\"bankName\":\"天河区天河北路888号\",\"bankNo\":\"9882340000000000000000\",\"orderNo\":null,\"orderId\":175096,\"picturePathList\":[\"\"]}]\n";
        List<OrderInvoiceInfoVO> invoiceVOList = JsonUtils.parseList(invoiceVOListJson, OrderInvoiceInfoVO.class);
        Mockito.when(invoiceClient.findInvoiceVOList(Mockito.any(InvoiceQueryDTO.class))).thenReturn(invoiceVOList);

        Mockito.when(purchaseApprovalLogClient.getApprovalLogById(Mockito.anyInt())).thenReturn(New.list());
        Mockito.when(bidClient.findApprovalLogInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(New.list());
        Mockito.when(researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(Mockito.anyString(), Mockito.anyList())).thenReturn(New.list());

        ApplicationMasterDTO applicationMasterDTO = new ApplicationMasterDTO();
        applicationMasterDTO.setId(89757L);
        applicationMasterDTO.setApplyInfo("test Apply info");
        List<ApplicationMasterDTO> applyMasterList = New.list(applicationMasterDTO);
        Mockito.when(applicationBaseClient.findByMasterId(Mockito.any(ApplicationQueryDTO.class))).thenReturn(applyMasterList);
        ApplyRefSuppBusinessDTO applyRefSuppBusinessDTO = new ApplyRefSuppBusinessDTO();
        applicationMasterDTO.setId(89757L);
        Mockito.when(applicationBaseClient.getOfflineSupplierByAppIdList(Mockito.any())).thenReturn(Arrays.asList(applyRefSuppBusinessDTO));

        List<Integer> orderIdList = new ArrayList<>();
        orderIdList.add(175096);
        OrderCommonPrintParamDTO request = new OrderCommonPrintParamDTO();
        request.setOrderIdList(orderIdList);

        // test exit warehouse and invoice info
        request.setPrintDataTypeList(New.list(OrderPrintDataConstant.PURCHASE, OrderPrintDataConstant.EXIT_WAREHOUSE, OrderPrintDataConstant.INVOICE_INFO));
        RemoteResponse<OrderCommonPrintDataDTO> response = orderManageRpcService.getCommonPrintData(request);
        Assert.assertTrue(response.isSuccess());

        // test throw an exception when turn orderNo to barcode
        orderInfoList.get(0).setFusercode(OrgEnum.QI_LU_GONG_YE_DA_XUE.getCode());
        Mockito.when(orderMasterMapper.findByIdIn(Mockito.anyCollection())).thenReturn(orderInfoList);
        response = orderManageRpcService.getCommonPrintData(request);
        Assert.assertTrue(response.isSuccess());

        orderInfoList.get(0).setFusercode(OrgEnum.FU_ZHOU_PI_FU_BING_FANG_ZHI_YUAN.getCode());
        Mockito.when(orderMasterMapper.findByIdIn(Mockito.anyCollection())).thenReturn(orderInfoList);
        response = orderManageRpcService.getCommonPrintData(request);
        Assert.assertTrue(response.isSuccess());

        // 陆军军医 父级课题组查询、线下供应商社会统一信用编号查询
        DepartmentDTO departmentDTO = new DepartmentDTO();
        departmentDTO.setParentId(1);
        departmentDTO.setName("deptParentName");
        Mockito.when(departmentRpcClient.getDepartmentParentInfo(Mockito.anyInt())).thenReturn(departmentDTO);

        OfflineSupplierDTO offlineSupplierDTO = new OfflineSupplierDTO();
        offlineSupplierDTO.setUnifyCode("unifyCode");
        Mockito.when(suppClient.getOfflineSuppById(Mockito.anyInt())).thenReturn(offlineSupplierDTO);

        orderInfoList.get(0).setFuserid(OrgEnum.LU_JUN_JUN_YI_DA_XUE.getValue());
        orderInfoList.get(0).setFusercode(OrgEnum.LU_JUN_JUN_YI_DA_XUE.getCode());
        BaseOrderExtraDTO orderExtraDTO = new BaseOrderExtraDTO();
        orderExtraDTO.setOrderId(175096);
        orderExtraDTO.setExtraValue("second name");
        Mockito.when(orderExtraClient.selectByOrderIdInAndExtraKey(Mockito.anyCollection(), Mockito.anyInt())).thenReturn(New.list(orderExtraDTO));
        response = orderManageRpcService.getCommonPrintData(request);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void agreeCancelOrder() throws Exception {
        OrderMasterDTO param = new OrderMasterDTO();
        param.setStatus(1);
        param.setId(2);
        param.setShutDownDate(new Date());

        Mockito.when(orderMasterMapper.updateOrderById(Mockito.any(UpdateOrderParamDTO.class))).thenReturn(1);
        Mockito.doNothing().when(invoiceClient).deleteInvoiceByOrderIds(Mockito.anyList());

        // normal
        orderManageRpcService.agreeCancelOrder(param);
        Assert.assertTrue(true);

        // exception
        Mockito.doThrow(new RuntimeException("error test")).when(orderMasterMapper).updateOrderById(Mockito.any(UpdateOrderParamDTO.class));
        try {
            orderManageRpcService.agreeCancelOrder(param);
        } catch (Exception e) {
            Assert.assertTrue(true);
        }
    }

    @Test
    public void retryPushOrderByOrderNo() {
        MyMockUtils.setThreadLocalField(orderManageRpcService, "thunderSwitch", false);
        MyMockUtils.setRPCContextRJSession("test", 42, 165);
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setId(1);
        orderMasterDO.setForderno("test");
        orderMasterDO.setStatus(0);
        orderMasterDO.setFusercode(DockingConstant.GUANG_DONG_GONG_YE_DA_XUE);
        Mockito.when(orderMasterMapper.findByFtbuyappidIn(Arrays.asList(Mockito.anyInt()))).thenReturn(Arrays.asList(orderMasterDO));
        Mockito.when(dockingExtraMapper.updateByInfo(Mockito.any())).thenReturn(1);
        Mockito.when(dockingExtraMapper.findByInfoIn(Mockito.any())).thenReturn(Arrays.asList(new DockingExtra("test", "test", -2, "test")));
        Mockito.when(orderMasterMapper.findByForderno(Mockito.anyString())).thenReturn(orderMasterDO);
        Mockito.when(orderMasterMapper.findByFtbuyappidIn(Arrays.asList(Mockito.anyInt()))).thenReturn(Arrays.asList(orderMasterDO));
        Mockito.when(orderDetailMapper.findByFmasterid(Mockito.anyInt())).thenReturn(Arrays.asList());
        OrderAddressDTO orderAddressDTO = new OrderAddressDTO();
        orderAddressDTO.setAddress("test");
        Mockito.when(orderAddressRPCClient.findByOrderId(Mockito.anyInt())).thenReturn(orderAddressDTO);

        Mockito.when(userClient.getUserInfoByGuidAndOrgid(Mockito.anyString(), Mockito.anyInt())).thenReturn(new UserBaseInfoDTO());
        Mockito.when(userClient.getUserInfo(Mockito.any(), Mockito.anyInt())).thenReturn(new UserBaseInfoDTO());
        Mockito.when(refFundcardOrderService.findByOrderId(Mockito.anyList())).thenReturn(Collections.emptyList());
        Mockito.when(orderMasterForTPIService.updateOrderStatusAsync(Mockito.any())).thenReturn(ListenableFutures.forValue(true));

//        Mockito.when(orderMasterForTPIService.updateOrderStatusAsync(Mockito.any())).thenReturn(AsyncResults.forValue(true));

        LoginUserInfoBO userBaseInfoDTO = new LoginUserInfoBO();
        userBaseInfoDTO.setUserName("aaa");
        Mockito.when(userClient.getLoginUserInfo(Mockito.anyString(), Mockito.anyInt())).thenReturn(userBaseInfoDTO);
        Mockito.when(tpiOrderClient.retryPushOrderInfo(Mockito.any())).thenReturn(true);
        Mockito.doNothing().when(orderOtherLogClient).createOrderDockingLog(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());

        Map<String, Object> session = new HashMap<>();
        RjSessionInfo sessionInfo = new RjSessionInfo();
        sessionInfo.setGuid("test");
        sessionInfo.setSuppId(165);
        session.put("RJ_SESSION_INFO", sessionInfo);

        RpcContext.getProviderContext().setProviderContext(ProviderContext.getProviderContext());
        RpcContext.getProviderContext().setCallAttachments(session);

        RemoteResponse<Integer> response = orderManageRpcService.retryPushOrderByAppId(1);
        Assert.assertTrue(response.isSuccess());

//        orderMasterDO.setFusercode(DockingConstant.GUANG_XI_ZHONG_LIU);
        orderMasterDO.setFusercode(DockingConstant.GUANG_DONG_GONG_YE_DA_XUE);
        Mockito.when(orderMasterMapper.findByFtbuyappidIn(Arrays.asList(Mockito.anyInt()))).thenReturn(Arrays.asList(orderMasterDO));
        response = orderManageRpcService.retryPushOrderByAppId(1);
        Assert.assertTrue(response.isSuccess());

        orderMasterDO.setStatus(6);
        Mockito.when(orderMasterMapper.findByFtbuyappidIn(Arrays.asList(Mockito.anyInt()))).thenReturn(Arrays.asList(orderMasterDO));
        response = orderManageRpcService.retryPushOrderByAppId(1);
        Assert.assertTrue(response.isSuccess());

        orderMasterDO.setStatus(0);
        Mockito.when(orderMasterMapper.findByFtbuyappidIn(Arrays.asList(Mockito.anyInt()))).thenReturn(Arrays.asList(orderMasterDO));
        Mockito.when(dockingExtraMapper.findByInfoIn(Mockito.any())).thenReturn(Arrays.asList(new DockingExtra("test", "test", -3, "test")));
        response = orderManageRpcService.retryPushOrderByAppId(1);
        Assert.assertTrue(response.isSuccess());

        response = orderManageRpcService.retryPushOrderByAppId(1);
        Assert.assertTrue(response.isSuccess());


        response = orderManageRpcService.retryPushOrderByAppId(1);
        Assert.assertTrue(response.isSuccess());

        orderMasterDO.setFusercode(DockingConstant.GUANG_XI_ZHONG_LIU);
        Mockito.when(orderMasterMapper.findByFtbuyappidIn(Arrays.asList(Mockito.anyInt()))).thenReturn(Arrays.asList(orderMasterDO));
        response = orderManageRpcService.retryPushOrderByAppId(1);
        Assert.assertTrue(response.isSuccess());

        orderMasterDO.setStatus(6);
        Mockito.when(orderMasterMapper.findByFtbuyappidIn(Arrays.asList(Mockito.anyInt()))).thenReturn(Arrays.asList(orderMasterDO));
        response = orderManageRpcService.retryPushOrderByAppId(1);
        Assert.assertTrue(response.isSuccess());

        MyMockUtils.setThreadLocalField(orderManageRpcService, "thunderSwitch", true);
        orderMasterDO.setFusercode(DockingConstant.GUANG_DONG_GONG_YE_DA_XUE);
        Mockito.when(orderMasterMapper.findByFtbuyappidIn(Arrays.asList(Mockito.anyInt()))).thenReturn(Arrays.asList(orderMasterDO));
        Mockito.when(thirdPartOrderRPCClient.pushOrderInfo(Mockito.anyList(), Mockito.any(), Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        response = orderManageRpcService.retryPushOrderByAppId(1);
        Assert.assertTrue(response.isSuccess());

        GoodsReturn g = new GoodsReturn();
        g.setId(1);
        Mockito.when(goodsReturnMapper.findByOrderNo(Mockito.anyString())).thenReturn(Arrays.asList(g));
        Mockito.when(orderMasterForTPIService.asyncOrderReturn(Mockito.any(), Mockito.any())).thenReturn(ListenableFutures.forValue(true));
        orderMasterDO.setStatus(5);
        orderMasterDO.setFusercode(DockingConstant.GUANG_ZHOU_YI_KE_DA_XUE);
        orderMasterDO.setReturnAmount(1.00D);
        Mockito.when(orderMasterMapper.findByFtbuyappidIn(Arrays.asList(Mockito.anyInt()))).thenReturn(Arrays.asList(orderMasterDO));
        response = orderManageRpcService.retryPushOrderByAppId(1);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void getCommonSummaryPrintData() {
        Integer orderId = 123;
        Integer deptId = 321;

        // find in db
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setId(orderId);
        orderMasterDO.setSpecies((byte)0);
        orderMasterDO.setFbuydepartmentid(deptId);
        // 随意普通单位
        orderMasterDO.setFusercode(OrgEnum.LING_NAN_NONG_KE_SHI_YAN_SHI.getCode());
        orderMasterDO.setFuserid(OrgEnum.LING_NAN_NONG_KE_SHI_YAN_SHI.getValue());
        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.any())).thenReturn(orderMasterDO);

        // 几个异步调用的方法 1关联采购人t_user的信息
        Mockito.when(userClient.getUserInfo(Mockito.any(), Mockito.anyInt())).thenReturn(null);

        // 2获取打印汇总单商品详情
        Mockito.when(orderDetailMapper.findByFmasterid(Mockito.anyInt())).thenReturn(New.list(new OrderDetailDO()));
        // 3经费负责人信息
        RefFundcardOrderDTO refFundCard = new RefFundcardOrderDTO();
        refFundCard.setCardId("cardId");
        Mockito.when(refFundcardOrderService.findByOrderIdList(Mockito.anyList())).thenReturn(New.list(refFundCard));
        FundCardDTO fundCardDTO = new FundCardDTO();
        Mockito.when(researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(Mockito.anyString(), Mockito.anyList())).thenReturn(New.list(fundCardDTO));
        // 4订单审批日志
        Mockito.when(bidClient.findApprovalLogInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(New.list());
        // 5发票信息
        OrderInvoiceInfoVO orderInvoiceInfoVO = new OrderInvoiceInfoVO();
        orderInvoiceInfoVO.setInvoiceNo("invoiceNO");
        orderInvoiceInfoVO.setOrderIdList(New.list(orderId));
        Mockito.when(invoiceClient.findInvoiceVOList(Mockito.any(InvoiceQueryDTO.class))).thenReturn(New.list(orderInvoiceInfoVO));
        // 6课题组负责人
        DepartmentDTO deptInfo = new DepartmentDTO();
        deptInfo.setId(orderMasterDO.getFbuydepartmentid());
        UserBaseInfoDTO managerInfo = new UserBaseInfoDTO();
        managerInfo.setName("managerName");
        Mockito.when(userClient.getDepartmentListByIds(Mockito.anyList())).thenReturn(New.list(deptInfo));
        Mockito.when(userClient.getUserDetailByID(Mockito.anyInt())).thenReturn(managerInfo);

        // 创建6个线程
        ThreadPoolExecutor defaultIoExecutor = new ThreadPoolExecutor(6, 6, 10, TimeUnit.SECONDS, new SynchronousQueue<Runnable>());
        MyMockUtils.setThreadLocalField(orderManageRpcService, "defaultIoExecutor", defaultIoExecutor);

        RemoteResponse<List<PrintApproveDTO>> response = orderManageRpcService.getCommonSummaryPrintData(New.list(orderId));
        Assert.assertTrue(response.isSuccess());

        Mockito.when(invoiceClient.findInvoiceVOList(Mockito.any(InvoiceQueryDTO.class))).thenReturn(New.list());
        response = orderManageRpcService.getCommonSummaryPrintData(New.list(orderId));
        Assert.assertTrue(response.isSuccess());
    }

    /**
     * agreeCancelOrder(CancelOrderReqDTO cancelOrderReqDTO)
     */
    @Test
    public void agreeCancelOrderParamCancelOrderReqDTO() {
        orderManageRpcService.agreeCancelOrder(new CancelOrderReqDTO());
    }


    @Test
    public void cancelOfflineOrder() {
        CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setOrderMasterId(123);
        orderManageRpcService.cancelOfflineOrder(cancelOrderReqDTO);
    }
}