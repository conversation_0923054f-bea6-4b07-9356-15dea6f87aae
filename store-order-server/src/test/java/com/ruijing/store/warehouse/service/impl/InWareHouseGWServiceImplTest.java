package com.ruijing.store.warehouse.service.impl;

import com.reagent.research.statement.api.invoice.dto.InvoiceDTO;
import com.reagent.research.statement.api.invoice.dto.InvoiceQueryDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.shop.category.api.dto.CategoryDTO;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.apply.dto.ApplyRefWarehouseDTO;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.user.api.service.UserInfoService;
import com.ruijing.store.utils.MyMockUtils;
import com.ruijing.store.warehouse.message.bean.OutWarehouseApplicationBean;
import com.ruijing.store.warehouse.message.bean.ProductBean;
import com.ruijing.store.warehouse.message.vo.inwarehouse.InAndOutWarehouseApplicationDetailVO;
import com.ruijing.store.warehouse.message.vo.inwarehouse.OrderRequestVO;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseProductRequestVO;
import com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseSubmitApplicationRequestVO;
import com.ruijing.store.warehouse.message.vo.outwarehouse.OutWarehouseApplicationDetailVO;
import com.ruijing.store.warehouse.service.OutWarehouseGWService;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDetailDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseRoomDTO;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class InWareHouseGWServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private InWareHouseGWServiceImpl inWareHouseGWService;

    @Mock
    private InvoiceClient invoiceClient;

    @Mock
    private BizWareHouseClient bizWareHouseClient;

    @Mock
    private UserClient userClient;

    @Mock
    private CategoryServiceClient categoryServiceClient;

    @Mock
    private BizExitServiceClient bizExitServiceClient;

    @Mock
    private OrderMasterMapper orderMasterMapper;

    @Mock
    private UserDepartmentRoleRpcServiceClient userDepartmentRoleRpcServiceClient;

    @Mock
    private OrderSearchBoostService orderSearchBoostService;

    @Mock
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Mock
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @Mock
    private BidClient bidClient;

    @Mock
    private BizWarehouseRoomServiceClient bizWarehouseRoomServiceClient;

    @Mock
    private UserInfoService userInfoService;

    @Mock
    private DepartmentRpcClient departmentRpcClient;

    @Mock
    private OutWarehouseGWService outWarehouseGWService;

    @Mock
    private OrderDetailMapper orderDetailMapper;

    @Mock
    private ApplyRefWarehouseServiceClient applyRefWarehouseServiceClient;

    @Test
    public void testGetPersonalizedInvoice() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class<InWareHouseGWServiceImpl> inWareHouseGWServiceClass = (Class<InWareHouseGWServiceImpl>) inWareHouseGWService.getClass();
        Method getPersonalizedInvoice = inWareHouseGWServiceClass.getDeclaredMethod("getPersonalizedInvoice", String.class, Integer.class, String.class);
        boolean accessible = getPersonalizedInvoice.isAccessible();
        getPersonalizedInvoice.setAccessible(true);

        Mockito.when(invoiceClient.findInvoiceList(Mockito.any(InvoiceQueryDTO.class))).thenReturn(new ArrayList<>());
        Mockito.when(invoiceClient.findInvoiceListByOrderNo(Mockito.anyString())).thenReturn(new ArrayList<>());

        List<InvoiceDTO> resultList = (List<InvoiceDTO>) getPersonalizedInvoice.invoke(inWareHouseGWService, "FU_ZHOU_PI_FU_BING_FANG_ZHI_YUAN",16106, "DC202103151838001");
        Assert.assertTrue(CollectionUtils.isEmpty(resultList));

        // return accessible
        getPersonalizedInvoice.setAccessible(accessible);
    }

    @Test
    public void testGetProductBeans() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class<InWareHouseGWServiceImpl> inWareHouseGWServiceClass = (Class<InWareHouseGWServiceImpl>) inWareHouseGWService.getClass();
        Method getPersonalizedInvoice = inWareHouseGWServiceClass.getDeclaredMethod("getProductBeans", List.class, List.class, String.class, Integer.class);
        boolean accessible = getPersonalizedInvoice.isAccessible();
        getPersonalizedInvoice.setAccessible(true);

        Mockito.when(bizWareHouseClient.queryKind(Mockito.anyInt())).thenReturn(Arrays.asList());

        BizWarehouseEntryDetailDTO bizWarehouseEntryDetailDTO = new BizWarehouseEntryDetailDTO();
        bizWarehouseEntryDetailDTO.setProductCode("test");
        bizWarehouseEntryDetailDTO.setPrice(BigDecimal.valueOf(1.00));
        bizWarehouseEntryDetailDTO.setReceivedNum(1);
        OrderDetailSearchDTO orderDetailSearchDTO = new OrderDetailSearchDTO();
        orderDetailSearchDTO.setCategoryId(1);
        orderDetailSearchDTO.setDetailId(1);
        orderDetailSearchDTO.setFgoodcode("test");
        orderDetailSearchDTO.setFbidprice(2.00);

        List<ProductBean> resultList = (List<ProductBean>) getPersonalizedInvoice.invoke(inWareHouseGWService, Arrays.asList(bizWarehouseEntryDetailDTO), Arrays.asList(orderDetailSearchDTO), "JI_NAN_DA_XUE", 37);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));

        // return accessible
        getPersonalizedInvoice.setAccessible(accessible);
    }

    @Test
    public void mergeWarehouseApplicationByRoomId() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class<InWareHouseGWServiceImpl> inWareHouseGWServiceClass = (Class<InWareHouseGWServiceImpl>) inWareHouseGWService.getClass();
        Method getPersonalizedInvoice = inWareHouseGWServiceClass.getDeclaredMethod("mergeWarehouseApplicationByRoomId", UserBaseInfoDTO.class, OrderMasterSearchDTO.class, WarehouseSubmitApplicationRequestVO.class);
        boolean accessible = getPersonalizedInvoice.isAccessible();
        getPersonalizedInvoice.setAccessible(true);

        UserBaseInfoDTO userBaseInfoDTO = new UserBaseInfoDTO();
        Mockito.when(userClient.getNotNullUserDetailById(Mockito.any())).thenReturn(userBaseInfoDTO);
        CategoryDTO categoryDTO = new CategoryDTO();
        categoryDTO.setId(1L);
        categoryDTO.setLevel(1);
        Mockito.when(categoryServiceClient.queryForList(Mockito.any())).thenReturn(Arrays.asList(categoryDTO));

        UserBaseInfoDTO userParam = new UserBaseInfoDTO();
        OrderMasterSearchDTO orderSearch = new OrderMasterSearchDTO();
        OrderDetailSearchDTO orderDetailSearchDTO = new OrderDetailSearchDTO();
        orderDetailSearchDTO.setFgoodcode("t");
        orderDetailSearchDTO.setFquantity(1);
        orderDetailSearchDTO.setCategoryId(1);
        orderDetailSearchDTO.setFbidprice(1.00);
        orderSearch.setOrderDetail(Arrays.asList(orderDetailSearchDTO));

        WarehouseSubmitApplicationRequestVO request = new WarehouseSubmitApplicationRequestVO();
        WarehouseProductRequestVO wareItem = new WarehouseProductRequestVO();
        wareItem.setWarehouseId(1);
        wareItem.setGoodCode("t");
        wareItem.setCategoryId(1);
        request.setWarehouseProductRequestList(Arrays.asList(wareItem));

        List<BizWarehouseEntryDTO> resultList = (List<BizWarehouseEntryDTO>) getPersonalizedInvoice.invoke(inWareHouseGWService, userParam, orderSearch, request);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));

        // return accessible
        getPersonalizedInvoice.setAccessible(accessible);
    }

    @Test
    public void getInAndOutWarehouseInfoForOrder() {
        Integer orderId = 95998;
        String orderNo = "DC201911215296401";

        OrderRequestVO request = new OrderRequestVO();
        request.setOrderId(orderId);
        request.setOrderNo(orderNo);

        Mockito.when(outWarehouseGWService.getImmediatelyOutWarehouseApplicationBean(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new OutWarehouseApplicationBean());
        Mockito.when(outWarehouseGWService.getImmediatelyOutWarehouseApplicationDetail(Mockito.any(), Mockito.any())).thenReturn(new OutWarehouseApplicationDetailVO());

        // get orderbean
        Mockito.when(bizWarehouseRoomServiceClient.getWarehouseByDepartmentIds(Mockito.any())).thenReturn(New.list());
        Mockito.when(userClient.getNotNullUserDetailById(Mockito.any())).thenReturn(new UserBaseInfoDTO());
        Mockito.when(userClient.getDepartmentInfo(Mockito.any())).thenReturn(new DepartmentDTO());
        Mockito.when(userClient.getNotNullUserDetailById(Mockito.any())).thenReturn(new UserBaseInfoDTO());

        DepartmentDTO deptInfo = new DepartmentDTO();
        deptInfo.setName("deptParent");
        Mockito.when(departmentRpcClient.getDepartmentParentInfo(Mockito.any())).thenReturn(deptInfo);

        InAndOutWarehouseApplicationDetailVO res = inWareHouseGWService.getInAndOutWarehouseInfoForOrder(request);
        Assert.assertNotNull(res);
    }

    @Test
    public void getBatchPrepareSubmitInfoByOrderId() {
        MyMockUtils.setRPCContextRJSession("test", 8, 165);
        OrderMasterDO o1 = new OrderMasterDO();
        o1.setId(1);
        o1.setFuserid(8);
        o1.setFbuydepartmentid(1);
        o1.setForderamounttotal(BigDecimal.TEN);
        o1.setSpecies(Byte.valueOf("0"));
        Mockito.when(orderMasterMapper.findByIdIn(Mockito.any())).thenReturn(Collections.singletonList(o1));
        Mockito.when(userDepartmentRoleRpcServiceClient.findUserHasAccess(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        BizWarehouseRoomDTO b1 = new BizWarehouseRoomDTO();
        Mockito.when(bizWarehouseRoomServiceClient.getWarehouseByDepartmentIds(Mockito.any())).thenReturn(Collections.singletonList(b1));
        OrderDetailDO d1 = new OrderDetailDO();
        d1.setFmasterid(1);
        d1.setFbidamount(BigDecimal.TEN);
        d1.setFquantity(BigDecimal.ONE);
        d1.setFcancelquantity(BigDecimal.ZERO);
        Mockito.when(orderDetailMapper.findAllByFmasteridIn(Mockito.any())).thenReturn(Collections.singletonList(d1));
        InvoiceDTO i1 = new InvoiceDTO();
        Mockito.when(invoiceClient.findInvoiceList(Mockito.any(), Mockito.anyInt())).thenReturn(Collections.singletonList(i1));
        ApplyRefWarehouseDTO a1 = new ApplyRefWarehouseDTO();
        a1.setWarehouseId(1);
        Mockito.when(applyRefWarehouseServiceClient.getWarehouseIdByApplicationId(Mockito.any())).thenReturn(a1);

        OrderBasicParamDTO request = new OrderBasicParamDTO();
        request.setOrderIdList(Arrays.asList(1, 2));
        inWareHouseGWService.getBatchPrepareSubmitInfoByOrderId(request);
    }
}